import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class EmailVerificationService {
  apiName = 'Default';
  

  getConfirmIdentityEmail = (token: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'GET',
      responseType: 'text',
      url: '/api/app/emailVerification/confirmIdentityEmail',
      params: { token },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
