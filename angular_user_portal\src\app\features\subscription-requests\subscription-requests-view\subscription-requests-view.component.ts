import { DatePipe, Ng<PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { Component, computed, inject, input, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { <PERSON>Button, MatMiniFabButton } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Router, RouterLink } from '@angular/router';
import { RequestService } from '@proxy/mobile/requests';
import {
  BusinessAccountSubscriptionRequestDetailsDto,
  BusinessAccountSubscriptionRequestService,
} from '@proxy/mobile/requests/account-subscription-requests/business-account-subscription-requests';
import { PersonalAccountSubscriptionRequestService } from '@proxy/mobile/requests/account-subscription-requests/personal-account-subscription-requests';
import { SmsBundleService } from '@proxy/mobile/sms-bundles';
import { SmsBundleDto } from '@proxy/mobile/sms-bundles/dtos';
import { AutoAnimateDirective } from '@shared/directives/autoAnimate.directive';
import { HexToColorPipe } from '@shared/pipes/hex-to-color.pipe';
import { LanguagePipe, LanguageService } from '@ttwr-framework/ngx-main-visuals';
import { filter, map, of, switchMap } from 'rxjs';
import { openPriceOfferDialog } from '../../../shared/components/price-offer-dialog/price-offer-dialog.component';
import { ConfigStateService } from '@abp/ng.core';

@Component({
  selector: 'app-subscription-requests-view',
  standalone: true,
  templateUrl: `./subscription-requests-view.component.html`,
  imports: [
    RouterLink,
    MatButton,
    LanguagePipe,
    HexToColorPipe,
    NgStyle,
    DatePipe,
    AutoAnimateDirective,
    MatIcon,
    MatMiniFabButton,
    NgClass,
    MatMenuModule,
  ],
})
export class SubscriptionRequestsViewComponent {
  open = signal<boolean>(false);
  private dialog = inject(MatDialog);
  configStateService = inject(ConfigStateService);

  smsPundel = signal<SmsBundleDto>({ messagesCount: 0, price: 0 });
  private personalAccountSubscriptionRequestService = inject(
    PersonalAccountSubscriptionRequestService
  );
  private businessAccountSubscriptionRequestService = inject(
    BusinessAccountSubscriptionRequestService
  );
  private smsBundleService = inject(SmsBundleService);
  private router = inject(Router);
  private languageService = inject(LanguageService);
  private requestService = inject(RequestService);

  id = input.required<string>();
  type = input.required<string>();

  mex = toObservable(
    computed(() => {
      return { id: this.id(), type: this.type() };
    })
  );

  request = toSignal<BusinessAccountSubscriptionRequestDetailsDto>(
    this.mex.pipe(
      switchMap(val => {
        let obs =
          this.type() == 'PersonalAccountSubscription'
            ? this.personalAccountSubscriptionRequestService.get(this.id())
            : this.businessAccountSubscriptionRequestService.get(this.id());

        return obs;
      }),
      switchMap(request => {
        if (request.smsBundleId) {
          return this.getSmsPundle(request.smsBundleId).pipe(map(() => request));
        } else {
          return of(request);
        }
      })
    )
  );

  ngOnInit(): void {}

  payment() {
    const obj = {
      requestId: this.id(),
      language: this.languageService.selectedLanguage(),
      savedCards: true,
      callBackUrl: '',
    };
    let obs =
      this.type() == 'PersonalAccountSubscription'
        ? this.personalAccountSubscriptionRequestService
            .createPayment(obj)
            .pipe(
              filter(
                () =>
                  this.configStateService.getAll().setting.values[
                    'GoTrack.TrackAccountRequest.PersonalRequestFatoraPayEnabled'
                  ] == 'True'
              )
            )
        : this.businessAccountSubscriptionRequestService
            .createPayment(obj)
            .pipe(
              filter(
                () =>
                  this.configStateService.getAll().setting.values[
                    'GoTrack.TrackAccountRequest.BusinessRequestFatoraPayEnabled'
                  ] == 'True'
              )
            );

    obs
      .pipe(
        map(link => {
          window.open(link, '_blank');
        })
      )
      .subscribe();
  }

  getSmsPundle(id: string) {
    return this.smsBundleService.get(id).pipe(
      map(response => {
        this.smsPundel.set(response);
      })
    );
  }

  openPriceOffer() {
    openPriceOfferDialog(this.dialog, { id: this.request().id });
  }

  cancle() {
    this.requestService.cancel(this.id()).subscribe(() => {
      this.router.navigate(['/subscription-requests']);
    });
  }
}
