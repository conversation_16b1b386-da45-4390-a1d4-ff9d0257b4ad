import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { IdentityUserDto, IdentityUserService } from '@abp/ng.identity/proxy';
import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { of } from 'rxjs';
import { user } from '../users.model';

@Component({
  selector: 'app-users-create-dialog',
  standalone: true,
  imports: [LocalizationModule, MatDialogContent, MatDialogTitle, TtwrFormComponent],
  templateUrl: './users-update-dialog.component.html',
  styleUrl: './users-update-dialog.component.scss',
})
export class UsersUpdateDialogComponent {
  private identityUser = inject(IdentityUserService);
  private localization = inject(LocalizationService);
  private ref = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private user = inject<IdentityUserDto>(MAT_DIALOG_DATA);

  private loading = inject(LOADING);

  protected config = user().form({
    submitAction: {
      onSubmit: value => {
        this.loading.set(true);
        this.identityUser
          .update(this.user.id!, value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.loading.set(false);
              this.ref.close(true);
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    fields: {
      userName: {
        validators: [requiredValidator],
      },
      password: {
        textInputType: 'password',
      },
      email: {
        validators: [
          requiredValidator,
          {
            name: 'email',
            validator: Validators.email,
            message: this.localization.instant('AbpValidation::ThisFieldIsNotAValidEmailAddress.'),
          },
        ],
      },
    },
    actions: [
      {
        label: 'Cancel',
        delegateFunc: () => {
          this.ref.close();
        },
      },
    ],
    viewFunc: () => of(this.user),
  });
}
