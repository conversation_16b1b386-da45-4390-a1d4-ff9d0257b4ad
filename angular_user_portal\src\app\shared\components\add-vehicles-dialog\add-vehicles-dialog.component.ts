import { Component, inject, model } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';

import { toSignal } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatLabel, MatOption, MatSelect } from '@angular/material/select';
import { VehicleService } from '@proxy/mobile/vehicles';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';

@Component({
  selector: 'app-add-vehicles-dialog',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    LanguagePipe,
    MatSelect,
    MatOption,
    MatFormField,
    MatLabel,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: `./add-vehicles-dialog.component.html`,
})
export class AddVehiclesDialogComponent {
  dialogRef = inject(MatDialogRef<AddVehiclesDialogComponent>);
  data = inject<string>(MAT_DIALOG_DATA);
  private vehicleService = inject(VehicleService);
  vehicleId = model<string | null>();
  availableVehicles$ = toSignal(
    this.vehicleService.getList({ skipCount: 0, maxResultCount: 50 }).pipe(
      map(response => {
        return response.items;
      })
    )
  );

  closeDialog() {
    this.dialogRef.close();
  }
  save() {
    this.dialogRef.close(this.vehicleId());
  }
}
export const openAddVehiclesDialog = (dialog: MatDialog, data: any) => {
  return dialog
    .open(AddVehiclesDialogComponent, {
      data: data,
    })
    .afterClosed();
};
