import type { VehicleDistanceAverageAndMaxSpeedReportDto, VehicleDistanceAverageAndMaxSpeedReportInputDto } from './dto/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class VehicleReportService {
  apiName = 'Default';
  

  getVehicleDistanceAverageAndMaxSpeedReport = (requestDto: VehicleDistanceAverageAndMaxSpeedReportInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VehicleDistanceAverageAndMaxSpeedReportDto>({
      method: 'GET',
      url: '/api/app/vehicleReport/vehicleDistanceAverageAndMaxSpeedReport',
      params: { vehicleId: requestDto.vehicleId, fromDate: requestDto.fromDate, toDate: requestDto.toDate, ignoreSpeedUnder: requestDto.ignoreSpeedUnder },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
