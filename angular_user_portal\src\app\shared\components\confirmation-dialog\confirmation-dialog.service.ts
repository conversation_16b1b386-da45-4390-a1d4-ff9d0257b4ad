import { Injectable, inject } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import {
  ConfirmationDialogComponent,
  ConfirmationDialogData,
} from './confirmation-dialog.component';

@Injectable({
  providedIn: 'root',
})
export class ConfirmationDialogService {
  private dialog = inject(MatDialog);

  /**
   * Opens a confirmation dialog and returns an observable with the result
   * @param data Configuration for the dialog
   * @param config Optional MatDialog configuration
   * @returns Observable<boolean> - true if confirmed, false if cancelled
   */
  open(
    data?: Partial<ConfirmationDialogData>,
    config?: Partial<MatDialogConfig>
  ): Observable<boolean> {
    const d = data.payConfirm
      ? {
          confirmText: 'UserPortal:ok',
          cancelText: 'UserPortal:Cancel',
          confirmButtonColor: 'primary',
          payConfirm: true,
          title: 'UserPortal:confirm',
        }
      : {
          ...data,
        };
    const dialogConfig: MatDialogConfig = {
      width: '400px',
      maxWidth: '90vw',
      disableClose: false,
      autoFocus: true,
      restoreFocus: true,
      ...config,
      data: d || {},
    };

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, dialogConfig);

    return dialogRef.afterClosed();
  }
}
