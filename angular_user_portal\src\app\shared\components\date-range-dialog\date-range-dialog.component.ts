import { Component, inject } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import {
  fields,
  model,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';

// Custom validator for 24-hour restriction
export function last24HoursValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }

    const selectedDate = new Date(control.value);
    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    if (selectedDate < twentyFourHoursAgo) {
      return {
        last24Hours: {
          message: 'Date must be within the last 24 hours',
          actualDate: selectedDate,
          minDate: twentyFourHoursAgo,
        },
      };
    }

    if (selectedDate > now) {
      return {
        futureDate: {
          message: 'Date cannot be in the future',
          actualDate: selectedDate,
          maxDate: now,
        },
      };
    }

    return null;
  };
}

// Custom validator for date range (from date should be before to date)
export function dateRangeValidator(): ValidatorFn {
  const fromDateField = 'fromDate';
  const toDateField = 'toDate';
  return (formGroup: AbstractControl): ValidationErrors | null => {
    let fromDate = formGroup.get(fromDateField)?.value;
    let toDate = formGroup.get(toDateField)?.value;
    if (formGroup && formGroup.parent) {
      fromDate = formGroup.parent.get(fromDateField)?.value;
      toDate = formGroup.parent.get(toDateField)?.value;
    }

    if (!fromDate || !toDate) {
      return null; // Don't validate if either date is missing
    }

    const from = new Date(fromDate);
    const to = new Date(toDate);

    if (from > to) {
      return {
        dateRange: {
          message: 'From date must be before to date',
          fromDate: from,
          toDate: to,
        },
      };
    }

    return null;
  };
}

export interface DateRangeDialogData {
  title?: string;
  fromDate?: Date;
  toDate?: Date;
  fromLabel?: string;
  toLabel?: string;
}

export interface DateRangeResult {
  fromDate: Date;
  toDate: Date;
}
@Component({
  selector: 'app-date-range-dialog',
  standalone: true,
  imports: [TtwrFormComponent],
  template: `
    <div class="m-4">
      <ttwr-form [config]="config" />
    </div>
  `,
})
export class DateRangeDialogComponent {
  dialogRef = inject(MatDialogRef<DateRangeDialogComponent>);
  data = inject<DateRangeDialogData>(MAT_DIALOG_DATA);

  config = model({
    fromDate: fields.datetime(),
    toDate: fields.datetime(),
  })
    .select({
      fromDate: true,
      toDate: true,
    })
    .form({
      fields: {
        fromDate: {
          label: this.data?.fromLabel || 'From Date',
          validators: [
            requiredValidator,
            {
              name: 'last24Hours',
              message: 'Date must be within the last 24 hours',
              validator: last24HoursValidator(),
            },
            {
              name: 'dateRange',
              message: 'From date must be before to date',
              validator: dateRangeValidator(),
            },
          ],
        },
        toDate: {
          label: this.data?.toLabel || 'To Date',
          validators: [
            requiredValidator,
            {
              name: 'last24Hours',
              message: 'Date must be within the last 24 hours',
              validator: last24HoursValidator(),
            },
            {
              name: 'dateRange',
              message: 'To date must be after from date',
              validator: dateRangeValidator(),
            },
          ],
        },
      },
      submitAction: {
        matButtonType: 'flat',
        onSubmit: (value: any) => {
          const result: DateRangeResult = {
            fromDate: new Date(value.fromDate),
            toDate: new Date(value.toDate),
          };
          this.dialogRef.close(result);
        },
      },
      actions: [
        {
          matButtonType: 'raised',
          label: 'Cancel',
          delegateFunc: () => this.dialogRef.close(),
        },
      ],
    });
}

export const openDateRangeDialog = (dialog: MatDialog, dialogData?: DateRangeDialogData) => {
  return dialog
    .open(DateRangeDialogComponent, {
      data: { ...dialogData },
    })
    .afterClosed();
};
