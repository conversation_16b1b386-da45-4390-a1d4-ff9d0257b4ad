import { Component, inject, signal } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { Router, RouterModule } from '@angular/router';
import { ObservationService } from '@proxy/mobile/observations';
import { VehicleGroupDto, VehicleGroupService } from '@proxy/mobile/vehicle-groups';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';

@Component({
  selector: 'app-identified-observers-create',
  standalone: true,
  templateUrl: './identified-observers-create-update.component.html',
  imports: [
    MatCardModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    RouterModule,
    LanguagePipe,
    MatRadioButton,
    MatRadioGroup,
    MatChipsModule,
  ],
})
export class IdentifiedObserversCreateUpdateComponent {
  withGroups = signal<boolean>(false);
  data = inject(MAT_DIALOG_DATA);
  dialogRef = inject(MatDialogRef<IdentifiedObserversCreateUpdateComponent>);

  availableVehicles$ = signal(new Map());
  availableGroups$ = signal(new Map());
  private fb = inject(FormBuilder);
  private observationService = inject(ObservationService);
  private vehicleService = inject(VehicleService);
  private vehicleGroupService = inject(VehicleGroupService);
  router = inject(Router);
  groupBased = signal(false);

  observerForm = this.fb.group({
    vehicleIds: new FormControl([]),
    vehicleGroupIds: new FormControl([]),
    name: new FormControl('', Validators.required),
    phoneNumber: new FormControl('', Validators.required),
  });

  ngOnInit(): void {
    if (this.data) {
      this.observerForm.setValue({
        phoneNumber: this.data.phoneNumber,
        name: this.data.name,
        vehicleIds: [],
        vehicleGroupIds: [],
      });
    }
    this.getVehicles();
    this.getGroups();
  }

  getVehicles() {
    this.vehicleService
      .getList({ skipCount: 0, maxResultCount: 50 })
      .pipe(
        map(response => {
          const map = new Map<string, VehicleDto>();
          response.items.map((vehicle: VehicleDto & { name: string }) => {
            vehicle.name = `${vehicle.licensePlateSerial} - ${vehicle.licensePlateSubClass}`;
            map.set(vehicle.id, vehicle);
          });
          this.availableVehicles$.set(map);
        })
      )
      .subscribe();
  }
  getGroups() {
    this.vehicleGroupService
      .getList({ skipCount: 0, maxResultCount: 50 })
      .pipe(
        map(response => {
          const map = new Map<string, VehicleGroupDto>();
          response.items.map((Group: VehicleGroupDto) => {
            map.set(Group.id, Group);
          });
          this.availableGroups$.set(map);
        })
      )
      .subscribe();
  }
  toggleSelectAll(control: string, map: Map<string, any>): void {
    const con = this.observerForm.get(control).value.length < map.size;
    const value = con ? Array.from(map.keys()) : [];
    this.observerForm.get(control)?.setValue(value);
  }

  saveObserver(): void {
    let observerData;

    if (!!this.data) {
      observerData = {
        userTrackAccountAssociationId: this.data.id,
        name: this.observerForm.value.name,
        phoneNumber: this.observerForm.value.phoneNumber,
      };
    } else {
      observerData = {
        name: this.observerForm.value.name,
        phoneNumber: this.observerForm.value.phoneNumber,
        vehicleIds: this.observerForm.value.vehicleIds || [],
        vehicleGroupIds: this.groupBased() ? this.observerForm.value.vehicleGroupIds : [],
      };
    }

    if (!!this.data) {
      this.observationService.updateObserver(observerData).subscribe(() => {
        this.dialogRef.close(true);
      });
    } else {
      this.observationService.createObserver(observerData).subscribe(() => {
        this.dialogRef.close(true);
      });
    }
  }
  removeChips(id: string, control: string): void {
    const selected = this.observerForm.get(control)?.value.filter((id_: string) => id_ !== id);
    this.observerForm.get(control)?.setValue(selected);
  }

  cancel() {
    this.dialogRef.close();
  }
}
