import type { CoordinateDto, CreateCoordinateDto } from '../../coordinates/dtos/models';
import type { CreateStopPointDto, StopPointDto } from '../../stop-points/dtos/models';
import type { FullAuditedEntityDto } from '@abp/ng.core';

export interface CreateRouteDto {
  name?: string;
  line: CreateCoordinateDto[];
  hexColor?: string;
  stopPoints: CreateStopPointDto[];
}

export interface RouteDto extends FullAuditedEntityDto<string> {
  name?: string;
  line: CoordinateDto[];
  color?: string;
  startPoint: CoordinateDto;
  endPoint: CoordinateDto;
  trackAccountId?: string;
}

export interface RouteViewModelDto extends FullAuditedEntityDto<string> {
  name?: string;
  line: CoordinateDto[];
  color?: string;
  startPoint: CoordinateDto;
  endPoint: CoordinateDto;
  trackAccountId?: string;
  stopPoints: StopPointDto[];
}
