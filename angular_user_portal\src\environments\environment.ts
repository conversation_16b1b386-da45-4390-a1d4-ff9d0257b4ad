import { Environment } from '@abp/ng.core';

const baseUrl = 'http://localhost:4200';

export const environment = {
  production: true,
  application: {
    baseUrl,
    name: 'GoTrack',
    logoUrl: '',
  },
  oAuthConfig: {
    issuer: 'https://gotrack-mobile.dev05.tatweer.sy/',
    redirectUri: baseUrl,
    clientId: 'GoTrack_App',
    responseType: 'code',
    scope: 'offline_access GoTrack',
    requireHttps: true,
  },
  apis: {
    default: {
      url: 'https://gotrack-mobile.dev05.tatweer.sy',
      rootNamespace: 'GoTrack',
    },
  },
  firebase: {
    projectId: 'gotrack-d2f3a',
    appId: '1:1020214318355:web:12674b8a499ae38189f6e6',
    storageBucket: 'gotrack-d2f3a.firebasestorage.app',
    apiKey: 'AIzaSyCTn3sGGhphSsnqPhGt0dXXS9JMrZVcqQQ',
    authDomain: 'gotrack-d2f3a.firebaseapp.com',
    messagingSenderId: '1020214318355',
  },
} as Environment;
