import { NgClass } from '@angular/common';
import { Component, inject, input, output } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MobileIdentityUserService as profile } from '@proxy/mobile/controllers/index';
import {
  GetMobileUserProfileDto,
  MobileIdentityUserService,
} from '@proxy/mobile/mobile-identity-users';
import { AlertService } from '@ttwr-framework/ngx-main-visuals';
import { skip } from 'rxjs';

@Component({
  selector: 'app-profile-image',
  standalone: true,
  imports: [MatIconModule, MatButtonModule, FormsModule, ReactiveFormsModule, NgClass],
  templateUrl: `./profile-image.component.html`,
})
export class ProfileImageComponent {
  profileImageIcon = '/assets/images/svg/user-placeholder.svg';
  cameraIcon = '/assets/images/svg/camera.svg';
  user: GetMobileUserProfileDto | any = {};
  userloaded = output<GetMobileUserProfileDto>();
  reload = input<boolean>();
  reload$ = toObservable(this.reload).pipe(skip(1));

  private userService = inject(MobileIdentityUserService);
  private alert = inject(AlertService);
  private profile = inject(profile);

  ngOnInit(): void {
    this.reload$.subscribe(() => {
      this.loadUserProfile();
    });
    this.loadUserProfile();
  }
  loadUserProfile(): void {
    this.userService.getProfile().subscribe(userData => {
      this.user = userData;
      this.userloaded.emit(userData);
    });
    this.userService.getProfilePicture().subscribe(userData => {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.profileImageIcon = e.target.result;
      };
      reader.readAsDataURL(userData);
    });
  }

  onProfileImageChange(event: any) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const base64Data = e.target.result.split(',')[1];
        const binaryData = atob(base64Data);
        const uint8Array = new Uint8Array(binaryData.length);
        for (let i = 0; i < binaryData.length; i++) {
          uint8Array[i] = binaryData.charCodeAt(i);
        }
        const blob = new Blob([uint8Array], { type: file.type });
        this.profileImageIcon = URL.createObjectURL(blob);
        const formData = new FormData();
        formData.append('profilePictureFile', blob);
        this.profile.setProfilePicture(this.userService, formData as any).subscribe(val => {
          this.alert.success('تم تحديث صورة الملف الشخصي بنجاح');
        });
      };
      reader.readAsDataURL(file);
    } else {
      this.alert.warning('لم يتم اختيار أي صورة');
    }
  }
}
