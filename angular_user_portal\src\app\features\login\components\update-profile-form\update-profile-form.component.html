<form [formGroup]="info()">
  <mat-form-field>
    <input
      matInput
      [placeholder]="'UserPortal:firstName' | i18n"
      formControlName="firstName"
      required
    />
    <mat-error>
      <app-validation [errors]="info().get('firstName')" />
    </mat-error>
  </mat-form-field>
  <mat-form-field>
    <input
      matInput
      [placeholder]="'UserPortal:lastName' | i18n"
      formControlName="lastName"
      required
    />
    <mat-error>
      <app-validation [errors]="info().get('lastName')" />
    </mat-error>
  </mat-form-field>
  <mat-form-field>
    <input matInput [placeholder]="'UserPortal:email' | i18n" formControlName="email" required />
    <mat-error>
      <app-validation [errors]="info().get('email')" />
    </mat-error>
  </mat-form-field>
  <div>
    <button
      mat-button
      mat-flat-button
      [disabled]="info().invalid"
      (click)="update_profile()"
      class="w-full"
    >
      {{ 'UserPortal:Next' | i18n }}
    </button>
  </div>
</form>
