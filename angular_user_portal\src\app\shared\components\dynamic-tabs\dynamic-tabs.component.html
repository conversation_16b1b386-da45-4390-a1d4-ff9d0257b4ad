<div class="flex justify-center items-center min-h-screen scale-90">
  <mat-card class="w-10/12 !bg-white p-6 shadow-lg">
    <p class="text-gray-700 text-center mb-6">
      {{ description() | i18n }}
    </p>

    <mat-tab-group (selectedTabChange)="onTabChange($event)">
      @for (tab of tabs(); track tab; let i = $index) {
      <mat-tab>
        <ng-template mat-tab-label>
          @if (tab.iconActive && tab.iconInactive) {
          <img
            [src]="activeTabIndex === i ? tab.iconActive : tab.iconInactive"
            width="24"
            height="24"
            alt="{{ tab.label }} Icon"
          />
          }
          <span class="ml-2">{{ tab.label | i18n }}</span>
        </ng-template>
      </mat-tab>
      }
    </mat-tab-group>

    <div class="content-container">
      @if (tabs()[activeTabIndex]?.component) {
      <ng-container *ngComponentOutlet="tabs()[activeTabIndex].component"></ng-container>
      } @else {
      <mat-label class="text-gray-500 text-center">{{ 'UserPortal:EmptyTap' | i18n }}</mat-label>
      }
    </div>
  </mat-card>
</div>
