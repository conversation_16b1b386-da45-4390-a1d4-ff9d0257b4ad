import type { AddVehicleGroupsToTripTemplateDto, AddVehiclesToTripTemplateDto, CreateTripTemplateDto, TripTemplateDto, TripTemplateViewModelDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TripTemplateService {
  apiName = 'Default';
  

  addVehicleGroupsToTripTemplate = (input: AddVehicleGroupsToTripTemplateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/tripTemplate/vehicleGroupsToTripTemplate',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  addVehiclesToTripTemplate = (input: AddVehiclesToTripTemplateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/tripTemplate/vehiclesToTripTemplate',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  create = (createTripTemplateDto: CreateTripTemplateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, TripTemplateDto>({
      method: 'POST',
      url: '/api/app/tripTemplate',
      body: createTripTemplateDto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/tripTemplate/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, TripTemplateViewModelDto>({
      method: 'GET',
      url: `/api/app/tripTemplate/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<TripTemplateDto>>({
      method: 'GET',
      url: '/api/app/tripTemplate',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  removeVehicleFromTripTemplate = (tripTemplateId: string, vehicleId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/tripTemplate/vehicleFromTripTemplate',
      params: { tripTemplateId, vehicleId },
    },
    { apiName: this.apiName,...config });
  

  removeVehicleGroupFromTripTemplate = (tripTemplateId: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/tripTemplate/vehicleGroupFromTripTemplate',
      params: { tripTemplateId, vehicleGroupId },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
