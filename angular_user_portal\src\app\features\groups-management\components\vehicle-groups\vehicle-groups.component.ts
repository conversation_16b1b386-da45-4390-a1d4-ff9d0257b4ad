import { CommonModule } from '@angular/common';
import { Component, DestroyRef, inject, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router, RouterLink } from '@angular/router';
import { VehicleGroupDto, VehicleGroupService } from '@proxy/mobile/vehicle-groups';
import { VehicleService } from '@proxy/mobile/vehicles';
import { openAddVehiclesDialog } from '@shared/components/add-vehicles-dialog/add-vehicles-dialog.component';
import { openRelatedItemsDialog } from '@shared/components/related-items-dialog/related-items-dialog.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, firstValueFrom, switchMap } from 'rxjs';

@Component({
  standalone: true,
  selector: 'app-vehicle-groups',
  templateUrl: './vehicle-groups.component.html',
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    LanguagePipe,
    MatMenuModule,
    MatProgressSpinnerModule,
    RouterLink,
  ],
})
export class VehicleGroupsComponent {
  private router = inject(Router);
  private dialog = inject(MatDialog);
  private vehicleGroupService = inject(VehicleGroupService);
  private vehicleService = inject(VehicleService);
  private destroyRef = inject(DestroyRef);

  // Signal for groups
  groups$ = signal<VehicleGroupDto[]>([]);

  selectedGroupId: string | null = null;

  ngOnInit(): void {
    this.loadGroups();
  }

  loadGroups() {
    this.vehicleGroupService.getList({ skipCount: 0, maxResultCount: 50 }).subscribe(response => {
      this.groups$.set(response.items || []);
    });
  }

  navigateToAddGroup() {
    this.router.navigate(['']);
  }

  openVehiclesCreationDialog(group: VehicleGroupDto) {
    const dialogRef = openAddVehiclesDialog(this.dialog, {});
    dialogRef
      .pipe(
        filter(v => v != null),
        switchMap(data => {
          return this.vehicleService.addAddToVehicleGroupByIdAndVehicleGroupId(data, group.id);
        })
      )
      .subscribe(() => {
        this.loadGroups();
      });
  }
  async openAssignedVehicles(group: VehicleGroupDto) {
    const items = await firstValueFrom(
      this.vehicleService.getList({ maxResultCount: 999, skipCount: 0, vehicleGroupId: group.id })
    );
    const dialogRef = openRelatedItemsDialog({
      dialog: this.dialog,
      items: items.items,
      type: 'VEHICLE',
      hideAction: true,
    });
  }
}
