import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

export interface ConfirmationDialogData {
  title?: string;
  message?: string;
  payConfirm?: boolean;
  confirmText?: string;
  cancelText?: string;
  confirmButtonColor?: 'primary' | 'accent' | 'warn';
}

@Component({
  selector: 'app-confirmation-dialog',
  standalone: true,
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss'],
  imports: [MatDialogModule, MatButtonModule, LanguagePipe, MatIcon],
})
export class ConfirmationDialogComponent {
  private dialogRef = inject(MatDialogRef<ConfirmationDialogComponent>);
  data = inject<ConfirmationDialogData>(MAT_DIALOG_DATA);

  constructor() {
    this.data = {
      title: this.data?.title || 'UserPortal:Confirmation',
      message: this.data?.message || 'UserPortal:AreYouSure',
      confirmText: this.data?.confirmText || 'UserPortal:Confirm',
      cancelText: this.data?.cancelText || 'UserPortal:Cancel',
      confirmButtonColor: this.data?.confirmButtonColor || 'primary',
      ...this.data,
    };
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
