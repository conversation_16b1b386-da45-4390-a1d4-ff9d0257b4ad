<div class="flex justify-between items-center mt-4">
  <h3 class="text-lg font-semibold">{{ 'GoTrack:ObserversList' | i18n }}</h3>
  <button mat-button color="warn" (click)="openCreateObserverDialog()">
    + {{ 'GoTrack:AddObserver' | i18n }}
  </button>
</div>

<mat-accordion class="mt-4 scale-90">
  @for(observer of observers$(); track observer; let i = $index) {
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title
        class="flex !flex-grow-[unset] !flex-basis-[unset]"
        (click)="$event.stopPropagation(); $event.preventDefault()"
      >
        <button mat-icon-button [matMenuTriggerFor]="menu" class="ml-auto">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="addVehicles(observer)"
          >
            <mat-icon>add_circle</mat-icon>
            <span>{{ 'UserPortal:AddNewVehicles' | i18n }}</span>
          </button>
          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="addGroups(observer)"
          >
            <mat-icon>add_circle</mat-icon>
            <span>{{ 'UserPortal:AddNewGroups' | i18n }}</span>
          </button>
          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="openCreateObserverDialog(observer)"
          >
            <mat-icon>edit</mat-icon>
            <span>{{ 'UserPortal:edit' | i18n }}</span>
          </button>
          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="viewAssignedVehicles(observer)"
          >
            <mat-icon>directions_car</mat-icon>
            <span>{{ 'UserPortal:ViewAssignedVehicles' | i18n }}</span>
          </button>

          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="deleteObserver(observer)"
          >
            <mat-icon>delete</mat-icon>
            <span>{{ 'UserPortal:DeleteObserver' | i18n }}</span>
          </button>
        </mat-menu>

        {{ observer.name }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="flex flex-col p-3 gap-3">
      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray">{{ 'UserPortal:phone' | i18n }}:</span>
        <span class="text-main_gray">{{ observer.phoneNumber }}</span>
      </div>
    </div>
  </mat-expansion-panel>
  }
</mat-accordion>
