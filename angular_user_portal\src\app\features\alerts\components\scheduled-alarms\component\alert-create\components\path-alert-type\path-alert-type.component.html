<div [formGroup]="alertForm()">
  <mat-label class="pr-2">{{ 'UserPortal:SelectRoutes' | i18n }}</mat-label>
  <mat-form-field appearance="outline" class="w-full">
    <mat-select
      formControlName="routeIds"
      multiple
      placeholder="{{ 'UserPortal:SelectRoutes' | i18n }}"
    >
      <mat-select-trigger>
        <div>
          @for (routeIds of alertForm().get('routeIds')?.value; track routeIds) {
          <mat-chip [removable]="true" (removed)="removeGeoZone(routeIds)">
            {{ paths().get(routeIds).name }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
          }
        </div>
      </mat-select-trigger>
      @for (path of paths().values(); track $index) {
      <mat-option [value]="path.id">
        {{ path.name }}
      </mat-option>
      }
    </mat-select>
  </mat-form-field>
</div>
