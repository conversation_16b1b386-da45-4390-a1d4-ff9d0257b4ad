.mat-mdc-form-field {
  width: 100%;
  --mdc-outlined-text-field-container-shape: 10px;
  --mat-form-field-container-height: 48px;
  border-radius: 8px;
}

.mat-mdc-text-field-wrapper {
  background-color: white !important;
  border-radius: 8px;
}

.leaflet-popup-tip {
  background-color: transparent;
}

.mdc-text-field {
  background-color: white;
  box-shadow: 0 0 4px 3px #00000038;
  border-radius: 10px !important;
}

.mat-mdc-checkbox .mat-internal-form-field {
  width: 100%;
  .mdc-label {
    display: flex;
    flex-grow: 1;
  }
  .mdc-checkbox__background {
    background-color: white !important;
    border-color: white !important;
  }
  .mdc-checkbox__checkmark {
    color: inherit;
  }
}
.mdc-checkbox:hover .mdc-checkbox__native-control:not(:checked) ~ .mdc-checkbox__background,
.mdc-checkbox:hover .mdc-checkbox__native-control:not(:indeterminate) ~ .mdc-checkbox__background {
  background-color: white !important;
}
.cancleButton {
  background-color: theme('colors.main_dark_blue') !important;
}
.mat-mdc-label {
  font-size: 16px;
  font-weight: 500;
  color: #666;
}
.main-header {
  font-size: 20px;
  font-weight: bold;
  color: var(--sys-primary);
}
.mat-mdc-card.card-blue {
  background-color: rgba(175, 175, 175, 0.3);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
.cdk-overlay-pane:has(.mat-timepicker-content) {
  transform: scale(0.9) !important;
  transform-origin: top right;
  background-color: white;
}
mat-label {
  color: theme('colors.main_gray');
}
* {
  border-style: inherit;
}
