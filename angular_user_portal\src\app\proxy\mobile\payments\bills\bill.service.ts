import type { BillDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class BillService {
  apiName = 'Default';
  

  getBill = (requestId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'GET',
      url: `/api/app/bill/bill/${requestId}`,
    },
    { apiName: this.apiName,...config });
  

  getFakeBill = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'GET',
      url: '/api/app/bill/fakeBill',
    },
    { apiName: this.apiName,...config });
  

  getFakeBillByRequestId = (requestId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'GET',
      url: `/api/app/bill/fakeBill/${requestId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
