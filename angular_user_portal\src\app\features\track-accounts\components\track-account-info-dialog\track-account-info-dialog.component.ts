import { DatePipe } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TrackAccountService } from '@proxy/mobile/track-accounts';
import { TrackAccountDetailsDto } from '@proxy/mobile/track-accounts/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-track-account-info-dialog',
  standalone: true,
  templateUrl: `./track-account-info-dialog.component.html`,
  imports: [MatButton, LanguagePipe, DatePipe],
})
export class TrackAccountInfoDialogComponent {
  dialogRef = inject(MatDialogRef<TrackAccountInfoDialogComponent>);
  data: { id: string } = inject(MAT_DIALOG_DATA);
  trackAccountService = inject(TrackAccountService);
  trackAccount = signal<TrackAccountDetailsDto | null>(null);

  ngOnInit(): void {
    this.getInfo();
  }

  getInfo() {
    this.trackAccountService.get(this.data.id).subscribe(res => {
      this.trackAccount.set(res);
    });
  }

  closeDialog() {
    this.dialogRef.close();
  }
}
