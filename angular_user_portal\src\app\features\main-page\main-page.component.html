@if (features()["GoTrack.LiveMonitoring"]) {
<div class="relative overflow-clip h-full w-full">
  <div class="geoLocation row h-full">
    <div class="w-full h-full">
      <app-map [nodes]="nodes$()" [options]="options$()" [LayerChange]="LayerChange$()" />
    </div>
  </div>
  <div class="absolute top-2 right-4 gray z-[999]">
    <button class="!bg-white" mat-mini-fab (click)="displayDialog$.set(!displayDialog$())">
      <mat-icon>menu</mat-icon>
    </button>
  </div>
  <div class="absolute top-20 left-2 gray z-[999]">
    <button
      class="!bg-white"
      mat-mini-fab
      (click)="changeLayer()"
      [matTooltip]="'change view' | i18n"
    >
      @if (this.layer$()) {
      <mat-icon>map</mat-icon>
      } @else {
      <mat-icon>image</mat-icon>
      }
    </button>
  </div>
  <div class="absolute top-14 right-4 gray max-w-80 z-[999]">
    @if (displayDialog$()) {

    <div class="bg-white rounded-lg p-4 pb-0">
      <mat-form-field class="w-full">
        <mat-select matInput [placeholder]="'vehicle' | i18n" [formControl]="selectedVehicle">
          <mat-option [value]="null"> - </mat-option>
          @for (item of vehicles$(); track $index) {
          <mat-option [value]="item">{{
            item.licensePlateSubClass + ' ' + item.licensePlateSerial
          }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>
    @if (selectedVehicle$()) {
    <div class="bg-white rounded-lg p-4 mt-4 text-xs">
      <div class="grid grid-cols-3 gap-2">
        @for (item of times|keyvalue; track $index) {
        <button
          class="p-2 rounded-md bg-[#E1F9FC] text-black [&.sel]:text-white [&.sel]:bg-main_sky_blue2"
          [ngClass]="{ sel: time$() == item.value }"
          (click)="time$.set(item.value)"
        >
          {{ item.key | i18n }}
        </button>
        }
      </div>
      <div class="divide-y-2 text-main_gray">
        <div class="py-2">
          {{ 'UserPortal:State' | i18n }} :
          {{ selectedVehcileInfo$().isActive ? 'active' : 'Not Active' }}
        </div>
        <div class="py-2">
          {{ 'UserPortal:average_speed' | i18n }} : {{ selectedVehcileInfo$().averageSpeed
          }}{{ 'Km/h' | i18n }}
        </div>
        <div class="py-2">
          {{ 'UserPortal:distance' | i18n }} : {{ selectedVehcileInfo$().distance
          }}{{ 'Km' | i18n }}
        </div>
        <div class="py-2">
          {{ 'UserPortal:maxSpeed' | i18n }} : {{ selectedVehcileInfo$().maxSpeed
          }}{{ 'Km/h' | i18n }}
        </div>
      </div>
      <div class="grid grid-cols-2 gap-4 justify-items-center">
        <button
          mat-mini-fab
          class="!bg-white"
          [routerLink]="['history/', selectedVehicle$().id]"
          [matTooltip]="'UserPortal:show_history' | i18n"
        >
          <mat-icon>track_changes</mat-icon>
        </button>
        <button
          mat-mini-fab
          class="!bg-white"
          [routerLink]="['track/', selectedVehicle$().id]"
          [matTooltip]="'UserPortal:track' | i18n"
        >
          <mat-icon>timeline</mat-icon>
        </button>
      </div>
    </div>
    } }
  </div>
</div>

} @else{
<div class="w-full h-full flex justify-center items-center">
  <div class="bg-white p-4 rounded-2xl shadow-lg border-gray-300 border-2 border-solid">
    {{ 'you don`t have access to this part of the application' | i18n }}
  </div>
</div>
}
