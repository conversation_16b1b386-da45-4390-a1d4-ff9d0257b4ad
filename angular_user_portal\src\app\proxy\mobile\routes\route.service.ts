import type { CreateRouteDto, RouteDto, RouteViewModelDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { CreateStopPointDto, StopPointDto } from '../stop-points/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class RouteService {
  apiName = 'Default';
  

  addStopPointToRoute = (id: string, createStopPointDto: CreateStopPointDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RouteDto>({
      method: 'POST',
      url: `/api/app/route/${id}/stopPointToRoute`,
      body: createStopPointDto,
    },
    { apiName: this.apiName,...config });
  

  create = (createRouteDto: CreateRouteDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RouteDto>({
      method: 'POST',
      url: '/api/app/route',
      body: createRouteDto,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/route/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RouteViewModelDto>({
      method: 'GET',
      url: `/api/app/route/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<RouteDto>>({
      method: 'GET',
      url: '/api/app/route',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getStopPointsOnRoute = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<StopPointDto>>({
      method: 'GET',
      url: `/api/app/route/${id}/stopPointsOnRoute`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  removeStopPointFromRoute = (id: string, stopPointId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/route/${id}/stopPointFromRoute/${stopPointId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
