@if (items().length) {
<ol>
  <!-- <li>
      <a routerLink="/">
        <mat-icon aria-hidden="true">home</mat-icon>
      </a>
    </li> -->
  @for (item of items(); track $index; let last = $last) {
  <li [class.active]="last" aria-current="page">
    <ng-container
      *ngTemplateOutlet="item.path ? linkTemplate : textTemplate; context: { $implicit: item }"
    ></ng-container>
  </li>
  }
</ol>
}

<ng-template #linkTemplate let-item>
  <a [routerLink]="item.path"> {{ item.name | abpLocalization }}</a>
</ng-template>

<ng-template #textTemplate let-item>
  {{ item.name | abpLocalization }}
</ng-template>
