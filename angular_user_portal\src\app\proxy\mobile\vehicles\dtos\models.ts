import type { AuditedEntityDto, FullAuditedEntityDto } from '@abp/ng.core';

export interface VehicleDto extends AuditedEntityDto<string> {
  colorHex?: string;
  licensePlateSerial?: string;
  licensePlateSubClass?: string;
  consumptionRate: number;
}

export interface VehicleViewModelWithAuditingDto extends FullAuditedEntityDto<string> {
  colorHex?: string;
  licensePlateSerial?: string;
  licensePlateSubClass?: string;
  consumptionRate: number;
}
