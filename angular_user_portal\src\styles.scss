@use '@angular/material' as mat;
@use '@ttwr-framework/ngx-main-visuals' as ttwr;
@use '@dhutaryan/ngx-mat-timepicker' as mat-timepicker;

@import 'theme';
@import url(./styles.tw.scss);
@import url(./styles-overrides.scss);

@include mat.core();
@include ttwr.m3-support();
@include ttwr.ttwr-basic-table();

$font-family: 'Noto Ku<PERSON> Arabic';

$app-theme: mat.define-theme(
  (
    color: (
      primary: mat.$azure-palette,
      tertiary: mat.$blue-palette,
    ),
    typography: (
      plain-family: $font-family,
      brand-family: $font-family,
    ),
  )
);

html {
  @include mat.all-component-themes($app-theme);
  @include mat-timepicker.timepicker-theme($app-theme);
  @include mat.color-variants-backwards-compatibility($app-theme);
}

:root {
  --lpx-logo: url('/assets/images/logo/LogoColored.png');
  --lpx-logo-icon: url('/assets/images/logo/LogoColored.png');
  --lpx-logo-icon-white: url('/assets/images/logo/Logo_white.svg');

  --ttwr-overlay-color: #00000073;

  --ttwr-ckeditor-background: var(--mat-app-background-color);
  --ttwr-ckeditor-color: var(--mat-app-text-color);
  --ttwr-ckeditor-placeholder-color: var(--mdc-outlined-text-field-label-text-color);
  --ttwr-ckeditor-tooltip-background: var(--mdc-plain-tooltip-container-color);
  --ttwr-ckeditor-tooltip-color: var(--mdc-plain-tooltip-supporting-text-color);

  --ttwr-basic-table-normal: var(--sys-secondary-container);
  --ttwr-basic-table-normal-text: var(--sys-on-secondary-container);
  --ttwr-basic-table-light: var(--sys-surface-container);
  --ttwr-basic-table-light-text: var(--sys-on-surface);

  --mat-sidenav-content-background-color: transparent;
  --mat-timepicker-content-background-color: transparent;

  --mat-stepper-container-color: #ffffff00;
  --mat-stepper-header-edit-state-icon-background-color: var(--sys-primary);
  --mat-stepper-line-color: #ffffff00;
  --mat-stepper-header-hover-state-layer-color: #ffffff00;
  --mat-stepper-header-done-state-icon-background-color: var(--sys-primary);
  --mat-stepper-header-icon-background-color: #e0e0e0;
  --mdc-filled-button-container-color: var(--mdc-filled-button-container-color);
  --mdc-checkbox-unselected-icon-color: #ffffff00;
  --mdc-checkbox-unselected-focus-state-layer-color: #ffffff00;
  --mdc-checkbox-unselected-hover-icon-color: #ffffff00;
  --mdc-checkbox-unselected-focus-icon-color: #ffffff00;
  --sys-tertiary: #ffffff;
  --sys-on-tertiary: #ffffff00;

  --mdc-outlined-text-field-container-shape: 10px;
  --mat-form-field-container-vertical-padding: 10px;
  --mat-form-field-container-height: 44px;
  --mdc-filled-button-container-shape: 10px;
  --mdc-filled-button-container-color: #7164e4;
}

* {
  letter-spacing: normal !important;
}

body {
  margin: 0;
  font-family: 'Noto Kufi Arabic', sans-serif;
  background-image: url('assets/images/background.png');
}

.mat-datepicker-content .mat-calendar {
  height: 366px !important;
}

.ttwr-download-link {
  color: var(--sys-primary);
}

a {
  text-decoration: none;
  color: inherit;
}

.link {
  color: var(--sys-primary);

  &:hover {
    text-decoration: underline;
  }
}

::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #707070;
  border-radius: 100px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.map-main {
  .leaflet-popup-tip-container {
    top: -20px;
    rotate: 180deg;
  }
}

.car {
  filter: brightness(70%) sepia(100%) hue-rotate(-53deg) saturate(418%);
}

.custom-dialog .mat-dialog-container {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  color: theme('colors.gray.800');
}

.leaflet-pane.leaflet-shadow-pane {
  display: none;
}
.mat-mdc-form-field {
  margin-bottom: 0.5rem;
}
