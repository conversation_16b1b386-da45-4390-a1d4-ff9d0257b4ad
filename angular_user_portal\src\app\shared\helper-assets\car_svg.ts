import { getRandomHexColor } from '@shared/functions';

export const car_svg = (color?: string) => `
            <svg style="color:${
              color ?? getRandomHexColor()
            }; width:100%;height:100%" xmlns="http://www.w3.org/2000/svg" width="41.94" height="43.624" viewBox="0 0 41.94 43.624">
              <g id="noun_Car_386509" transform="translate(-26.198 -61.628) rotate(40)">
                <path id="Path_154" data-name="Path 154" d="M39.5,13.223V7.073A6.091,6.091,0,0,0,33.424,1H28.11a6.091,6.091,0,0,0-6.073,6.073v6.15A3.8,3.8,0,0,0,19,16.943h3.037V32.126A6.091,6.091,0,0,0,28.11,38.2h5.314A6.091,6.091,0,0,0,39.5,32.126V16.943h3.037A3.8,3.8,0,0,0,39.5,13.223ZM30.767,33.265c-4.555,0-6.833-1.518-6.833-1.518l1.518-4.555a35.84,35.84,0,0,0,5.314.38,35.84,35.84,0,0,0,5.314-.38L37.6,31.747S35.322,33.265,30.767,33.265Zm5.314-16.322a37.39,37.39,0,0,0-10.628,0l-1.518-4.555s2.278-1.518,6.833-1.518S37.6,12.388,37.6,12.388Z" transform="translate(59 14)" fill="currentColor"/>
              </g>
            </svg>`;
