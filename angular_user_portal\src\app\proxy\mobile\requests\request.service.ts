import type { GetListRequestsInputDto, GetRequestsOfTrackAccountInputDto, RequestDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class RequestService {
  apiName = 'Default';
  

  cancel = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/request/${id}/cancel`,
    },
    { apiName: this.apiName,...config });
  

  getList = (inputDto: GetListRequestsInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<RequestDto>>({
      method: 'GET',
      url: '/api/app/request',
      params: { types: inputDto.types, status: inputDto.status, skipCount: inputDto.skipCount, maxResultCount: inputDto.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListOfTrackAccountIdByInput = (input: GetRequestsOfTrackAccountInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<RequestDto>>({
      method: 'GET',
      url: '/api/app/request/ofTrackAccountId',
      params: { types: input.types, status: input.status, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
