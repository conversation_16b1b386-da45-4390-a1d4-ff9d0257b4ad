import type { AuditedEntityDto, PagedResultRequestDto } from '@abp/ng.core';

export interface GetListRequestsInputDto extends PagedResultRequestDto {
  types: string[];
  status: string[];
}

export interface GetRequestsOfTrackAccountInputDto extends PagedResultRequestDto {
  types: string[];
  status: string[];
}

export interface RequestDto extends AuditedEntityDto<string> {
  type?: string;
  status?: string;
  rejectReason?: string;
}

export interface RequestNoteDto {
  note?: string;
  creationTime?: string;
}
