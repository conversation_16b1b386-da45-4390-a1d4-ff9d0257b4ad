:root {
  --sys-background: #fcf8ff;
  --sys-error: #ba1a1a;
  --sys-error-container: #ffdad6;
  --sys-inverse-on-surface: #f3effa;
  --sys-inverse-primary: #c6bfff;
  --sys-inverse-surface: #312f38;
  --sys-on-background: #1c1b22;
  --sys-on-error: #ffffff;
  --sys-on-error-container: #410002;
  --sys-on-primary: #ffffff;
  --sys-on-primary-container: #160066;
  --sys-on-primary-fixed: #160066;
  --sys-on-primary-fixed-variant: #402eb1;
  --sys-on-secondary: #ffffff;
  --sys-on-secondary-container: #191442;
  --sys-on-secondary-fixed: #191442;
  --sys-on-secondary-fixed-variant: #454171;
  --sys-on-surface: #1c1b22;
  --sys-on-surface-variant: #474553;
  --sys-on-tertiary: #ffffff;
  --sys-on-tertiary-container: #390034;
  --sys-on-tertiary-fixed: #390034;
  --sys-on-tertiary-fixed-variant: #7e1574;
  --sys-outline: #787585;
  --sys-outline-variant: #c8c4d6;
  --sys-primary: #584aca;
  --sys-primary-container: #e4dfff;
  --sys-primary-fixed: #e4dfff;
  --sys-primary-fixed-dim: #c6bfff;
  --sys-scrim: #000000;
  --sys-secondary: #5d598a;
  --sys-secondary-container: #e4dfff;
  --sys-secondary-fixed: #e4dfff;
  --sys-secondary-fixed-dim: #c6c0f9;
  --sys-shadow: #000000;
  --sys-surface: #fcf8ff;
  --sys-surface-bright: #fcf8ff;
  --sys-surface-container: #f0ecf7;
  --sys-surface-container-high: #ebe6f2;
  --sys-surface-container-highest: #e5e1ec;
  --sys-surface-container-low: #f6f2fd;
  --sys-surface-container-lowest: #ffffff;
  --sys-surface-dim: #dcd8e3;
  --sys-surface-tint: #584aca;
  --sys-surface-variant: #e5e0f2;
  --sys-tertiary: #9b328e;
  --sys-tertiary-container: #ffd7f2;
  --sys-tertiary-fixed: #ffd7f2;
  --sys-tertiary-fixed-dim: #ffaceb;

}
