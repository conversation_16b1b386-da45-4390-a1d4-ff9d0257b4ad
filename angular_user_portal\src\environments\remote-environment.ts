import type { Environment } from '@abp/ng.core';

interface RemoteEnvironment {
  baseURL: string;
  production: boolean;
  appName: string;
  logoUrl: string;
  issuer: string;
  clientId: string;
  responseType: string;
  scope: string;
  requireHttps: boolean;
  apiURL: string;
  rootNamespace: string;
}

export function mergeFunction(_: any, remoteEnv: RemoteEnvironment): Environment {
  return {
    production: remoteEnv.production,
    application: {
      baseUrl: remoteEnv.baseURL,
      name: remoteEnv.appName,
      logoUrl: remoteEnv.logoUrl,
    },
    oAuthConfig: {
      issuer: remoteEnv.issuer,
      redirectUri: remoteEnv.baseURL,
      clientId: remoteEnv.clientId,
      responseType: remoteEnv.responseType,
      scope: remoteEnv.scope,
      requireHttps: remoteEnv.requireHttps,
    },
    apis: {
      default: {
        url: remoteEnv.apiURL,
        rootNamespace: remoteEnv.rootNamespace,
      },
    },
  };
}
