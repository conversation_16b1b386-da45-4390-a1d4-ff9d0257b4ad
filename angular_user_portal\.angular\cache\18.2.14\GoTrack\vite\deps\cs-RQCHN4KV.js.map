{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/cs.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  if (i === Math.floor(i) && i >= 2 && i <= 4 && v === 0) return 3;\n  if (!(v === 0)) return 4;\n  return 5;\n}\nexport default [\"cs\", [[\"dop.\", \"odp.\"], u, u], u, [[\"N\", \"P\", \"Ú\", \"S\", \"Č\", \"P\", \"S\"], [\"ne\", \"po\", \"út\", \"st\", \"čt\", \"pá\", \"so\"], [\"ned<PERSON>le\", \"ponděl<PERSON>\", \"úterý\", \"středa\", \"čtvrtek\", \"pátek\", \"sobota\"], [\"ne\", \"po\", \"út\", \"st\", \"čt\", \"pá\", \"so\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"led\", \"úno\", \"bře\", \"dub\", \"kvě\", \"čvn\", \"čvc\", \"srp\", \"zář\", \"říj\", \"lis\", \"pro\"], [\"ledna\", \"února\", \"března\", \"dubna\", \"května\", \"června\", \"července\", \"srpna\", \"září\", \"října\", \"listopadu\", \"prosince\"]], [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"led\", \"úno\", \"bře\", \"dub\", \"kvě\", \"čvn\", \"čvc\", \"srp\", \"zář\", \"říj\", \"lis\", \"pro\"], [\"leden\", \"únor\", \"březen\", \"duben\", \"květen\", \"červen\", \"červenec\", \"srpen\", \"září\", \"říjen\", \"listopad\", \"prosinec\"]], [[\"př.n.l.\", \"n.l.\"], [\"př. n. l.\", \"n. l.\"], [\"před naším letopočtem\", \"našeho letopočtu\"]], 1, [6, 0], [\"dd.MM.yy\", \"d. M. y\", \"d. MMMM y\", \"EEEE d. MMMM y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"CZK\", \"Kč\", \"česká koruna\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CSK\": [\"Kčs\"],\n  \"CZK\": [\"Kč\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [\"JP¥\", \"¥\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"RUR\": [u, \"р.\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"VND\": [u, \"₫\"],\n  \"XEU\": [\"ECU\"],\n  \"XXX\": []\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,MAAI,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,EAAG,QAAO;AAC/D,MAAI,EAAE,MAAM,GAAI,QAAO;AACvB,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,WAAW,SAAS,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,SAAS,SAAS,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,QAAQ,SAAS,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,SAAS,QAAQ,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,QAAQ,SAAS,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,MAAM,GAAG,CAAC,aAAa,OAAO,GAAG,CAAC,yBAAyB,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,WAAW,aAAa,gBAAgB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,MAAM,gBAAgB;AAAA,EACppC,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC;AACV,GAAG,OAAO,MAAM;", "names": []}