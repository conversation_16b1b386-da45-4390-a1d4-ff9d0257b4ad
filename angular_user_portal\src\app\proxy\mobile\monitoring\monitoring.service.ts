import type { VehicleHistoryInputDto, WarpGtsHistoryDto } from './dtos/device-history/models';
import type { LiveLocationDto, LiveLocationInputDto } from './dtos/live-locations/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class MonitoringService {
  apiName = 'Default';
  

  getVehicleHistory = (inputDto: VehicleHistoryInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, WarpGtsHistoryDto[]>({
      method: 'GET',
      url: '/api/app/monitoring/vehicleHistory',
      params: { vehicleId: inputDto.vehicleId, fromDate: inputDto.fromDate, toDate: inputDto.toDate, onlySpeed: inputDto.onlySpeed },
    },
    { apiName: this.apiName,...config });
  

  postLiveLocation = (inputDto: LiveLocationInputDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, LiveLocationDto[]>({
      method: 'POST',
      url: '/api/app/monitoring/liveLocation',
      body: inputDto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
