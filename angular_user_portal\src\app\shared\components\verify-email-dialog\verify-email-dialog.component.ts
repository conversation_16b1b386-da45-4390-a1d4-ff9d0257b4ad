import { Component, inject, model, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  GetMobileUserProfileDto,
  MobileIdentityUserService,
} from '@proxy/mobile/mobile-identity-users';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-verify-email-dialog',
  standalone: true,
  imports: [
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
  ],
  templateUrl: './verify-email-dialog.component.html',
})
export class VerifyEmailDialogComponent {
  dialogRef = inject(MatDialogRef<VerifyEmailDialogComponent>);
  data = inject<GetMobileUserProfileDto>(MAT_DIALOG_DATA);
  mobileIdentityUserService = inject(MobileIdentityUserService);
  verify = signal(false);
  email = model<string>(this.data.email || '');

  closeDialog() {
    this.dialogRef.close();
  }

  submit() {
    if (!this.verify()) {
      this.verify.set(true);
    } else {
      this.mobileIdentityUserService.updateEmail({ email: this.email() }).subscribe(val => {
        this.dialogRef.close(true);
      });
    }
  }
}

export const openVerifyEmailDialog = (dialog: MatDialog, dialogData?: GetMobileUserProfileDto) => {
  return dialog
    .open(VerifyEmailDialogComponent, {
      data: { ...dialogData },
    })
    .afterClosed();
};
