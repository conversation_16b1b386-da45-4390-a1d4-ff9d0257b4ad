<div [formGroup]="alertForm()">
  <mat-label class="pr-2">{{ 'UserPortal:Name' | i18n }}</mat-label>
  <mat-form-field appearance="outline" class="w-full">
    <input matInput formControlName="name" placeholder="{{ 'UserPortal:Name' | i18n }}" />
  </mat-form-field>
  <div style="padding-bottom: 0.4rem">
    <mat-label class="pt-2 pr-2">{{ 'UserPortal:SelectTime' | i18n }}</mat-label>
  </div>
  <div class="flex gap-3">
    <mat-form-field appearance="outline" class="w-1/2">
      <input
        matInput
        [matTimepicker]="startTimePicker"
        formControlName="startTime"
        [placeholder]="'UserPortal:From' | i18n"
      />
      <mat-timepicker-toggle matSuffix [for]="startTimePicker"></mat-timepicker-toggle>
      <mat-timepicker
        class="tt"
        #startTimePicker
        (timeSet)="onTimeChange('startTime', $event)"
      ></mat-timepicker>
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-1/2">
      <input
        matInput
        [matTimepicker]="endTimePicker"
        formControlName="endTime"
        [placeholder]="'UserPortal:To' | i18n"
      />
      <mat-timepicker-toggle matSuffix [for]="endTimePicker"></mat-timepicker-toggle>
      <mat-timepicker #endTimePicker (timeSet)="onTimeChange('endTime', $event)"></mat-timepicker>
    </mat-form-field>
  </div>

  <div style="padding: 0.5rem 0">
    <mat-label class="pr-2">{{ 'UserPortal:RepeatOnDays' | i18n }}</mat-label>
  </div>
  <div class="flex flex-col items-center pt-4">
    <div class="flex gap-2">
      @for (day of weekDays(); track day) {
      <div class="flex flex-col items-center">
        <div
          class="flex justify-center items-center bg-gray-50 rounded-full border-2 transition-all cursor-pointer size-10 border-lighter_perple"
          [ngClass]="{ 'text-white !bg-lighter_perple': isDayActive(day) }"
          (click)="toggleDay(day)"
        >
          {{ day[0] + day[1] }}
        </div>
      </div>
      }
    </div>
  </div>
</div>
