
import { Component } from '@angular/core';

@Component({
  selector: 'app-about',
  standalone: true,
  imports: [], 
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss']
})
export class AboutComponent {
  description: string = "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربي حيث يمكنك أن تولد مثل هذا النص أو العديد من النصوص الأخرى إضافة إلى زيادة عدد الحروف التي يولدها التطبيق.";

  logoPath: string = "/assets/images/svg/go-track-about-logo.svg";
  socialIcons = [
    { name: "Facebook", src: "/assets/images/svg/facebook.svg", link: "https://facebook.com" },
    { name: "WhatsApp", src: "/assets/images/svg/whatsapp-about.svg", link: "https://wa.me" },
    { name: "Telegram", src: "/assets/images/svg/telegram.svg", link: "https://telegram.org" }
  ];

}
