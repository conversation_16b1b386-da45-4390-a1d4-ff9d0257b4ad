import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { TenantService } from '@abp/ng.tenant-management/proxy';
import { Component, DestroyRef, inject, OnDestroy } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  fields,
  LOADING,
  model,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-tenant-management-update-dialog',
  standalone: true,
  imports: [LocalizationModule, MatDialogContent, MatDialogTitle, TtwrFormComponent],
  templateUrl: './tenant-management-update-dialog.component.html',
  styleUrl: './tenant-management-update-dialog.component.scss',
})
export class TenantManagementUpdateDialogComponent implements OnDestroy {
  private tenant = inject<Tenant>(MAT_DIALOG_DATA);
  private ref = inject(MatDialogRef);
  private tenants = inject(TenantService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private localization = inject(LocalizationService);
  private loading = inject(LOADING);

  protected config = model({
    name: fields.text(),
  }).form({
    initialRequired: true,
    submitAction: {
      onSubmit: value => {
        this.loading.set(true);

        this.tenants
          .update(this.tenant.id, {
            name: value.name,
            concurrencyStamp: this.tenant.concurrencyStamp,
          })
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.loading.set(false);
              this.ref.close(true);
              this.alert.success(
                this.localization.instant('AbpSettingManagement::SuccessfullySaved')
              );
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    fields: {
      name: {
        defaultValue: this.tenant.name,
      },
    },
    actions: [
      {
        label: 'Cancel',
        delegateFunc: () => {
          this.ref.close();
        },
      },
    ],
  });

  ngOnDestroy() {
    this.loading.set(false);
  }
}

interface Tenant {
  id: string;
  name: string;
  concurrencyStamp: string;
}
