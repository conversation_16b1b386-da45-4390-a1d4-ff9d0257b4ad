<div>
  <mat-label class="text-start text-sm mb-4 pb-2">{{
    'UserPortal:NotificationDescription' | i18n
  }}</mat-label>

  <h4 class="mt-4 text-base font-medium text-gray-900 flex items-center justify-center gap-4">
    <a class="text-main_blue cursor-pointer" (click)="loadProfile()">{{ 'try again' | i18n }}</a>
    <div>
      {{ user().email ?? 'email' }}
    </div>
    <mat-icon [ngClass]="[!user().isVerified ? 'text-main_red' : 'text-main_green']">{{
      user().isVerified ? 'check_circle' : 'warning'
    }}</mat-icon>
  </h4>
  <div class="flex flex-col space-y-4">
    @for (method of notificationMethods(); track method) {
    <mat-checkbox
      class="rounded-md my-2 shadow-md text-white p-2 flex items-center justify-center"
      [checked]="methods().value.includes(method.value)"
      (change)="addMethod(method.value)"
      [ngStyle]="{ 'background-color': method.color }"
      labelPosition="before"
    >
      <div class="flex items-center justify-center w-full">
        <img src="{{ method.icon }}" class="mx-2 size-5" alt="" />
        <span class="text-center w-full">{{ method.name | i18n }}</span>
      </div>
    </mat-checkbox>
    }
  </div>
</div>
