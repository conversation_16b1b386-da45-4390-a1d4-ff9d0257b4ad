import { ConfigStateService } from '@abp/ng.core';
import { Component, DestroyRef, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckbox } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { PaymentService } from '@proxy/mobile/payments';
import { RequestDto, RequestService } from '@proxy/mobile/requests';
import { IncreaseUserCountRequestService } from '@proxy/mobile/requests/account-subscription-requests/increase-user-count-requests';
import { SmsBundleRenewalRequestService } from '@proxy/mobile/requests/account-subscription-requests/sms-bundle-renewal-requests';
import { AddVehiclesRequestService } from '@proxy/mobile/requests/add-vehicles-requests';
import { RenewSubscriptionRequestService } from '@proxy/mobile/requests/renew-subscription-requests';
import { ConfirmationDialogService } from '@shared/components/confirmation-dialog';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, map, of, switchMap } from 'rxjs';

@Component({
  selector: 'app-track-account-request-dialog',
  standalone: true,
  templateUrl: './track-account-request-dialog.component.html',
  imports: [MatButtonModule, LanguagePipe, MatCardModule, MatIcon, MatCheckbox],
})
export class TrackAccountrequestDialogComponent {
  dialogRef = inject(MatDialogRef<TrackAccountrequestDialogComponent>);
  requestService = inject(RequestService);
  pymentService = inject(PaymentService);
  destroyRef = inject(DestroyRef);
  confirmationDialogService = inject(ConfirmationDialogService);
  data: { id: string } = inject(MAT_DIALOG_DATA);

  ides = signal<Set<string>>(new Set());
  private dialog = inject(MatDialog);

  ngOnInit(): void {}
  icons = {
    SmsBundleRenewalRequest: 'sms.svg',
    RenewSubscription: 'تجديد اشتراك.svg',
    AddVehiclesRequest: 'زيادة المركبات.svg',
  };

  configStateService = inject(ConfigStateService);
  smsBundleRenewalRequestService = inject(SmsBundleRenewalRequestService);
  renewSubscriptionRequestService = inject(RenewSubscriptionRequestService);
  addVehiclesRequestService = inject(AddVehiclesRequestService);

  servicesSelector = {
    SmsBundleRenewalRequest: {
      service: this.smsBundleRenewalRequestService,
      allowPayment: 'GoTrack.TrackAccountRequest.SmsBundleRenewalRequestFatoraPayEnabled',
    },
    RenewSubscription: {
      service: this.renewSubscriptionRequestService,
      allowPayment: 'GoTrack.TrackAccountRequest.RenewTrackAccountSubscriptionFatoraPayEnabled',
    },
    AddVehiclesRequest: {
      service: this.addVehiclesRequestService,
      allowPayment: 'GoTrack.TrackAccountRequest.AddVehiclesRequestFatoraPayEnabled',
    },
  } as const;

  request = toSignal(
    this.requestService
      .getListOfTrackAccountIdByInput({
        maxResultCount: 999,
        skipCount: 0,
        status: [],
        types: [],
      })
      .pipe(
        map(val => {
          return val.items;
        })
      )
  );

  pay(order: RequestDto) {
    const dialogref = openPriceOfferDialog(this.dialog, { id: order.id, pay: true });
    dialogref
      .afterClosed()
      .pipe(
        filter(v => !!v),
        switchMap(id => {
          return this.confirmationDialogService
            .open({
              payConfirm: true,
            })
            .pipe(
              filter(
                () =>
                  this.configStateService.getAll().setting.values[
                    this.servicesSelector[order.type].allowPayment
                  ] == 'True'
              ),
              switchMap(val => {
                if (val) {
                  return this.servicesSelector[order.type].service
                    .craetePayment({
                      requestId: id,
                      language: 'en',
                      savedCards: true,
                      callBackUrl: location.href + 'track-accounts',
                    })
                    .pipe(
                      map((link: string) => {
                        window.open(link, '_blank');
                      })
                    );
                } else {
                  return of(null);
                }
              })
            );
        })
      )
      .subscribe();
  }

  toggle(order: RequestDto) {
    if (this.ides().has(order.id)) {
      this.ides().delete(order.id);
    } else {
      if (
        this.configStateService.getAll().setting.values[
          this.servicesSelector[order.type].allowPayment
        ] == 'True'
      )
        this.ides().add(order.id);
    }
  }
  paySelected() {
    return this.pymentService
      .payMultipleRequests({
        requestIds: [...this.ides()],
        language: 'en',
        savedCards: true,
        callBackUrl: location.href + 'track-accounts',
      })
      .pipe(
        map((link: string) => {
          window.open(link, '_blank');
        })
      )
      .subscribe();
  }
  remove(order: RequestDto) {
    this.confirmationDialogService
      .open({
        title: 'UserPortal:DeleteConfirmation',
        message: 'UserPortal:ConfirmMissage',
        confirmText: 'UserPortal:Delete',
        cancelText: 'UserPortal:Cancel',
        confirmButtonColor: 'warn',
      })
      .subscribe(status => {
        if (status === true) {
          this.requestService.cancel(order.id).subscribe(() => {
            this.dialogRef.close();
          });
        }
      });
  }
}
