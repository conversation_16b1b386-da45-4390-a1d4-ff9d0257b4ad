{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/hu.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"hu\", [[\"de.\", \"du.\"], u, u], u, [[\"V\", \"H\", \"K\", \"Sz\", \"Cs\", \"P\", \"Sz\"], [\"V\", \"H\", \"K\", \"<PERSON>ze\", \"Cs\", \"P\", \"Szo\"], [\"vasárnap\", \"hétfő\", \"kedd\", \"szerda\", \"csütörtök\", \"péntek\", \"szombat\"], [\"V\", \"H\", \"K\", \"Sze\", \"<PERSON>s\", \"P\", \"<PERSON>zo\"]], u, [[\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"A\", \"<PERSON>z\", \"O\", \"N\", \"D\"], [\"jan.\", \"febr.\", \"márc.\", \"ápr.\", \"máj.\", \"jún.\", \"júl.\", \"aug.\", \"szept.\", \"okt.\", \"nov.\", \"dec.\"], [\"január\", \"február\", \"m<PERSON>rcius\", \"április\", \"május\", \"június\", \"július\", \"augusztus\", \"szeptember\", \"október\", \"november\", \"december\"]], u, [[\"ie.\", \"isz.\"], [\"i. e.\", \"i. sz.\"], [\"Krisztus előtt\", \"időszámításunk szerint\"]], 1, [6, 0], [\"y. MM. dd.\", \"y. MMM d.\", \"y. MMMM d.\", \"y. MMMM d., EEEE\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \" \", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0%\", \"#,##0.00 ¤\", \"#E0\"], \"HUF\", \"Ft\", \"magyar forint\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"EUR\": [u, \"€\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"HUF\": [\"Ft\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"],\n  \"XCD\": [u, \"$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI;AACV,MAAI,MAAM,EAAG,QAAO;AACpB,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,IAAI,GAAG,CAAC,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,YAAY,SAAS,QAAQ,UAAU,aAAa,UAAU,SAAS,GAAG,CAAC,KAAK,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,SAAS,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM,GAAG,CAAC,UAAU,WAAW,WAAW,WAAW,SAAS,UAAU,UAAU,aAAa,cAAc,WAAW,YAAY,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,MAAM,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,kBAAkB,wBAAwB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,aAAa,cAAc,kBAAkB,GAAG,CAAC,QAAQ,WAAW,aAAa,cAAc,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,MAAM,iBAAiB;AAAA,EACj6B,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,IAAI;AAAA,EACZ,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAChB,GAAG,OAAO,MAAM;", "names": []}