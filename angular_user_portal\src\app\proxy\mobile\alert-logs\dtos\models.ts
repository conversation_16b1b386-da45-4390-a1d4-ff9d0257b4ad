import type { AlertType } from '../../../alert-definitions/alert-type.enum';
import type { PagedResultRequestDto } from '@abp/ng.core';

export interface AlertLogDto {
  vehicleId?: string;
  deviceId?: string;
  alertType: AlertType;
  alertTriggerId?: string;
  startedAt?: string;
  endedAt?: string;
}

export interface AlertLogPagedResultRequestDto extends PagedResultRequestDto {
  vehicleId?: string;
  deviceId?: string;
}
