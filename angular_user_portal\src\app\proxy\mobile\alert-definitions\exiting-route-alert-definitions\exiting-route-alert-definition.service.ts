import type { CreateExitingRouteAlertDefinitionDto, ExitingRouteAlertDefinitionDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { RouteDto } from '../../routes/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class ExitingRouteAlertDefinitionService {
  apiName = 'Default';
  

  create = (input: CreateExitingRouteAlertDefinitionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/exitingRouteAlertDefinition',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ExitingRouteAlertDefinitionDto>({
      method: 'GET',
      url: `/api/app/exitingRouteAlertDefinition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ExitingRouteAlertDefinitionDto>>({
      method: 'GET',
      url: '/api/app/exitingRouteAlertDefinition',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getRoutes = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<RouteDto>>({
      method: 'GET',
      url: `/api/app/exitingRouteAlertDefinition/${id}/routes`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
