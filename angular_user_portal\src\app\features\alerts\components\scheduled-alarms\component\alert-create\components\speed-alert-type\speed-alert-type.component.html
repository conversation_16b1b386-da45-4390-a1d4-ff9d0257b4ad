<div class="flex flex-col items-center p-4">
  <mat-card class="gauge-card">
    <ngx-gauge
      [value]="speedControl().value"
      [min]="0"
      [size]="300"
      [max]="360"
      [cap]="'round'"
      [thick]="20"
      [type]="'semi'"
      [append]="'UserPortal:SpeedUnit' | i18n"
      [foregroundColor]="gaugeColor$()"
      [backgroundColor]="'#e0e0e0'"
      [thresholds]="thresholds"
      [markers]="markerConfig"
      [animate]="true"
    >
    </ngx-gauge>
    <div class="flex justify-evenly w-full p-2">
      <button mat-flat-button class="!bg-main_green !text-white" (click)="increaseSpeed()">
        <mat-icon>arrow_upward</mat-icon> {{ 'UserPortal:IncreaseSpeed' | i18n }}
      </button>
      <button mat-flat-button class="!bg-main_blood_red !text-white" (click)="decreaseSpeed()">
        <mat-icon>arrow_downward</mat-icon> {{ 'UserPortal:DecreaseSpeed' | i18n }}
      </button>
    </div>
  </mat-card>
</div>
