import { Component, inject, Inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { UserTrackAccountAssociationDto } from '@proxy/mobile/user-track-account-associations/dtos';

export interface ObserverListDialogData {
  observers: Array<{
    id: string;
    name: string;
    phoneNumber: string;
    isActive: boolean;
  }>;
  action: (observer: any) => void;
  actionIcon?: string;
  actionLabel?: string;
}

@Component({
  selector: 'app-observer-list-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    LanguagePipe,
  ],
  templateUrl: './observer-list-dialog.component.html',
})
export class ObserverListDialogComponent {
  dialogRef = inject(MatDialogRef<ObserverListDialogComponent>);
  data: UserTrackAccountAssociationDto[] = inject(MAT_DIALOG_DATA);
  data$ = signal<UserTrackAccountAssociationDto[]>([]);
  deletedObservers$ = signal<string[]>([]);

  ngOnInit(): void {
    this.data$.set(this.data);
  }
  handleAction(id: string) {
    this.deletedObservers$.update(val => {
      if (val.includes(id)) {
        val = val.filter(e => {
          return e !== id;
        });
      } else {
        val.push(id);
      }
      return val;
    });
  }

  close() {
    this.dialogRef.close(this.deletedObservers$());
  }
}
