import * as Addresses from './addresses';
import * as AlertDefinitions from './alert-definitions';
import * as AlertLogs from './alert-logs';
import * as Controllers from './controllers';
import * as Coordinates from './coordinates';
import * as Emails from './emails';
import * as Fcmdevices from './fcmdevices';
import * as GeoNodes from './geo-nodes';
import * as GeoZones from './geo-zones';
import * as MobileIdentityUsers from './mobile-identity-users';
import * as Monitoring from './monitoring';
import * as Notifications from './notifications';
import * as Observations from './observations';
import * as Otps from './otps';
import * as Payments from './payments';
import * as PrivacyPolicies from './privacy-policies';
import * as Reports from './reports';
import * as Requests from './requests';
import * as Routes from './routes';
import * as SmsBundles from './sms-bundles';
import * as StopPoints from './stop-points';
import * as SubscriptionPlans from './subscription-plans';
import * as TrackAccounts from './track-accounts';
import * as TripTemplates from './trip-templates';
import * as UserTrackAccountAssociations from './user-track-account-associations';
import * as VehicleGroups from './vehicle-groups';
import * as Vehicles from './vehicles';
export { Addresses, AlertDefinitions, AlertLogs, Controllers, Coordinates, Emails, Fcmdevices, GeoNodes, GeoZones, MobileIdentityUsers, Monitoring, Notifications, Observations, Otps, Payments, PrivacyPolicies, Reports, Requests, Routes, SmsBundles, StopPoints, SubscriptionPlans, TrackAccounts, TripTemplates, UserTrackAccountAssociations, VehicleGroups, Vehicles };
