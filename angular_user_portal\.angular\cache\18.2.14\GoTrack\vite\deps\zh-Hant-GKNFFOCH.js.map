{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/zh-Hant.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  return 5;\n}\nexport default [\"zh-Hant\", [[\"上午\", \"下午\"], u, u], u, [[\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"], [\"週日\", \"週一\", \"週二\", \"週三\", \"週四\", \"週五\", \"週六\"], [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"], [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"]], u, [[\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"], [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\", \"10月\", \"11月\", \"12月\"], u], u, [[\"西元前\", \"西元\"], u, u], 0, [6, 0], [\"y/M/d\", \"y年M月d日\", u, \"y年M月d日 EEEE\"], [\"Bh:mm\", \"Bh:mm:ss\", \"Bh:mm:ss [z]\", \"Bh:mm:ss [zzzz]\"], [\"{1} {0}\", u, u, u], [\".\", \",\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"非數值\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤#,##0.00\", \"#E0\"], \"TWD\", \"$\", \"新台幣\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"KRW\": [\"￦\", \"₩\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"RUR\": [u, \"р.\"],\n  \"TWD\": [\"$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"XXX\": []\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI;AACV,SAAO;AACT;AACA,IAAO,kBAAQ,CAAC,WAAW,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,UAAU,GAAG,aAAa,GAAG,CAAC,SAAS,YAAY,gBAAgB,iBAAiB,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,aAAa,KAAK,GAAG,OAAO,KAAK,OAAO;AAAA,EAC/oB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,KAAK,GAAG;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC;AACV,GAAG,OAAO,MAAM;", "names": []}