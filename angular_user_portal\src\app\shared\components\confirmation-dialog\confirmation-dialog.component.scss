.confirmation-dialog {
  min-width: 400px;
  max-width: 500px;
  padding: 16px;
}

.mat-mdc-dialog-title::before {
  display: none;
}
.dialog-title {
  padding: 10px 10px 0 10px;
  margin: 0;

  .title-content {
    display: flex;
    align-items: center;
    gap: 12px;

    h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 500;
      color: #333;
    }
  }
}

.dialog-content {
  padding: 8px 10px;

  .dialog-message {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: #666;
  }
}

.dialog-actions {
  padding: 8px 10px 10px 10px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;

  .confirm-button {
    min-width: 80px;
  }
}

// Responsive design
@media (max-width: 600px) {
  .confirmation-dialog {
    min-width: 280px;
    max-width: 90vw;
  }

  .dialog-title {
    padding: 16px 16px 0 16px;

    .title-content {
      h2 {
        font-size: 1.125rem;
      }
    }
  }

  .dialog-content {
    padding: 16px;
  }

  .dialog-actions {
    padding: 8px 16px 16px 16px;
    flex-direction: column-reverse;

    button {
      width: 100%;
      margin: 4px 0;
    }
  }
}
