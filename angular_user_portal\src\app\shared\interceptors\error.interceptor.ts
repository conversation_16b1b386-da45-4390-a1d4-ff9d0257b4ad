import { HttpErrorResponse, HttpInterceptorFn, HttpEventType } from '@angular/common/http';
import { inject } from '@angular/core';
import { AlertService, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { catchError, tap, throwError } from 'rxjs';

export const errorHandlerInterceptor: HttpInterceptorFn = (req, next) => {
  const alert = inject(AlertService);
  const loading = inject(LOADING);

  return next(req).pipe(
    tap(event => {
      if (event.type === HttpEventType.Sent) {
        loading.set(true);
      }
      if (event.type === HttpEventType.Response) {
        loading.set(false);
      }
    }),
    catchError(err => {
      if (err instanceof HttpErrorResponse) {
        let abpError: { error: { message: string } } = err.error;

        if (typeof err.error === 'string') {
          abpError = JSON.parse(err.error);
        }

        loading.set(false);

        if (!err.url?.endsWith('account/my-profile') && abpError?.error) {
          alert.error(abpError.error.message, { duration: 5000 });
        }
      }

      return throwError(() => err);
    })
  );
};
