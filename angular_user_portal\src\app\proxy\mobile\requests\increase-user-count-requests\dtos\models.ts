import type { RequestDto } from '../../models';
import type { IncreaseUserCountRequestStage } from '../../../../requests/increase-user-count-requests/increase-user-count-request-stage.enum';

export interface CreateIncreaseUserCountRequestDto {
  userCount: number;
}

export interface IncreaseUserCountRequestDto extends RequestDto {
  ownerId?: string;
  ownerFirstName?: string;
  ownerLastName?: string;
  ownerEmail?: string;
  ownerPhoneNumber?: string;
  trackAccountSubscriptionId?: string;
  increaseUserCountRequestStage: IncreaseUserCountRequestStage;
  userCount: number;
  paymentUrl?: string;
}
