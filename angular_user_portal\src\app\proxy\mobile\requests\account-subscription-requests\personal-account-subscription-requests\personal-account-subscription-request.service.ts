import type { PersonalAccountSubscriptionRequestCreateDto, PersonalAccountSubscriptionRequestDetailsDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { CreateAccountRequestPaymentDto } from '../models';
import type { BillDto } from '../../../payments/bills/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class PersonalAccountSubscriptionRequestService {
  apiName = 'Default';
  

  create = (createDto: PersonalAccountSubscriptionRequestCreateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/personalAccountSubscriptionRequest',
      body: createDto,
    },
    { apiName: this.apiName,...config });
  

  createPayment = (input: CreateAccountRequestPaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/personalAccountSubscriptionRequest/payment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  createTempBill = (input: PersonalAccountSubscriptionRequestCreateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/personalAccountSubscriptionRequest/tempBill',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PersonalAccountSubscriptionRequestDetailsDto>({
      method: 'GET',
      url: `/api/app/personalAccountSubscriptionRequest/${id}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
