import type { AddressDto, AddressFormDto } from '../../../addresses/models';
import type { SubscriptionVehicleInfoCreateDto, SubscriptionVehicleInfoDto } from '../models';
import type { RequestDto, RequestNoteDto } from '../../models';

export interface BusinessAccountSubscriptionRequestCreateDto {
  companyName: string;
  address: AddressFormDto;
  accountName: string;
  subscriptionVehicleInfoCreateDtos: SubscriptionVehicleInfoCreateDto[];
  subscriptionPlanKey: string;
  userCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  promoCode?: string;
}

export interface BusinessAccountSubscriptionRequestCreateWithoutVehiclesDataDto {
  companyName: string;
  address: AddressFormDto;
  accountName: string;
  subscriptionPlanKey: string;
  userCount: number;
  vehicleCount: number;
  deviceCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  promoCode?: string;
}

export interface BusinessAccountSubscriptionRequestDetailsDto extends RequestDto {
  ownerId?: string;
  companyName?: string;
  accountName?: string;
  companyAddress: AddressDto;
  trackerInstallationLocation?: string;
  trackVehicles: SubscriptionVehicleInfoDto[];
  requestNotes: RequestNoteDto[];
  subscriptionPlanKey?: string;
  subscriptionPlanLocalizedName?: string;
  userCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  discountRate?: number;
  stage?: string;
  price: number;
  paymentUrl?: string;
}
