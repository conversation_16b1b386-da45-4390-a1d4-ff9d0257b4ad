<div class="bg-[url('/assets/images/background-grad.png')] bg-cover min-h-svh h-full">
  <div
    class="grid flex-1 grid-cols-1 grid-rows-1 justify-items-center content-center p-12 justify-center items-center h-full min-h-svh"
  >
    <div class="size-full bg-white rounded-3xl bg-opacity-90 relative">
      <div class="absolute top-4 end-4">
        <button mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>language</mat-icon>
        </button>
        <mat-menu #menu>
          <button mat-menu-item (click)="changeLang('ar')">العربية</button>
          <button mat-menu-item (click)="changeLang('en')">English</button>
        </mat-menu>
      </div>
      <div class="flex flex-col justify-center items-center h-full">
        <img src="assets/images/logo/LogoColored.png" class="w-32 mb-4 mt-4" alt="" />
        <div>
          <mat-stepper
            #stepper
            class="sm:w-96"
            [disableRipple]="true"
            [linear]="true"
            [labelPosition]="'bottom'"
          >
            <mat-step [stepControl]="phone">
              <ng-template matStepContent>
                <p class="text-main_gray my-4">
                  {{ 'UserPortal:creation_flow_entro' | i18n }}
                </p>

                <form [formGroup]="phone">
                  <mat-form-field>
                    <input
                      matInput
                      [placeholder]="'UserPortal:phone' | i18n"
                      formControlName="phone"
                      required
                    />
                    <mat-error>
                      <app-validation [errors]="phone.get('phone')" />
                    </mat-error>
                  </mat-form-field>
                  <div>
                    <button
                      mat-button
                      mat-flat-button
                      class="w-full"
                      [disabled]="phone.invalid"
                      (click)="sendOtp(stepper)"
                    >
                      {{ 'UserPortal:Next' | i18n }}
                    </button>
                  </div>
                </form>
              </ng-template>
            </mat-step>
            <mat-step [stepControl]="confirm">
              <ng-template matStepContent>
                <button
                  class="!absolute top-4 start-4"
                  mat-button
                  matStepperPrevious
                  (click)="confirm.reset()"
                >
                  {{ 'UserPortal:Back' | i18n }}
                </button>
                <p class="text-main_gray my-4">
                  {{ 'UserPortal:creation_phone_entro1' | i18n }}
                  {{ phone.value.phone }}
                  {{ 'UserPortal:creation_phone_entro2' | i18n }}
                </p>
                <form [formGroup]="confirm">
                  <mat-form-field>
                    <input
                      matInput
                      [placeholder]="'UserPortal:verification code' | i18n"
                      formControlName="confirm"
                      required
                    />
                    <mat-error>
                      <app-validation [errors]="confirm.get('confirm')" />
                    </mat-error>
                  </mat-form-field>
                  <p class="text-main_gray mb-2">
                    {{ "UserPortal:Didn't receive a code" | i18n }} ?

                    <a class="text-black" (click)="sendOtp()">
                      {{ 'UserPortal:Click here to resend Code' | i18n }}.</a
                    >
                  </p>
                  <div>
                    <button
                      mat-button
                      mat-flat-button
                      [disabled]="confirm.invalid"
                      (click)="sendCode(stepper)"
                      class="w-full"
                    >
                      {{ 'UserPortal:Next' | i18n }}
                    </button>
                  </div>
                </form>
              </ng-template>
            </mat-step>
            <mat-step [stepControl]="info">
              <ng-template matStepContent>
                <button
                  class="!absolute top-4 start-4"
                  mat-button
                  matStepperPrevious
                  (click)="info.reset()"
                >
                  {{ 'UserPortal:Back' | i18n }}
                </button>
                <app-update-profile-form [info]="info" />
              </ng-template>
            </mat-step>
          </mat-stepper>
        </div>
      </div>
    </div>
  </div>
</div>
