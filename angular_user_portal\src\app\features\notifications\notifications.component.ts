import { Component, computed, inject, Signal, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatButton } from '@angular/material/button';
import { NotificationService } from '@proxy/mobile/notifications';
import { NotificationDto } from '@proxy/mobile/notifications/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { combineLatest, map, switchMap } from 'rxjs';

@Component({
  selector: 'app-notifications',
  standalone: true,
  templateUrl: './notifications.component.html',
  imports: [LanguagePipe, MatButton],
})
export class NotificationsComponent {
  notificationService = inject(NotificationService);
  skipCount = signal(0);
  skipCount$ = toObservable(this.skipCount);

  loadMore() {
    this.skipCount.set(this.skipCount() + 1);
  }

  notifications = signal<NotificationDto[]>([]);

  ngOnInit(): void {
    this.skipCount$
      .pipe(
        switchMap(val => {
          return this.notificationService
            .getList(localStorage.getItem('trackAccountId'), {
              maxResultCount: 100,
              skipCount: val,
            })
            .pipe(
              map(v => {
                this.notifications.set([...this.notifications(), ...v.items]);
              })
            );
        })
      )
      .subscribe();
  }
}
