import { mapEnumToOptions } from '@abp/ng.core';

export enum VehicleLicensePlateSubClass {
  Damascus = 'Damascus',
  Aleppo = 'Aleppo',
  Raqqa = 'Raqqa',
  AsSuwayda = 'AsSuwayda',
  Daraa = 'Daraa',
  DeirezZor = '<PERSON><PERSON>z<PERSON>or',
  Hama = 'Hama',
  AlHasakah = 'AlHasakah',
  Homs = 'Homs',
  Idlib = 'Idlib',
  Latakia = 'Latakia',
  Quneitra = 'Quneitra',
  RifDimashq = 'RifDimashq',
  Tartus = 'Tartus',
}

export const vehicleLicensePlateSubClassOptions = mapEnumToOptions(VehicleLicensePlateSubClass);
