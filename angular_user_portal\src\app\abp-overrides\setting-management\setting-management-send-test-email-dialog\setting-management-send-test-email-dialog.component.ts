import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { EmailSettingsService } from '@abp/ng.setting-management/proxy';
import { TitleCasePipe } from '@angular/common';
import { Component, DestroyRef, inject, OnDestroy } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  fields,
  LOADING,
  model,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-setting-management-send-test-email-dialog',
  standalone: true,
  imports: [MatDialogTitle, LocalizationModule, MatDialogContent, TitleCasePipe, TtwrFormComponent],
  templateUrl: './setting-management-send-test-email-dialog.component.html',
  styleUrl: './setting-management-send-test-email-dialog.component.scss',
})
export class SettingManagementSendTestEmailDialogComponent implements OnDestroy {
  private defaultFromAddress = inject<string>(MAT_DIALOG_DATA);
  private localization = inject(LocalizationService);
  private ref = inject(MatDialogRef);
  private emailSettings = inject(EmailSettingsService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  protected config = model({
    senderEmailAddress: fields.text(),
    targetEmailAddress: fields.text(),
    subject: fields.text(),
    body: fields.text(),
  }).form({
    submitAction: {
      onSubmit: value => {
        if (this.loading()) return;

        this.loading.set(true);

        this.emailSettings
          .sendTestEmail(value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.ref.close();
              this.alert.success(
                this.localization.instant('AbpSettingManagement::SuccessfullySent')
              );
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    initialRequired: true,
    fields: {
      senderEmailAddress: {
        defaultValue: this.defaultFromAddress,
        validators: [
          requiredValidator,
          {
            name: 'email',
            validator: Validators.email,
            message: this.localization.instant('AbpValidation::ThisFieldIsNotAValidEmailAddress.'),
          },
        ],
      },
      targetEmailAddress: {
        validators: [
          requiredValidator,
          {
            name: 'email',
            validator: Validators.email,
            message: this.localization.instant('AbpValidation::ThisFieldIsNotAValidEmailAddress.'),
          },
        ],
      },
      body: {
        textInputType: 'textarea',
        validators: [],
      },
    },
    actions: [
      {
        label: 'Cancel',
        delegateFunc: () => this.ref.close(),
      },
    ],
  });

  ngOnDestroy() {
    this.loading.set(false);
  }
}
