import {
  RoutesService
} from "./chunk-3CXWNYMM.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-6D52GKB4.js";
import {
  APP_INITIALIZER,
  NgModule,
  makeEnvironmentProviders,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-QGPYGS5J.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.identity/fesm2022/abp-ng.identity-config.mjs
var IDENTITY_ROUTE_PROVIDERS = [{
  provide: APP_INITIALIZER,
  useFactory: configureRoutes,
  deps: [RoutesService],
  multi: true
}];
function configureRoutes(routesService) {
  return () => {
    routesService.add([{
      path: void 0,
      name: "AbpIdentity::Menu:IdentityManagement",
      parentName: "AbpUiNavigation::Menu:Administration",
      requiredPolicy: "AbpIdentity.Roles || AbpIdentity.Users",
      iconClass: "fa fa-id-card-o",
      layout: "application",
      order: 1
    }, {
      path: "/identity/roles",
      name: "AbpIdentity::Roles",
      parentName: "AbpIdentity::Menu:IdentityManagement",
      requiredPolicy: "AbpIdentity.Roles",
      order: 1
    }, {
      path: "/identity/users",
      name: "AbpIdentity::Users",
      parentName: "AbpIdentity::Menu:IdentityManagement",
      requiredPolicy: "AbpIdentity.Users",
      order: 2
    }]);
  };
}
function provideIdentityConfig() {
  return makeEnvironmentProviders([IDENTITY_ROUTE_PROVIDERS]);
}
var IdentityConfigModule = class _IdentityConfigModule {
  static forRoot() {
    return {
      ngModule: _IdentityConfigModule,
      providers: [provideIdentityConfig()]
    };
  }
  static {
    this.ɵfac = function IdentityConfigModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _IdentityConfigModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _IdentityConfigModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IdentityConfigModule, [{
    type: NgModule
  }], null, null);
})();
export {
  IDENTITY_ROUTE_PROVIDERS,
  IdentityConfigModule,
  configureRoutes,
  provideIdentityConfig
};
//# sourceMappingURL=@abp_ng__identity_config.js.map
