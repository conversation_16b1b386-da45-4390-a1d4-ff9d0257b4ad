import type { AuditedEntityDto } from '@abp/ng.core';

export interface VehicleGroupDto extends AuditedEntityDto<string> {
  name?: string;
}

export interface VehicleGroupCreateDto {
  name?: string;
  vehicleGroupVehicleCreateDtos: VehicleGroupVehicleCreateDto[];
}

export interface VehicleGroupDetailsDto extends AuditedEntityDto<string> {
  name?: string;
}

export interface VehicleGroupVehicleCreateDto {
  vehicleId?: string;
}
