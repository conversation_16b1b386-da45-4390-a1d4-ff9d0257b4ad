import {
  AccountService,
  ProfileService
} from "./chunk-NQZEAJRQ.js";
import {
  AbpVisibleDirective,
  ButtonComponent,
  Confirmation,
  ConfirmationService,
  DataTableColumnCellDirective,
  DataTableColumnDirective,
  DataTableColumnHeaderDirective,
  DatatableComponent,
  DateAdapter,
  DateTimeAdapter,
  DisabledDirective,
  EllipsisDirective,
  LoadingDirective,
  NgbDateAdapter,
  NgbDatepickerModule,
  NgbDropdown,
  NgbDropdownButtonItem,
  NgbDropdownItem,
  NgbDropdownMenu,
  NgbDropdownModule,
  NgbDropdownToggle,
  NgbInputDatepicker,
  NgbTimeAdapter,
  NgbTimepicker,
  NgbTimepickerModule,
  NgbTooltip,
  NgbTooltipModule,
  NgbTypeahead,
  NgbTypeaheadModule,
  NgxDatatableDefaultDirective,
  NgxDatatableListDirective,
  NgxDatatableModule,
  NgxValidateCoreModule,
  ThemeSharedModule,
  TimeAdapter,
  ToasterService,
  ValidationDirective,
  ValidationGroupDirective,
  ValidationStyleDirective,
  ValidationTargetDirective,
  comparePasswords,
  fadeIn,
  getPasswordValidators
} from "./chunk-NFRRDEX3.js";
import {
  transition,
  trigger,
  useAnimation
} from "./chunk-WXRVJEAW.js";
import {
  AbpValidators,
  AuthService,
  AutofocusDirective,
  ConfigStateService,
  CoreModule,
  FormSubmitDirective,
  InternalStore,
  LazyModuleFactory,
  LocalizationModule,
  LocalizationPipe,
  LocalizationService,
  PermissionDirective,
  PermissionService,
  ReplaceableRouteContainerComponent,
  ReplaceableTemplateDirective,
  RestService,
  RouterOutletComponent,
  ShowPasswordDirective,
  TrackByService,
  authGuard,
  collectionCompare,
  createLocalizationPipeKeyGenerator,
  escapeHtmlChars,
  getShortDateFormat,
  getShortDateShortTimeFormat,
  getShortTimeFormat
} from "./chunk-3CXWNYMM.js";
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterModule
} from "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import {
  CheckboxControlValueAccessor,
  ControlContainer,
  DefaultValueAccessor,
  FormControlName,
  FormGroupDirective,
  FormGroupName,
  FormsModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgModel,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  SelectMultipleControlValueAccessor,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-JP2LMHJE.js";
import {
  AsyncPipe,
  CommonModule,
  NgClass,
  NgComponentOutlet,
  NgTemplateOutlet,
  formatDate
} from "./chunk-6D52GKB4.js";
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Directive,
  EventEmitter,
  Inject,
  Injectable,
  InjectionToken,
  Injector,
  Input,
  LOCALE_ID,
  NgModule,
  Optional,
  Output,
  Pipe,
  SkipSelf,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
  inject,
  setClassMetadata,
  ɵɵInheritDefinitionFeature,
  ɵɵNgOnChangesFeature,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵpipeBind3,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵpureFunction2,
  ɵɵpureFunction3,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵrepeaterTrackByIndex,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeHtml,
  ɵɵstyleMap,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty,
  ɵɵviewQuery
} from "./chunk-QGPYGS5J.js";
import {
  merge
} from "./chunk-GJSJXBTC.js";
import "./chunk-BTHIXAM7.js";
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  of,
  pipe,
  switchMap,
  take,
  tap,
  throwError,
  zip
} from "./chunk-DJECZSZD.js";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-ZTELYOIP.js";

// node_modules/@abp/utils/dist/fesm2015/abp-utils.js
var ListNode = class {
  constructor(value) {
    this.value = value;
  }
};
var LinkedList = class _LinkedList {
  constructor() {
    this.size = 0;
  }
  get head() {
    return this.first;
  }
  get tail() {
    return this.last;
  }
  get length() {
    return this.size;
  }
  attach(value, previousNode, nextNode) {
    if (!previousNode) return this.addHead(value);
    if (!nextNode) return this.addTail(value);
    const node = new ListNode(value);
    node.previous = previousNode;
    previousNode.next = node;
    node.next = nextNode;
    nextNode.previous = node;
    this.size++;
    return node;
  }
  attachMany(values, previousNode, nextNode) {
    if (!values.length) return [];
    if (!previousNode) return this.addManyHead(values);
    if (!nextNode) return this.addManyTail(values);
    const list = new _LinkedList();
    list.addManyTail(values);
    list.first.previous = previousNode;
    previousNode.next = list.first;
    list.last.next = nextNode;
    nextNode.previous = list.last;
    this.size += values.length;
    return list.toNodeArray();
  }
  detach(node) {
    if (!node.previous) return this.dropHead();
    if (!node.next) return this.dropTail();
    node.previous.next = node.next;
    node.next.previous = node.previous;
    this.size--;
    return node;
  }
  add(value) {
    return {
      after: (...params) => this.addAfter.call(this, value, ...params),
      before: (...params) => this.addBefore.call(this, value, ...params),
      byIndex: (position) => this.addByIndex(value, position),
      head: () => this.addHead(value),
      tail: () => this.addTail(value)
    };
  }
  addMany(values) {
    return {
      after: (...params) => this.addManyAfter.call(this, values, ...params),
      before: (...params) => this.addManyBefore.call(this, values, ...params),
      byIndex: (position) => this.addManyByIndex(values, position),
      head: () => this.addManyHead(values),
      tail: () => this.addManyTail(values)
    };
  }
  addAfter(value, previousValue, compareFn = collectionCompare) {
    const previous = this.find((node) => compareFn(node.value, previousValue));
    return previous ? this.attach(value, previous, previous.next) : this.addTail(value);
  }
  addBefore(value, nextValue, compareFn = collectionCompare) {
    const next = this.find((node) => compareFn(node.value, nextValue));
    return next ? this.attach(value, next.previous, next) : this.addHead(value);
  }
  addByIndex(value, position) {
    if (position < 0) position += this.size;
    else if (position >= this.size) return this.addTail(value);
    if (position <= 0) return this.addHead(value);
    const next = this.get(position);
    return this.attach(value, next.previous, next);
  }
  addHead(value) {
    const node = new ListNode(value);
    node.next = this.first;
    if (this.first) this.first.previous = node;
    else this.last = node;
    this.first = node;
    this.size++;
    return node;
  }
  addTail(value) {
    const node = new ListNode(value);
    if (this.first) {
      node.previous = this.last;
      this.last.next = node;
      this.last = node;
    } else {
      this.first = node;
      this.last = node;
    }
    this.size++;
    return node;
  }
  addManyAfter(values, previousValue, compareFn = collectionCompare) {
    const previous = this.find((node) => compareFn(node.value, previousValue));
    return previous ? this.attachMany(values, previous, previous.next) : this.addManyTail(values);
  }
  addManyBefore(values, nextValue, compareFn = collectionCompare) {
    const next = this.find((node) => compareFn(node.value, nextValue));
    return next ? this.attachMany(values, next.previous, next) : this.addManyHead(values);
  }
  addManyByIndex(values, position) {
    if (position < 0) position += this.size;
    if (position <= 0) return this.addManyHead(values);
    if (position >= this.size) return this.addManyTail(values);
    const next = this.get(position);
    return this.attachMany(values, next.previous, next);
  }
  addManyHead(values) {
    return values.reduceRight((nodes, value) => {
      nodes.unshift(this.addHead(value));
      return nodes;
    }, []);
  }
  addManyTail(values) {
    return values.map((value) => this.addTail(value));
  }
  drop() {
    return {
      byIndex: (position) => this.dropByIndex(position),
      byValue: (...params) => this.dropByValue.apply(this, params),
      byValueAll: (...params) => this.dropByValueAll.apply(this, params),
      head: () => this.dropHead(),
      tail: () => this.dropTail()
    };
  }
  dropMany(count) {
    return {
      byIndex: (position) => this.dropManyByIndex(count, position),
      head: () => this.dropManyHead(count),
      tail: () => this.dropManyTail(count)
    };
  }
  dropByIndex(position) {
    if (position < 0) position += this.size;
    const current = this.get(position);
    return current ? this.detach(current) : void 0;
  }
  dropByValue(value, compareFn = collectionCompare) {
    const position = this.findIndex((node) => compareFn(node.value, value));
    return position < 0 ? void 0 : this.dropByIndex(position);
  }
  dropByValueAll(value, compareFn = collectionCompare) {
    const dropped = [];
    for (let current = this.first, position = 0; current; position++, current = current.next) {
      if (compareFn(current.value, value)) {
        dropped.push(this.dropByIndex(position - dropped.length));
      }
    }
    return dropped;
  }
  dropHead() {
    const head = this.first;
    if (head) {
      this.first = head.next;
      if (this.first) this.first.previous = void 0;
      else this.last = void 0;
      this.size--;
      return head;
    }
    return void 0;
  }
  dropTail() {
    const tail = this.last;
    if (tail) {
      this.last = tail.previous;
      if (this.last) this.last.next = void 0;
      else this.first = void 0;
      this.size--;
      return tail;
    }
    return void 0;
  }
  dropManyByIndex(count, position) {
    if (count <= 0) return [];
    if (position < 0) position = Math.max(position + this.size, 0);
    else if (position >= this.size) return [];
    count = Math.min(count, this.size - position);
    const dropped = [];
    while (count--) {
      const current = this.get(position);
      dropped.push(this.detach(current));
    }
    return dropped;
  }
  dropManyHead(count) {
    if (count <= 0) return [];
    count = Math.min(count, this.size);
    const dropped = [];
    while (count--) dropped.unshift(this.dropHead());
    return dropped;
  }
  dropManyTail(count) {
    if (count <= 0) return [];
    count = Math.min(count, this.size);
    const dropped = [];
    while (count--) dropped.push(this.dropTail());
    return dropped;
  }
  find(predicate) {
    for (let current = this.first, position = 0; current; position++, current = current.next) {
      if (predicate(current, position, this)) return current;
    }
    return void 0;
  }
  findIndex(predicate) {
    for (let current = this.first, position = 0; current; position++, current = current.next) {
      if (predicate(current, position, this)) return position;
    }
    return -1;
  }
  forEach(iteratorFn) {
    for (let node = this.first, position = 0; node; position++, node = node.next) {
      iteratorFn(node, position, this);
    }
  }
  get(position) {
    return this.find((_, index) => position === index);
  }
  indexOf(value, compareFn = collectionCompare) {
    return this.findIndex((node) => compareFn(node.value, value));
  }
  toArray() {
    const array = new Array(this.size);
    this.forEach((node, index) => array[index] = node.value);
    return array;
  }
  toNodeArray() {
    const array = new Array(this.size);
    this.forEach((node, index) => array[index] = node);
    return array;
  }
  toString(mapperFn = JSON.stringify) {
    return this.toArray().map((value) => mapperFn(value)).join(" <-> ");
  }
  // Cannot use Generator type because of ng-packagr
  *[Symbol.iterator]() {
    for (let node = this.first, position = 0; node; position++, node = node.next) {
      yield node.value;
    }
  }
};

// node_modules/@abp/ng.components/fesm2022/abp-ng.components-extensible.mjs
var _c0 = ["field"];
var _forTrack0 = ($index, $item) => $item.value;
var _c1 = () => ({
  $implicit: "form-check-label"
});
var _c2 = () => ({
  standalone: true
});
var _c3 = (a0, a1) => ({
  "fa-eye-slash": a0,
  "fa-eye": a1
});
function ExtensibleFormPropComponent_ng_container_0_Case_1_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_1_ng_container_0_Template, 1, 0, "ng-container", 9);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngComponentOutlet", ctx_r0.prop.template)("ngComponentOutletInjector", ctx_r0.injectorForCustomComponent);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_3_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_3_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelement(1, "input", 11, 1);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name)("autocomplete", ctx_r0.prop.autocomplete)("type", ctx_r0.getType(ctx_r0.prop))("abpDisabled", ctx_r0.disabled)("readonly", ctx_r0.readonly);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "input", 6);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("formControlName", ctx_r0.prop.name);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_5_ng_template_3_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_5_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 7);
    ɵɵelement(1, "input", 12, 1);
    ɵɵtemplate(3, ExtensibleFormPropComponent_ng_container_0_Case_5_ng_template_3_Template, 0, 0, "ng-template", 13);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵadvance();
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name)("abpDisabled", ctx_r0.disabled);
    ɵɵadvance(2);
    ɵɵproperty("ngTemplateOutlet", label_r2)("ngTemplateOutletContext", ɵɵpureFunction0(5, _c1));
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_6_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_6_For_4_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
    ɵɵpipe(1, "abpLocalization");
  }
  if (rf & 2) {
    const option_r3 = ɵɵnextContext().$implicit;
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(1, 1, "::" + option_r3.key), " ");
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_6_For_4_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
  }
  if (rf & 2) {
    const option_r3 = ɵɵnextContext().$implicit;
    ɵɵtextInterpolate1(" ", option_r3.key, " ");
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_6_For_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "option", 15);
    ɵɵtemplate(1, ExtensibleFormPropComponent_ng_container_0_Case_6_For_4_Conditional_1_Template, 2, 3)(2, ExtensibleFormPropComponent_ng_container_0_Case_6_For_4_Conditional_2_Template, 1, 1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const option_r3 = ctx.$implicit;
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngValue", option_r3.value);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.prop.isExtra ? 1 : 2);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_6_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelementStart(1, "select", 14, 1);
    ɵɵrepeaterCreate(3, ExtensibleFormPropComponent_ng_container_0_Case_6_For_4_Template, 3, 2, "option", 15, _forTrack0);
    ɵɵpipe(5, "async");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name)("abpDisabled", ctx_r0.disabled);
    ɵɵadvance(2);
    ɵɵrepeater(ɵɵpipeBind1(5, 4, ctx_r0.options$));
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_7_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_7_For_4_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
    ɵɵpipe(1, "abpLocalization");
  }
  if (rf & 2) {
    const option_r4 = ɵɵnextContext().$implicit;
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(1, 1, "::" + option_r4.key), " ");
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_7_For_4_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
  }
  if (rf & 2) {
    const option_r4 = ɵɵnextContext().$implicit;
    ɵɵtextInterpolate1(" ", option_r4.key, " ");
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_7_For_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "option", 15);
    ɵɵtemplate(1, ExtensibleFormPropComponent_ng_container_0_Case_7_For_4_Conditional_1_Template, 2, 3)(2, ExtensibleFormPropComponent_ng_container_0_Case_7_For_4_Conditional_2_Template, 1, 1);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const option_r4 = ctx.$implicit;
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵproperty("ngValue", option_r4.value);
    ɵɵadvance();
    ɵɵconditional(ctx_r0.prop.isExtra ? 1 : 2);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_7_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelementStart(1, "select", 16, 1);
    ɵɵrepeaterCreate(3, ExtensibleFormPropComponent_ng_container_0_Case_7_For_4_Template, 3, 2, "option", 15, _forTrack0);
    ɵɵpipe(5, "async");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name)("abpDisabled", ctx_r0.disabled);
    ɵɵadvance(2);
    ɵɵrepeater(ɵɵpipeBind1(5, 4, ctx_r0.options$));
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_8_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = ɵɵgetCurrentView();
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_8_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelementStart(1, "div", 17, 2)(3, "input", 18, 1);
    ɵɵtwoWayListener("ngModelChange", function ExtensibleFormPropComponent_ng_container_0_Case_8_Template_input_ngModelChange_3_listener($event) {
      ɵɵrestoreView(_r5);
      const ctx_r0 = ɵɵnextContext(2);
      ɵɵtwoWayBindingSet(ctx_r0.typeaheadModel, $event) || (ctx_r0.typeaheadModel = $event);
      return ɵɵresetView($event);
    });
    ɵɵlistener("selectItem", function ExtensibleFormPropComponent_ng_container_0_Case_8_Template_input_selectItem_3_listener($event) {
      ɵɵrestoreView(_r5);
      const ctx_r0 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r0.setTypeaheadValue($event.item));
    })("blur", function ExtensibleFormPropComponent_ng_container_0_Case_8_Template_input_blur_3_listener() {
      ɵɵrestoreView(_r5);
      const ctx_r0 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r0.setTypeaheadValue(ctx_r0.typeaheadModel));
    });
    ɵɵelementEnd();
    ɵɵelement(5, "input", 6);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const typeahead_r6 = ɵɵreference(2);
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance(3);
    ɵɵclassProp("is-invalid", typeahead_r6.classList.contains("is-invalid"));
    ɵɵproperty("id", ctx_r0.prop.id)("autocomplete", ctx_r0.prop.autocomplete)("abpDisabled", ctx_r0.disabled)("ngbTypeahead", ctx_r0.search)("editable", false)("inputFormatter", ctx_r0.typeaheadFormatter)("resultFormatter", ctx_r0.typeaheadFormatter)("ngModelOptions", ɵɵpureFunction0(13, _c2));
    ɵɵtwoWayProperty("ngModel", ctx_r0.typeaheadModel);
    ɵɵadvance(2);
    ɵɵproperty("formControlName", ctx_r0.prop.name);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_9_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = ɵɵgetCurrentView();
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_9_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelementStart(1, "input", 19, 3);
    ɵɵlistener("click", function ExtensibleFormPropComponent_ng_container_0_Case_9_Template_input_click_1_listener() {
      ɵɵrestoreView(_r7);
      const datepicker_r8 = ɵɵreference(2);
      return ɵɵresetView(datepicker_r8.open());
    })("keyup.space", function ExtensibleFormPropComponent_ng_container_0_Case_9_Template_input_keyup_space_1_listener() {
      ɵɵrestoreView(_r7);
      const datepicker_r8 = ɵɵreference(2);
      return ɵɵresetView(datepicker_r8.open());
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_10_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_10_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_10_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelement(1, "ngb-timepicker", 20);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("formControlName", ctx_r0.prop.name);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_11_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_11_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_11_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelement(1, "abp-extensible-date-time-picker", 21);
    ɵɵpipe(2, "async");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("prop", ctx_r0.prop)("meridian", ɵɵpipeBind1(2, 3, ctx_r0.meridian$));
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_12_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_12_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_12_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelement(1, "textarea", 22, 1);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance();
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name)("abpDisabled", ctx_r0.disabled)("readonly", ctx_r0.readonly);
  }
}
function ExtensibleFormPropComponent_ng_container_0_Case_13_ng_template_0_Template(rf, ctx) {
}
function ExtensibleFormPropComponent_ng_container_0_Case_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = ɵɵgetCurrentView();
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Case_13_ng_template_0_Template, 0, 0, "ng-template", 10);
    ɵɵelementStart(1, "div", 23);
    ɵɵelement(2, "input", 24);
    ɵɵelementStart(3, "button", 25);
    ɵɵlistener("click", function ExtensibleFormPropComponent_ng_container_0_Case_13_Template_button_click_3_listener() {
      ɵɵrestoreView(_r9);
      const ctx_r0 = ɵɵnextContext(2);
      return ɵɵresetView(ctx_r0.showPassword = !ctx_r0.showPassword);
    });
    ɵɵelement(4, "i", 26);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const label_r2 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", label_r2);
    ɵɵadvance(2);
    ɵɵproperty("id", ctx_r0.prop.id)("formControlName", ctx_r0.prop.name)("abpShowPassword", ctx_r0.showPassword);
    ɵɵadvance(2);
    ɵɵproperty("ngClass", ɵɵpureFunction2(5, _c3, !ctx_r0.showPassword, ctx_r0.showPassword));
  }
}
function ExtensibleFormPropComponent_ng_container_0_Conditional_14_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "small", 8);
    ɵɵtext(1);
    ɵɵpipe(2, "abpLocalization");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵtextInterpolate(ɵɵpipeBind1(2, 1, ctx_r0.prop.formText));
  }
}
function ExtensibleFormPropComponent_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, ExtensibleFormPropComponent_ng_container_0_Case_1_Template, 1, 2, "ng-container");
    ɵɵelementStart(2, "div", 5);
    ɵɵtemplate(3, ExtensibleFormPropComponent_ng_container_0_Case_3_Template, 3, 7)(4, ExtensibleFormPropComponent_ng_container_0_Case_4_Template, 1, 1, "input", 6)(5, ExtensibleFormPropComponent_ng_container_0_Case_5_Template, 4, 6, "div", 7)(6, ExtensibleFormPropComponent_ng_container_0_Case_6_Template, 6, 6)(7, ExtensibleFormPropComponent_ng_container_0_Case_7_Template, 6, 6)(8, ExtensibleFormPropComponent_ng_container_0_Case_8_Template, 6, 14)(9, ExtensibleFormPropComponent_ng_container_0_Case_9_Template, 3, 3)(10, ExtensibleFormPropComponent_ng_container_0_Case_10_Template, 2, 2)(11, ExtensibleFormPropComponent_ng_container_0_Case_11_Template, 3, 5)(12, ExtensibleFormPropComponent_ng_container_0_Case_12_Template, 3, 5)(13, ExtensibleFormPropComponent_ng_container_0_Case_13_Template, 5, 8)(14, ExtensibleFormPropComponent_ng_container_0_Conditional_14_Template, 3, 3, "small", 8);
    ɵɵelementEnd();
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_4_0;
    const ctx_r0 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵconditional((tmp_2_0 = ctx_r0.getComponent(ctx_r0.prop)) === "template" ? 1 : -1);
    ɵɵadvance();
    ɵɵproperty("ngClass", ctx_r0.containerClassName);
    ɵɵadvance();
    ɵɵconditional((tmp_4_0 = ctx_r0.getComponent(ctx_r0.prop)) === "input" ? 3 : tmp_4_0 === "hidden" ? 4 : tmp_4_0 === "checkbox" ? 5 : tmp_4_0 === "select" ? 6 : tmp_4_0 === "multiselect" ? 7 : tmp_4_0 === "typeahead" ? 8 : tmp_4_0 === "date" ? 9 : tmp_4_0 === "time" ? 10 : tmp_4_0 === "dateTime" ? 11 : tmp_4_0 === "textarea" ? 12 : tmp_4_0 === "passwordinputgroup" ? 13 : -1);
    ɵɵadvance(11);
    ɵɵconditional(ctx_r0.prop.formText ? 14 : -1);
  }
}
function ExtensibleFormPropComponent_ng_template_1_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
    ɵɵpipe(1, "abpLocalization");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(1, 1, ctx_r0.prop.displayTextResolver(ctx_r0.data)), " ");
  }
}
function ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
    ɵɵpipe(1, "abpLocalization");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(1, 1, "::" + ctx_r0.prop.displayName), " ");
  }
}
function ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
    ɵɵpipe(1, "abpLocalization");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(3);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(1, 1, ctx_r0.prop.displayName), " ");
  }
}
function ExtensibleFormPropComponent_ng_template_1_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_0_Template, 2, 3)(1, ExtensibleFormPropComponent_ng_template_1_Conditional_2_Conditional_1_Template, 2, 3);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵconditional(ctx_r0.prop.isExtra ? 0 : 1);
  }
}
function ExtensibleFormPropComponent_ng_template_1_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "i", 28);
    ɵɵpipe(1, "abpLocalization");
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    ɵɵproperty("ngbTooltip", ɵɵpipeBind1(1, 2, ctx_r0.prop.tooltip.text))("placement", ctx_r0.prop.tooltip.placement || "auto");
  }
}
function ExtensibleFormPropComponent_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "label", 27);
    ɵɵtemplate(1, ExtensibleFormPropComponent_ng_template_1_Conditional_1_Template, 2, 3)(2, ExtensibleFormPropComponent_ng_template_1_Conditional_2_Template, 2, 1);
    ɵɵtext(3);
    ɵɵtemplate(4, ExtensibleFormPropComponent_ng_template_1_Conditional_4_Template, 2, 4, "i", 28);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const classes_r10 = ctx.$implicit;
    const ctx_r0 = ɵɵnextContext();
    ɵɵproperty("htmlFor", ctx_r0.prop.id)("ngClass", classes_r10 || "form-label");
    ɵɵadvance();
    ɵɵconditional(ctx_r0.prop.displayTextResolver ? 1 : 2);
    ɵɵadvance(2);
    ɵɵtextInterpolate1(" ", ctx_r0.asterisk, " ");
    ɵɵadvance();
    ɵɵconditional(ctx_r0.prop.tooltip ? 4 : -1);
  }
}
var _forTrack1 = ($index, $item) => $item.name;
var _c4 = (a0, a1, a2) => ({
  groupedProp: a0,
  data: a1,
  isFirstGroup: a2
});
function ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 2);
    ɵɵelementContainer(1, 3);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const data_r1 = ɵɵnextContext().$implicit;
    const ctx_r1 = ɵɵnextContext();
    const groupedProp_r3 = ctx_r1.$implicit;
    const ɵ$index_2_r4 = ctx_r1.$index;
    ɵɵnextContext(2);
    const propListTemplate_r5 = ɵɵreference(2);
    ɵɵproperty("ngClass", groupedProp_r3.group == null ? null : groupedProp_r3.group.className);
    ɵɵattribute("data-name", (groupedProp_r3.group == null ? null : groupedProp_r3.group.name) || (groupedProp_r3.group == null ? null : groupedProp_r3.group.className));
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", propListTemplate_r5)("ngTemplateOutletContext", ɵɵpureFunction3(4, _c4, groupedProp_r3, data_r1, ɵ$index_2_r4 === 0));
  }
}
function ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0, 3);
  }
  if (rf & 2) {
    const data_r1 = ɵɵnextContext().$implicit;
    const ctx_r1 = ɵɵnextContext();
    const groupedProp_r3 = ctx_r1.$implicit;
    const ɵ$index_2_r4 = ctx_r1.$index;
    ɵɵnextContext(2);
    const propListTemplate_r5 = ɵɵreference(2);
    ɵɵproperty("ngTemplateOutlet", propListTemplate_r5)("ngTemplateOutletContext", ɵɵpureFunction3(2, _c4, groupedProp_r3, data_r1, ɵ$index_2_r4 === 0));
  }
}
function ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_1_Template, 2, 8, "div", 2)(2, ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Conditional_2_Template, 1, 6, "ng-container", 3);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const data_r1 = ctx.$implicit;
    const ctx_r1 = ɵɵnextContext();
    const groupedProp_r3 = ctx_r1.$implicit;
    const ɵ$index_2_r4 = ctx_r1.$index;
    const ctx_r5 = ɵɵnextContext(2);
    ɵɵadvance();
    ɵɵconditional(ctx_r5.isAnyGroupMemberVisible(ɵ$index_2_r4, data_r1) && (groupedProp_r3.group == null ? null : groupedProp_r3.group.className) ? 1 : 2);
  }
}
function ExtensibleFormComponent_Conditional_0_For_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormComponent_Conditional_0_For_1_ng_container_0_Template, 3, 1, "ng-container", 1);
  }
  if (rf & 2) {
    const groupedProp_r3 = ctx.$implicit;
    const ctx_r5 = ɵɵnextContext(2);
    ɵɵproperty("abpPropDataFromList", groupedProp_r3.formPropList)("abpPropDataWithRecord", ctx_r5.record);
  }
}
function ExtensibleFormComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵrepeaterCreate(0, ExtensibleFormComponent_Conditional_0_For_1_Template, 1, 2, "ng-container", null, ɵɵrepeaterTrackByIndex);
  }
  if (rf & 2) {
    const ctx_r5 = ɵɵnextContext();
    ɵɵrepeater(ctx_r5.groupedPropList.items);
  }
}
function ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0, 4);
    ɵɵelement(1, "abp-extensible-form-prop", 5);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const prop_r7 = ɵɵnextContext(2).$implicit;
    const data_r8 = ɵɵnextContext().data;
    const ctx_r5 = ɵɵnextContext();
    ɵɵproperty("formGroupName", ctx_r5.extraPropertiesKey);
    ɵɵadvance();
    ɵɵclassMap(prop_r7.className);
    ɵɵproperty("prop", prop_r7)("data", data_r8);
  }
}
function ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "abp-extensible-form-prop", 7);
  }
  if (rf & 2) {
    const ctx_r8 = ɵɵnextContext(3);
    const prop_r7 = ctx_r8.$implicit;
    const ɵ$index_17_r10 = ctx_r8.$index;
    const ctx_r10 = ɵɵnextContext();
    const data_r8 = ctx_r10.data;
    const isFirstGroup_r12 = ctx_r10.isFirstGroup;
    ɵɵclassMap(prop_r7.className);
    ɵɵproperty("prop", prop_r7)("data", data_r8)("first", ɵ$index_17_r10 === 0)("isFirstGroup", isFirstGroup_r12);
  }
}
function ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Conditional_0_Template, 1, 6, "abp-extensible-form-prop", 6);
  }
  if (rf & 2) {
    const prop_r7 = ɵɵnextContext(2).$implicit;
    const ctx_r5 = ɵɵnextContext(2);
    ɵɵconditional(ctx_r5.form.get(prop_r7.name) ? 0 : -1);
  }
}
function ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_0_Template, 2, 5, "ng-container", 4)(1, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Conditional_1_Template, 1, 1);
  }
  if (rf & 2) {
    const prop_r7 = ɵɵnextContext().$implicit;
    const ctx_r5 = ɵɵnextContext(2);
    ɵɵconditional(ctx_r5.extraProperties.controls[prop_r7.name] ? 0 : 1);
  }
}
function ExtensibleFormComponent_ng_template_1_For_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleFormComponent_ng_template_1_For_1_Conditional_0_Template, 2, 1);
  }
  if (rf & 2) {
    const prop_r7 = ctx.$implicit;
    const data_r8 = ɵɵnextContext().data;
    ɵɵconditional(prop_r7.visible(data_r8) ? 0 : -1);
  }
}
function ExtensibleFormComponent_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵrepeaterCreate(0, ExtensibleFormComponent_ng_template_1_For_1_Template, 1, 1, null, null, _forTrack1);
  }
  if (rf & 2) {
    const groupedProp_r13 = ctx.groupedProp;
    ɵɵrepeater(groupedProp_r13.formPropList);
  }
}
var _forTrack2 = ($index, $item) => $item.text;
var _c5 = (a0) => ({
  $implicit: a0
});
function GridActionsComponent_Conditional_0_For_7_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0, 4);
  }
  if (rf & 2) {
    const action_r1 = ctx.$implicit;
    ɵɵnextContext(2);
    const dropDownBtnItemTmp_r2 = ɵɵreference(3);
    ɵɵproperty("ngTemplateOutlet", dropDownBtnItemTmp_r2)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c5, action_r1));
  }
}
function GridActionsComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 3)(1, "button", 5);
    ɵɵelement(2, "i", 6);
    ɵɵtext(3);
    ɵɵpipe(4, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(5, "div", 7);
    ɵɵrepeaterCreate(6, GridActionsComponent_Conditional_0_For_7_Template, 1, 4, "ng-container", 4, _forTrack2);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    ɵɵadvance(2);
    ɵɵclassProp("me-1", ctx_r2.icon);
    ɵɵproperty("ngClass", ctx_r2.icon);
    ɵɵadvance();
    ɵɵtextInterpolate1("", ɵɵpipeBind1(4, 4, ctx_r2.text), " ");
    ɵɵadvance(3);
    ɵɵrepeater(ctx_r2.actionList);
  }
}
function GridActionsComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0, 4);
  }
  if (rf & 2) {
    const ctx_r2 = ɵɵnextContext();
    const btnTmp_r4 = ɵɵreference(7);
    ɵɵproperty("ngTemplateOutlet", btnTmp_r4)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c5, ctx_r2.actionList.get(0).value));
  }
}
function GridActionsComponent_ng_template_2_Conditional_0_button_0_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function GridActionsComponent_ng_template_2_Conditional_0_button_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 10);
    ɵɵlistener("click", function GridActionsComponent_ng_template_2_Conditional_0_button_0_Template_button_click_0_listener() {
      ɵɵrestoreView(_r5);
      const action_r6 = ɵɵnextContext(2).$implicit;
      const ctx_r2 = ɵɵnextContext();
      return ɵɵresetView(action_r6.action(ctx_r2.data));
    });
    ɵɵtemplate(1, GridActionsComponent_ng_template_2_Conditional_0_button_0_ng_container_1_Template, 1, 0, "ng-container", 11);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const action_r6 = ɵɵnextContext(2).$implicit;
    ɵɵnextContext();
    const buttonContentTmp_r7 = ɵɵreference(5);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", buttonContentTmp_r7)("ngTemplateOutletContext", ɵɵpureFunction1(2, _c5, action_r6));
  }
}
function GridActionsComponent_ng_template_2_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_2_Conditional_0_button_0_Template, 2, 4, "button", 9);
  }
  if (rf & 2) {
    const action_r6 = ɵɵnextContext().$implicit;
    ɵɵproperty("abpPermission", action_r6.permission)("abpPermissionRunChangeDetection", false);
  }
}
function GridActionsComponent_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_2_Conditional_0_Template, 1, 2, "button", 8);
  }
  if (rf & 2) {
    const action_r6 = ctx.$implicit;
    const ctx_r2 = ɵɵnextContext();
    ɵɵconditional(action_r6.visible(ctx_r2.data) ? 0 : -1);
  }
}
function GridActionsComponent_ng_template_4_Conditional_1_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span");
    ɵɵtext(1);
    ɵɵpipe(2, "abpLocalization");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const action_r8 = ɵɵnextContext(2).$implicit;
    ɵɵadvance();
    ɵɵtextInterpolate(ɵɵpipeBind1(2, 1, action_r8.text));
  }
}
function GridActionsComponent_ng_template_4_Conditional_1_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 12);
    ɵɵtext(1);
    ɵɵpipe(2, "abpLocalization");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const action_r8 = ɵɵnextContext(2).$implicit;
    ɵɵadvance();
    ɵɵtextInterpolate(ɵɵpipeBind1(2, 1, action_r8.text));
  }
}
function GridActionsComponent_ng_template_4_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_4_Conditional_1_Conditional_0_Template, 3, 3, "span")(1, GridActionsComponent_ng_template_4_Conditional_1_Conditional_1_Template, 3, 3, "div", 12);
  }
  if (rf & 2) {
    const action_r8 = ɵɵnextContext().$implicit;
    ɵɵconditional(action_r8.icon ? 0 : 1);
  }
}
function GridActionsComponent_ng_template_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "i", 6);
    ɵɵtemplate(1, GridActionsComponent_ng_template_4_Conditional_1_Template, 2, 1);
  }
  if (rf & 2) {
    const action_r8 = ctx.$implicit;
    ɵɵclassProp("me-1", action_r8.icon && !action_r8.showOnlyIcon);
    ɵɵproperty("ngClass", action_r8.icon);
    ɵɵadvance();
    ɵɵconditional(!action_r8.showOnlyIcon ? 1 : -1);
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 16);
    ɵɵpipe(1, "abpLocalization");
    ɵɵlistener("click", function GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template_button_click_0_listener() {
      ɵɵrestoreView(_r9);
      const action_r10 = ɵɵnextContext(3).$implicit;
      const ctx_r2 = ɵɵnextContext();
      return ɵɵresetView(action_r10.action(ctx_r2.data));
    });
    ɵɵtemplate(2, GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_ng_container_2_Template, 1, 0, "ng-container", 11);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const action_r10 = ɵɵnextContext(3).$implicit;
    ɵɵnextContext();
    const buttonContentTmp_r7 = ɵɵreference(5);
    ɵɵstyleMap(action_r10.btnStyle);
    ɵɵclassMap(action_r10.btnClass);
    ɵɵproperty("ngbTooltip", ɵɵpipeBind1(1, 8, action_r10.tooltip.text))("placement", action_r10.tooltip.placement || "auto");
    ɵɵadvance(2);
    ɵɵproperty("ngTemplateOutlet", buttonContentTmp_r7)("ngTemplateOutletContext", ɵɵpureFunction1(10, _c5, action_r10));
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_button_0_Template, 3, 12, "button", 15);
  }
  if (rf & 2) {
    const action_r10 = ɵɵnextContext(2).$implicit;
    ɵɵproperty("abpPermission", action_r10.permission)("abpPermissionRunChangeDetection", false);
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 18);
    ɵɵlistener("click", function GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template_button_click_0_listener() {
      ɵɵrestoreView(_r11);
      const action_r10 = ɵɵnextContext(3).$implicit;
      const ctx_r2 = ɵɵnextContext();
      return ɵɵresetView(action_r10.action(ctx_r2.data));
    });
    ɵɵtemplate(1, GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_ng_container_1_Template, 1, 0, "ng-container", 11);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const action_r10 = ɵɵnextContext(3).$implicit;
    ɵɵnextContext();
    const buttonContentTmp_r7 = ɵɵreference(5);
    ɵɵstyleMap(action_r10.btnStyle);
    ɵɵclassMap(action_r10.btnClass);
    ɵɵadvance();
    ɵɵproperty("ngTemplateOutlet", buttonContentTmp_r7)("ngTemplateOutletContext", ɵɵpureFunction1(6, _c5, action_r10));
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_button_0_Template, 2, 8, "button", 17);
  }
  if (rf & 2) {
    const action_r10 = ɵɵnextContext(2).$implicit;
    ɵɵproperty("abpPermission", action_r10.permission)("abpPermissionRunChangeDetection", false);
  }
}
function GridActionsComponent_ng_template_6_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Conditional_0_Template, 1, 2, "button", 13)(1, GridActionsComponent_ng_template_6_Conditional_0_Conditional_1_Template, 1, 2, "button", 14);
  }
  if (rf & 2) {
    const action_r10 = ɵɵnextContext().$implicit;
    ɵɵconditional(action_r10.tooltip ? 0 : 1);
  }
}
function GridActionsComponent_ng_template_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, GridActionsComponent_ng_template_6_Conditional_0_Template, 2, 1);
  }
  if (rf & 2) {
    const action_r10 = ctx.$implicit;
    const ctx_r2 = ɵɵnextContext();
    ɵɵconditional(action_r10.visible(ctx_r2.data) ? 0 : -1);
  }
}
var _c6 = (a0, a1) => ({
  $implicit: a0,
  index: a1
});
function ExtensibleTableComponent_Conditional_1_ng_template_2_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "abp-grid-actions", 6);
  }
  if (rf & 2) {
    const ctx_r0 = ɵɵnextContext(2);
    const row_r2 = ctx_r0.row;
    const i_r3 = ctx_r0.rowIndex;
    ɵɵproperty("index", i_r3)("record", row_r2);
  }
}
function ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Conditional_0_Template, 1, 2, "abp-grid-actions", 6);
  }
  if (rf & 2) {
    const row_r2 = ɵɵnextContext().row;
    const ctx_r3 = ɵɵnextContext(2);
    ɵɵconditional(ctx_r3.isVisibleActions(row_r2) ? 0 : -1);
  }
}
function ExtensibleTableComponent_Conditional_1_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleTableComponent_Conditional_1_ng_template_2_ng_container_0_Template, 1, 0, "ng-container", 5)(1, ExtensibleTableComponent_Conditional_1_ng_template_2_ng_template_1_Template, 1, 1, "ng-template", null, 0, ɵɵtemplateRefExtractor);
  }
  if (rf & 2) {
    const row_r2 = ctx.row;
    const i_r3 = ctx.rowIndex;
    const gridActions_r5 = ɵɵreference(2);
    const ctx_r3 = ɵɵnextContext(2);
    ɵɵproperty("ngTemplateOutlet", ctx_r3.actionsTemplate || gridActions_r5)("ngTemplateOutletContext", ɵɵpureFunction2(2, _c6, row_r2, i_r3));
  }
}
function ExtensibleTableComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "ngx-datatable-column", 2);
    ɵɵpipe(1, "abpLocalization");
    ɵɵtemplate(2, ExtensibleTableComponent_Conditional_1_ng_template_2_Template, 3, 5, "ng-template", 4);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = ɵɵnextContext();
    ɵɵproperty("name", ɵɵpipeBind1(1, 4, ctx_r3.actionsText))("maxWidth", ctx_r3.columnWidths[0])("width", ctx_r3.columnWidths[0])("sortable", false);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 9);
    ɵɵpipe(1, "abpLocalization");
    ɵɵtext(2);
    ɵɵelement(3, "i", 10);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const column_r6 = ɵɵnextContext().column;
    const prop_r7 = ɵɵnextContext(2).$implicit;
    ɵɵproperty("ngbTooltip", ɵɵpipeBind1(1, 3, prop_r7.tooltip.text))("placement", prop_r7.tooltip.placement || "auto");
    ɵɵadvance(2);
    ɵɵtextInterpolate1(" ", column_r6.name, " ");
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtext(0);
  }
  if (rf & 2) {
    const column_r6 = ɵɵnextContext().column;
    ɵɵtextInterpolate1(" ", column_r6.name, " ");
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_0_Template, 4, 5, "span", 9)(1, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Conditional_1_Template, 1, 1);
  }
  if (rf & 2) {
    const prop_r7 = ɵɵnextContext(2).$implicit;
    ɵɵconditional(prop_r7.tooltip ? 0 : 1);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "div", 14);
    ɵɵpipe(1, "async");
    ɵɵpipe(2, "async");
    ɵɵpipe(3, "abpLocalization");
    ɵɵlistener("click", function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template_div_click_0_listener() {
      ɵɵrestoreView(_r8);
      const ctx_r8 = ɵɵnextContext(3);
      const row_r10 = ctx_r8.row;
      const i_r11 = ctx_r8.index;
      const prop_r7 = ɵɵnextContext(2).$implicit;
      const ctx_r3 = ɵɵnextContext();
      return ɵɵresetView(prop_r7.action && prop_r7.action({
        getInjected: ctx_r3.getInjected,
        record: row_r10,
        index: i_r11
      }));
    });
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const row_r10 = ɵɵnextContext(3).row;
    const prop_r7 = ɵɵnextContext(2).$implicit;
    const ctx_r3 = ɵɵnextContext();
    ɵɵclassProp("pointer", prop_r7.action);
    ɵɵproperty("innerHTML", !prop_r7.isExtra ? ɵɵpipeBind1(1, 4, row_r10["_" + prop_r7.name] == null ? null : row_r10["_" + prop_r7.name].value) : ɵɵpipeBind1(3, 8, "::" + ɵɵpipeBind1(2, 6, row_r10["_" + prop_r7.name] == null ? null : row_r10["_" + prop_r7.name].value)), ɵɵsanitizeHtml)("ngClass", ctx_r3.entityPropTypeClasses[prop_r7.type]);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_ng_container_0_Template, 1, 0, "ng-container", 15);
  }
  if (rf & 2) {
    const row_r10 = ɵɵnextContext(3).row;
    const prop_r7 = ɵɵnextContext(2).$implicit;
    ɵɵproperty("ngComponentOutlet", row_r10["_" + prop_r7.name].component)("ngComponentOutletInjector", row_r10["_" + prop_r7.name].injector);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_1_Template, 4, 10, "div", 13)(2, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Conditional_2_Template, 1, 2, "ng-container");
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const row_r10 = ɵɵnextContext(2).row;
    const prop_r7 = ɵɵnextContext(2).$implicit;
    ɵɵadvance();
    ɵɵconditional(!row_r10["_" + prop_r7.name].component ? 1 : 2);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_ng_container_1_Template, 3, 1, "ng-container", 12);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    const row_r10 = ɵɵnextContext().row;
    const prop_r7 = ɵɵnextContext(2).$implicit;
    ɵɵadvance();
    ɵɵproperty("abpVisible", row_r10["_" + prop_r7.name] == null ? null : row_r10["_" + prop_r7.name].visible);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_ng_container_0_Template, 2, 1, "ng-container", 11);
  }
  if (rf & 2) {
    const prop_r7 = ɵɵnextContext(2).$implicit;
    ɵɵproperty("abpPermission", prop_r7.permission)("abpPermissionRunChangeDetection", false);
  }
}
function ExtensibleTableComponent_For_3_ngx_datatable_column_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "ngx-datatable-column", 3);
    ɵɵpipe(1, "abpLocalization");
    ɵɵtemplate(2, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_2_Template, 2, 1, "ng-template", 8)(3, ExtensibleTableComponent_For_3_ngx_datatable_column_0_ng_template_3_Template, 1, 2, "ng-template", 4);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r11 = ɵɵnextContext();
    const prop_r7 = ctx_r11.$implicit;
    const ɵ$index_18_r13 = ctx_r11.$index;
    const ctx_r3 = ɵɵnextContext();
    ɵɵproperty("width", ctx_r3.columnWidths[ɵ$index_18_r13 + 1] || 200)("name", ɵɵpipeBind1(1, 4, prop_r7.displayName))("prop", prop_r7.name)("sortable", prop_r7.sortable);
  }
}
function ExtensibleTableComponent_For_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, ExtensibleTableComponent_For_3_ngx_datatable_column_0_Template, 4, 6, "ngx-datatable-column", 7);
  }
  if (rf & 2) {
    const prop_r7 = ctx.$implicit;
    const ctx_r3 = ɵɵnextContext();
    ɵɵproperty("abpVisible", prop_r7.columnVisible(ctx_r3.getInjected));
  }
}
var _forTrack3 = ($index, $item) => $item.component || $item.action;
function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainer(0);
  }
}
function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_ng_container_0_Template, 1, 0, "ng-container", 4);
    ɵɵpipe(1, "createInjector");
  }
  if (rf & 2) {
    const action_r1 = ɵɵnextContext(3).$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("ngComponentOutlet", ctx)("ngComponentOutletInjector", ɵɵpipeBind3(1, 2, ctx_r1.record, action_r1, ctx_r1));
  }
}
function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "button", 6);
    ɵɵlistener("click", function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template_button_click_0_listener() {
      ɵɵrestoreView(_r3);
      const action_r1 = ɵɵnextContext(4).$implicit;
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(action_r1.action(ctx_r1.data));
    });
    ɵɵelement(1, "i", 7);
    ɵɵtext(2);
    ɵɵpipe(3, "abpLocalization");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const toolbarAction_r4 = ctx;
    const ctx_r1 = ɵɵnextContext(5);
    ɵɵproperty("ngClass", (toolbarAction_r4 == null ? null : toolbarAction_r4.btnClass) ? toolbarAction_r4 == null ? null : toolbarAction_r4.btnClass : ctx_r1.defaultBtnClass);
    ɵɵadvance();
    ɵɵclassProp("me-1", toolbarAction_r4 == null ? null : toolbarAction_r4.icon);
    ɵɵproperty("ngClass", toolbarAction_r4 == null ? null : toolbarAction_r4.icon);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(3, 5, toolbarAction_r4 == null ? null : toolbarAction_r4.text), " ");
  }
}
function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Conditional_0_Template, 4, 7, "button", 5);
  }
  if (rf & 2) {
    let tmp_14_0;
    const action_r1 = ɵɵnextContext(3).$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵconditional((tmp_14_0 = ctx_r1.asToolbarAction(action_r1).value) ? 0 : -1, tmp_14_0);
  }
}
function PageToolbarComponent_For_2_Conditional_1_ng_container_0_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementContainerStart(0);
    ɵɵtemplate(1, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_1_Template, 2, 6, "ng-container")(2, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Conditional_2_Template, 1, 1);
    ɵɵelementContainerEnd();
  }
  if (rf & 2) {
    let tmp_13_0;
    const action_r1 = ɵɵnextContext(2).$implicit;
    ɵɵadvance();
    ɵɵconditional((tmp_13_0 = action_r1.component) ? 1 : 2, tmp_13_0);
  }
}
function PageToolbarComponent_For_2_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵtemplate(0, PageToolbarComponent_For_2_Conditional_1_ng_container_0_Template, 3, 1, "ng-container", 3);
  }
  if (rf & 2) {
    const action_r1 = ɵɵnextContext().$implicit;
    ɵɵproperty("abpPermission", action_r1.permission)("abpPermissionRunChangeDetection", false);
  }
}
function PageToolbarComponent_For_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 2);
    ɵɵtemplate(1, PageToolbarComponent_For_2_Conditional_1_Template, 1, 2, "ng-container");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const action_r1 = ctx.$implicit;
    const ɵ$index_3_r5 = ctx.$index;
    const ɵ$count_3_r6 = ctx.$count;
    const ctx_r1 = ɵɵnextContext();
    ɵɵclassProp("pe-0", ɵ$index_3_r5 === ɵ$count_3_r6 - 1);
    ɵɵadvance();
    ɵɵconditional(action_r1.visible(ctx_r1.data) ? 1 : -1);
  }
}
var PropList = class extends LinkedList {
};
var PropData = class {
  get data() {
    return {
      getInjected: this.getInjected,
      index: this.index,
      record: this.record
    };
  }
};
var Prop = class {
  constructor(type, name, displayName, permission, visible = (_) => true, isExtra = false, template, className, formText, tooltip, displayTextResolver) {
    this.type = type;
    this.name = name;
    this.displayName = displayName;
    this.permission = permission;
    this.visible = visible;
    this.isExtra = isExtra;
    this.template = template;
    this.className = className;
    this.formText = formText;
    this.tooltip = tooltip;
    this.displayTextResolver = displayTextResolver;
    this.displayName = this.displayName || this.name;
  }
};
var PropsFactory = class {
  constructor() {
    this.contributorCallbacks = {};
  }
  get(name) {
    this.contributorCallbacks[name] = this.contributorCallbacks[name] || [];
    return new this._ctor(this.contributorCallbacks[name]);
  }
};
var Props = class {
  get props() {
    const propList = new this._ctor();
    this.callbackList.forEach((callback) => callback(propList));
    return propList;
  }
  constructor(callbackList) {
    this.callbackList = callbackList;
  }
  addContributor(contributeCallback) {
    this.callbackList.push(contributeCallback);
  }
  clearContributors() {
    while (this.callbackList.length) this.callbackList.pop();
  }
};
var FormPropList = class extends PropList {
};
var FormProps = class extends Props {
  constructor() {
    super(...arguments);
    this._ctor = FormPropList;
  }
};
var GroupedFormPropList = class {
  constructor() {
    this.items = [];
    this.count = 1;
  }
  addItem(item) {
    const groupName = item.group?.name;
    let group = this.items.find((i) => i.group?.name === groupName);
    if (group) {
      group.formPropList.addTail(item);
    } else {
      group = {
        formPropList: new FormPropList(),
        group: item.group || {
          name: `default${this.count++}`,
          className: item.group?.className
        }
      };
      group.formPropList.addHead(item);
      this.items.push(group);
    }
  }
};
var CreateFormPropsFactory = class extends PropsFactory {
  constructor() {
    super(...arguments);
    this._ctor = FormProps;
  }
};
var EditFormPropsFactory = class extends PropsFactory {
  constructor() {
    super(...arguments);
    this._ctor = FormProps;
  }
};
var FormProp = class _FormProp extends Prop {
  constructor(options) {
    super(options.type, options.name, options.displayName || "", options.permission || "", options.visible, options.isExtra, options.template, options.className, options.formText, options.tooltip);
    this.group = options.group;
    this.className = options.className;
    this.formText = options.formText;
    this.tooltip = options.tooltip;
    this.asyncValidators = options.asyncValidators || ((_) => []);
    this.validators = options.validators || ((_) => []);
    this.disabled = options.disabled || ((_) => false);
    this.readonly = options.readonly || ((_) => false);
    this.autocomplete = options.autocomplete || "off";
    this.options = options.options;
    this.id = options.id || options.name;
    const defaultValue = options.defaultValue;
    this.defaultValue = isFalsyValue(defaultValue) ? defaultValue : defaultValue || "";
    this.displayTextResolver = options.displayTextResolver;
  }
  static create(options) {
    return new _FormProp(options);
  }
  static createMany(arrayOfOptions) {
    return arrayOfOptions.map(_FormProp.create);
  }
};
var FormPropData = class extends PropData {
  constructor(injector, record) {
    super();
    this.record = record;
    this.getInjected = injector.get.bind(injector);
  }
};
function isFalsyValue(defaultValue) {
  return [0, "", false].indexOf(defaultValue) > -1;
}
function selfFactory(dependency) {
  return dependency;
}
var ExtensibleDateTimePickerComponent = class _ExtensibleDateTimePickerComponent {
  constructor() {
    this.cdRef = inject(ChangeDetectorRef);
    this.meridian = false;
  }
  setDate(dateStr) {
    this.date.writeValue(dateStr);
  }
  setTime(dateStr) {
    this.time.writeValue(dateStr);
  }
  static {
    this.ɵfac = function ExtensibleDateTimePickerComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensibleDateTimePickerComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ExtensibleDateTimePickerComponent,
      selectors: [["abp-extensible-date-time-picker"]],
      viewQuery: function ExtensibleDateTimePickerComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(NgbInputDatepicker, 5);
          ɵɵviewQuery(NgbTimepicker, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.date = _t.first);
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.time = _t.first);
        }
      },
      inputs: {
        prop: "prop",
        meridian: "meridian"
      },
      exportAs: ["abpExtensibleDateTimePicker"],
      standalone: true,
      features: [ɵɵProvidersFeature([], [{
        provide: ControlContainer,
        useFactory: selfFactory,
        deps: [[new Optional(), new SkipSelf(), ControlContainer]]
      }, {
        provide: NgbDateAdapter,
        useClass: DateTimeAdapter
      }, {
        provide: NgbTimeAdapter,
        useClass: DateTimeAdapter
      }]), ɵɵStandaloneFeature],
      decls: 4,
      vars: 4,
      consts: [["datepicker", "ngbDatepicker"], ["timepicker", ""], ["ngbDatepicker", "", "type", "text", 1, "form-control", 3, "ngModelChange", "click", "keyup.space", "id", "formControlName"], [3, "ngModelChange", "formControlName", "meridian"]],
      template: function ExtensibleDateTimePickerComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = ɵɵgetCurrentView();
          ɵɵelementStart(0, "input", 2, 0);
          ɵɵlistener("ngModelChange", function ExtensibleDateTimePickerComponent_Template_input_ngModelChange_0_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.setTime($event));
          })("click", function ExtensibleDateTimePickerComponent_Template_input_click_0_listener() {
            ɵɵrestoreView(_r1);
            const datepicker_r2 = ɵɵreference(1);
            return ɵɵresetView(datepicker_r2.open());
          })("keyup.space", function ExtensibleDateTimePickerComponent_Template_input_keyup_space_0_listener() {
            ɵɵrestoreView(_r1);
            const datepicker_r2 = ɵɵreference(1);
            return ɵɵresetView(datepicker_r2.open());
          });
          ɵɵelementEnd();
          ɵɵelementStart(2, "ngb-timepicker", 3, 1);
          ɵɵlistener("ngModelChange", function ExtensibleDateTimePickerComponent_Template_ngb_timepicker_ngModelChange_2_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.setDate($event));
          });
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("id", ctx.prop.id)("formControlName", ctx.prop.name);
          ɵɵadvance(2);
          ɵɵproperty("formControlName", ctx.prop.name)("meridian", ctx.meridian);
        }
      },
      dependencies: [CommonModule, ReactiveFormsModule, DefaultValueAccessor, NgControlStatus, FormControlName, NgbDatepickerModule, NgbInputDatepicker, NgbTimepickerModule, NgbTimepicker, NgxValidateCoreModule, ValidationDirective],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensibleDateTimePickerComponent, [{
    type: Component,
    args: [{
      exportAs: "abpExtensibleDateTimePicker",
      standalone: true,
      imports: [CommonModule, ReactiveFormsModule, NgbDatepickerModule, NgbTimepickerModule, NgxValidateCoreModule],
      selector: "abp-extensible-date-time-picker",
      template: `
    <input
      [id]="prop.id"
      [formControlName]="prop.name"
      (ngModelChange)="setTime($event)"
      (click)="datepicker.open()"
      (keyup.space)="datepicker.open()"
      ngbDatepicker
      #datepicker="ngbDatepicker"
      type="text"
      class="form-control"
    />
    <ngb-timepicker
      #timepicker
      [formControlName]="prop.name"
      (ngModelChange)="setDate($event)"
      [meridian]="meridian"
    ></ngb-timepicker>
  `,
      changeDetection: ChangeDetectionStrategy.OnPush,
      viewProviders: [{
        provide: ControlContainer,
        useFactory: selfFactory,
        deps: [[new Optional(), new SkipSelf(), ControlContainer]]
      }, {
        provide: NgbDateAdapter,
        useClass: DateTimeAdapter
      }, {
        provide: NgbTimeAdapter,
        useClass: DateTimeAdapter
      }]
    }]
  }], null, {
    prop: [{
      type: Input
    }],
    meridian: [{
      type: Input
    }],
    date: [{
      type: ViewChild,
      args: [NgbInputDatepicker]
    }],
    time: [{
      type: ViewChild,
      args: [NgbTimepicker]
    }]
  });
})();
var EXTENSIONS_IDENTIFIER = new InjectionToken("EXTENSIONS_IDENTIFIER");
var EXTENSIONS_ACTION_TYPE = new InjectionToken("EXTENSIONS_ACTION_TYPE");
var EXTENSIONS_ACTION_DATA = new InjectionToken("EXTENSIONS_ACTION_DATA");
var EXTENSIONS_ACTION_CALLBACK = new InjectionToken("EXTENSIONS_ACTION_DATA");
var PROP_DATA_STREAM = new InjectionToken("PROP_DATA_STREAM");
var ENTITY_PROP_TYPE_CLASSES = new InjectionToken("ENTITY_PROP_TYPE_CLASSES", {
  factory: () => ({})
});
var EXTENSIONS_FORM_PROP = new InjectionToken("EXTENSIONS_FORM_PROP");
var EXTENSIONS_FORM_PROP_DATA = new InjectionToken("EXTENSIONS_FORM_PROP_DATA");
var EXTRA_PROPERTIES_KEY = "extraProperties";
var TYPEAHEAD_TEXT_SUFFIX = "_Text";
var TYPEAHEAD_TEXT_SUFFIX_REGEX = /_Text$/;
function createTypeaheadOptions(lookup) {
  return (data, searchText) => searchText && data ? data.getInjected(RestService).request({
    method: "GET",
    url: lookup.url || "",
    params: {
      [lookup.filterParamName || ""]: searchText
    }
  }, {
    apiName: "Default"
  }).pipe(map((response) => {
    const list = response[lookup.resultListPropertyName || ""];
    const mapToOption = (item) => ({
      key: item[lookup.displayPropertyName || ""],
      value: item[lookup.valuePropertyName || ""]
    });
    return list.map(mapToOption);
  })) : of([]);
}
function getTypeaheadType(lookup, name) {
  if (!lookup.url) {
    return name.endsWith(TYPEAHEAD_TEXT_SUFFIX) ? "hidden" : void 0;
  } else {
    return "typeahead";
  }
}
function createTypeaheadDisplayNameGenerator(displayNameGeneratorFn, properties) {
  return (displayName, fallback) => {
    const name = removeTypeaheadTextSuffix(fallback.name || "");
    return displayNameGeneratorFn(displayName || properties[name].displayName, {
      name,
      resource: fallback.resource
    });
  };
}
function addTypeaheadTextSuffix(name) {
  return name + TYPEAHEAD_TEXT_SUFFIX;
}
function hasTypeaheadTextSuffix(name) {
  return TYPEAHEAD_TEXT_SUFFIX_REGEX.test(name);
}
function removeTypeaheadTextSuffix(name) {
  return name.replace(TYPEAHEAD_TEXT_SUFFIX_REGEX, "");
}
var ExtensibleFormPropService = class _ExtensibleFormPropService {
  constructor() {
    this.#configStateService = inject(ConfigStateService);
    this.meridian$ = this.#configStateService.getDeep$("localization.currentCulture.dateTimeFormat.shortTimePattern").pipe(map((shortTimePattern) => (shortTimePattern || "").includes("tt")));
  }
  #configStateService;
  isRequired(validator) {
    return validator === Validators.required || validator === AbpValidators.required || validator.name === "required";
  }
  getComponent(prop) {
    if (prop.template) {
      return "template";
    }
    switch (prop.type) {
      case "boolean":
        return "checkbox";
      case "date":
        return "date";
      case "datetime":
        return "dateTime";
      case "hidden":
        return "hidden";
      case "multiselect":
        return "multiselect";
      case "text":
        return "textarea";
      case "time":
        return "time";
      case "typeahead":
        return "typeahead";
      case "passwordinputgroup":
        return "passwordinputgroup";
      default:
        return prop.options ? "select" : "input";
    }
  }
  getType(prop) {
    switch (prop.type) {
      case "date":
      case "string":
        return "text";
      case "boolean":
        return "checkbox";
      case "number":
        return "number";
      case "email":
        return "email";
      case "password":
        return "password";
      case "passwordinputgroup":
        return "passwordinputgroup";
      default:
        return "hidden";
    }
  }
  calcAsterisks(validators) {
    if (!validators) return "";
    const required2 = validators.find((v) => this.isRequired(v));
    return required2 ? "*" : "";
  }
  static {
    this.ɵfac = function ExtensibleFormPropService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensibleFormPropService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ExtensibleFormPropService,
      factory: _ExtensibleFormPropService.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensibleFormPropService, [{
    type: Injectable
  }], null, null);
})();
var CreateInjectorPipe = class _CreateInjectorPipe {
  transform(_, action, context) {
    const get = (token, notFoundValue, options) => {
      const componentData = context.getData();
      const componentDataCallback = (data) => {
        data = data ?? context.getData();
        return action.action(data);
      };
      let extensionData;
      switch (token) {
        case EXTENSIONS_ACTION_DATA:
          extensionData = componentData;
          break;
        case EXTENSIONS_ACTION_CALLBACK:
          extensionData = componentDataCallback;
          break;
        default:
          extensionData = context.getInjected.call(context.injector, token, notFoundValue, options);
      }
      return extensionData;
    };
    return {
      get
    };
  }
  static {
    this.ɵfac = function CreateInjectorPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _CreateInjectorPipe)();
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "createInjector",
      type: _CreateInjectorPipe,
      pure: true,
      standalone: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CreateInjectorPipe, [{
    type: Pipe,
    args: [{
      name: "createInjector",
      standalone: true
    }]
  }], null, null);
})();
var ExtensibleFormPropComponent = class _ExtensibleFormPropComponent {
  constructor() {
    this.service = inject(ExtensibleFormPropService);
    this.cdRef = inject(ChangeDetectorRef);
    this.track = inject(TrackByService);
    this.#groupDirective = inject(FormGroupDirective);
    this.injector = inject(Injector);
    this.form = this.#groupDirective.form;
    this.asterisk = "";
    this.containerClassName = "mb-2";
    this.showPassword = false;
    this.options$ = of([]);
    this.validators = [];
    this.passwordKey = "ThemeShared.Extensions.PasswordComponent";
    this.disabledFn = (data) => false;
    this.search = (text$) => text$ ? text$.pipe(debounceTime(300), distinctUntilChanged(), switchMap((text) => this.prop?.options?.(this.data, text) || of([]))) : of([]);
    this.typeaheadFormatter = (option) => option.key;
    this.meridian$ = this.service.meridian$;
  }
  #groupDirective;
  get disabled() {
    return this.disabledFn(this.data);
  }
  setTypeaheadValue(selectedOption) {
    this.typeaheadModel = selectedOption || {
      key: null,
      value: null
    };
    const {
      key,
      value
    } = this.typeaheadModel;
    const [keyControl, valueControl] = this.getTypeaheadControls();
    if (valueControl?.value && !value) valueControl.markAsDirty();
    keyControl?.setValue(key);
    valueControl?.setValue(value);
  }
  get isInvalid() {
    const control = this.form.get(this.prop.name);
    return control?.touched && control.invalid;
  }
  getTypeaheadControls() {
    const {
      name
    } = this.prop;
    const extraPropName = `${EXTRA_PROPERTIES_KEY}.${name}`;
    const keyControl = this.form.get(addTypeaheadTextSuffix(extraPropName)) || this.form.get(addTypeaheadTextSuffix(name));
    const valueControl = this.form.get(extraPropName) || this.form.get(name);
    return [keyControl, valueControl];
  }
  setAsterisk() {
    this.asterisk = this.service.calcAsterisks(this.validators);
  }
  ngAfterViewInit() {
    if (this.isFirstGroup && this.first && this.fieldRef) {
      this.fieldRef.nativeElement.focus();
    }
  }
  getComponent(prop) {
    return this.service.getComponent(prop);
  }
  getType(prop) {
    return this.service.getType(prop);
  }
  ngOnChanges({
    prop,
    data
  }) {
    const currentProp = prop?.currentValue;
    const {
      options,
      readonly,
      disabled,
      validators,
      className,
      template
    } = currentProp || {};
    if (template) {
      this.injectorForCustomComponent = Injector.create({
        providers: [{
          provide: EXTENSIONS_FORM_PROP,
          useValue: currentProp
        }, {
          provide: EXTENSIONS_FORM_PROP_DATA,
          useValue: data?.currentValue?.record
        }, {
          provide: ControlContainer,
          useExisting: FormGroupDirective
        }],
        parent: this.injector
      });
    }
    if (options) this.options$ = options(this.data);
    if (readonly) this.readonly = readonly(this.data);
    if (disabled) {
      this.disabledFn = disabled;
    }
    if (validators) {
      this.validators = validators(this.data);
      this.setAsterisk();
    }
    if (className !== void 0) {
      this.containerClassName = className;
    }
    const [keyControl, valueControl] = this.getTypeaheadControls();
    if (keyControl && valueControl) this.typeaheadModel = {
      key: keyControl.value,
      value: valueControl.value
    };
  }
  static {
    this.ɵfac = function ExtensibleFormPropComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensibleFormPropComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ExtensibleFormPropComponent,
      selectors: [["abp-extensible-form-prop"]],
      viewQuery: function ExtensibleFormPropComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(_c0, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.fieldRef = _t.first);
        }
      },
      inputs: {
        data: "data",
        prop: "prop",
        first: "first",
        isFirstGroup: "isFirstGroup"
      },
      standalone: true,
      features: [ɵɵProvidersFeature([ExtensibleFormPropService], [{
        provide: ControlContainer,
        useFactory: selfFactory,
        deps: [[new Optional(), new SkipSelf(), ControlContainer]]
      }, {
        provide: NgbDateAdapter,
        useClass: DateAdapter
      }, {
        provide: NgbTimeAdapter,
        useClass: TimeAdapter
      }]), ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      decls: 3,
      vars: 2,
      consts: [["label", ""], ["field", ""], ["typeahead", ""], ["datepicker", "ngbDatepicker"], [4, "abpPermission", "abpPermissionRunChangeDetection"], [1, "mb-2", 3, "ngClass"], ["type", "hidden", 3, "formControlName"], ["validationTarget", "", 1, "form-check"], [1, "text-muted", "d-block"], [4, "ngComponentOutlet", "ngComponentOutletInjector"], [3, "ngTemplateOutlet"], [1, "form-control", 3, "id", "formControlName", "autocomplete", "type", "abpDisabled", "readonly"], ["type", "checkbox", 1, "form-check-input", 3, "id", "formControlName", "abpDisabled"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"], [1, "form-select", "form-control", 3, "id", "formControlName", "abpDisabled"], [3, "ngValue"], ["multiple", "multiple", 1, "form-select", "form-control", 3, "id", "formControlName", "abpDisabled"], ["validationStyle", "", "validationTarget", "", 1, "position-relative"], [1, "form-control", 3, "ngModelChange", "selectItem", "blur", "id", "autocomplete", "abpDisabled", "ngbTypeahead", "editable", "inputFormatter", "resultFormatter", "ngModelOptions", "ngModel"], ["ngbDatepicker", "", "type", "text", 1, "form-control", 3, "click", "keyup.space", "id", "formControlName"], [3, "formControlName"], [3, "prop", "meridian"], [1, "form-control", 3, "id", "formControlName", "abpDisabled", "readonly"], ["validationTarget", "", 1, "input-group", "form-group"], [1, "form-control", 3, "id", "formControlName", "abpShowPassword"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["aria-hidden", "true", 1, "fa", 3, "ngClass"], [3, "htmlFor", "ngClass"], ["container", "body", 1, "bi", "bi-info-circle", 3, "ngbTooltip", "placement"]],
      template: function ExtensibleFormPropComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, ExtensibleFormPropComponent_ng_container_0_Template, 15, 4, "ng-container", 4)(1, ExtensibleFormPropComponent_ng_template_1_Template, 5, 5, "ng-template", null, 0, ɵɵtemplateRefExtractor);
        }
        if (rf & 2) {
          ɵɵproperty("abpPermission", ctx.prop.permission)("abpPermissionRunChangeDetection", false);
        }
      },
      dependencies: [ExtensibleDateTimePickerComponent, NgbDatepickerModule, NgbInputDatepicker, NgbTimepickerModule, NgbTimepicker, ReactiveFormsModule, NgSelectOption, ɵNgSelectMultipleOption, DefaultValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, SelectMultipleControlValueAccessor, NgControlStatus, FormControlName, DisabledDirective, NgxValidateCoreModule, ValidationStyleDirective, ValidationTargetDirective, ValidationDirective, NgbTooltip, NgbTypeaheadModule, NgbTypeahead, ShowPasswordDirective, PermissionDirective, LocalizationModule, LocalizationPipe, CommonModule, NgClass, NgComponentOutlet, NgTemplateOutlet, AsyncPipe, FormsModule, NgModel],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensibleFormPropComponent, [{
    type: Component,
    args: [{
      selector: "abp-extensible-form-prop",
      standalone: true,
      imports: [ExtensibleDateTimePickerComponent, NgbDatepickerModule, NgbTimepickerModule, ReactiveFormsModule, DisabledDirective, NgxValidateCoreModule, NgbTooltip, NgbTypeaheadModule, CreateInjectorPipe, ShowPasswordDirective, PermissionDirective, LocalizationModule, CommonModule, FormsModule],
      changeDetection: ChangeDetectionStrategy.OnPush,
      providers: [ExtensibleFormPropService],
      viewProviders: [{
        provide: ControlContainer,
        useFactory: selfFactory,
        deps: [[new Optional(), new SkipSelf(), ControlContainer]]
      }, {
        provide: NgbDateAdapter,
        useClass: DateAdapter
      }, {
        provide: NgbTimeAdapter,
        useClass: TimeAdapter
      }],
      template: `<ng-container *abpPermission="prop.permission; runChangeDetection: false">\r
  @switch (getComponent(prop)) {\r
    @case ('template') {\r
      <ng-container *ngComponentOutlet="prop.template; injector: injectorForCustomComponent">\r
      </ng-container>\r
    }\r
  }\r
\r
  <div [ngClass]="containerClassName" class="mb-2">\r
    @switch (getComponent(prop)) {\r
      @case ('input') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <input\r
          #field\r
          [id]="prop.id"\r
          [formControlName]="prop.name"\r
          [autocomplete]="prop.autocomplete"\r
          [type]="getType(prop)"\r
          [abpDisabled]="disabled"\r
          [readonly]="readonly"\r
          class="form-control"\r
        />\r
      }\r
      @case ('hidden') {\r
        <input [formControlName]="prop.name" type="hidden" />\r
      }\r
      @case ('checkbox') {\r
        <div class="form-check" validationTarget>\r
          <input\r
            #field\r
            [id]="prop.id"\r
            [formControlName]="prop.name"\r
            [abpDisabled]="disabled"\r
            type="checkbox"\r
            class="form-check-input"\r
          />\r
          <ng-template\r
            [ngTemplateOutlet]="label"\r
            [ngTemplateOutletContext]="{ $implicit: 'form-check-label' }"\r
          ></ng-template>\r
        </div>\r
      }\r
      @case ('select') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <select\r
          #field\r
          [id]="prop.id"\r
          [formControlName]="prop.name"\r
          [abpDisabled]="disabled"\r
          class="form-select form-control"\r
        >\r
          @for (option of options$ | async; track option.value) {\r
            <option [ngValue]="option.value">\r
              @if (prop.isExtra) {\r
                {{ '::' + option.key | abpLocalization }}\r
              } @else {\r
                {{ option.key }}\r
              }\r
            </option>\r
          }\r
        </select>\r
      }\r
      @case ('multiselect') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <select\r
          #field\r
          [id]="prop.id"\r
          [formControlName]="prop.name"\r
          [abpDisabled]="disabled"\r
          multiple="multiple"\r
          class="form-select form-control"\r
        >\r
          @for (option of options$ | async; track option.value) {\r
            <option [ngValue]="option.value">\r
              @if (prop.isExtra) {\r
                {{ '::' + option.key | abpLocalization }}\r
              } @else {\r
                {{ option.key }}\r
              }\r
            </option>\r
          }\r
        </select>\r
      }\r
      @case ('typeahead') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <div #typeahead class="position-relative" validationStyle validationTarget>\r
          <input\r
            #field\r
            [id]="prop.id"\r
            [autocomplete]="prop.autocomplete"\r
            [abpDisabled]="disabled"\r
            [ngbTypeahead]="search"\r
            [editable]="false"\r
            [inputFormatter]="typeaheadFormatter"\r
            [resultFormatter]="typeaheadFormatter"\r
            [ngModelOptions]="{ standalone: true }"\r
            [(ngModel)]="typeaheadModel"\r
            (selectItem)="setTypeaheadValue($event.item)"\r
            (blur)="setTypeaheadValue(typeaheadModel)"\r
            [class.is-invalid]="typeahead.classList.contains('is-invalid')"\r
            class="form-control"\r
          />\r
          <input [formControlName]="prop.name" type="hidden" />\r
        </div>\r
      }\r
      @case ('date') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <input\r
          [id]="prop.id"\r
          [formControlName]="prop.name"\r
          (click)="datepicker.open()"\r
          (keyup.space)="datepicker.open()"\r
          ngbDatepicker\r
          #datepicker="ngbDatepicker"\r
          type="text"\r
          class="form-control"\r
        />\r
      }\r
      @case ('time') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <ngb-timepicker [formControlName]="prop.name"></ngb-timepicker>\r
      }\r
      @case ('dateTime') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <abp-extensible-date-time-picker [prop]="prop" [meridian]="meridian$ | async" />\r
      }\r
      @case ('textarea') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <textarea\r
          #field\r
          [id]="prop.id"\r
          [formControlName]="prop.name"\r
          [abpDisabled]="disabled"\r
          [readonly]="readonly"\r
          class="form-control"\r
        ></textarea>\r
      }\r
      @case ('passwordinputgroup') {\r
        <ng-template [ngTemplateOutlet]="label"></ng-template>\r
        <div class="input-group form-group" validationTarget>\r
          <input\r
            class="form-control"\r
            [id]="prop.id"\r
            [formControlName]="prop.name"\r
            [abpShowPassword]="showPassword"\r
          />\r
          <button class="btn btn-secondary" type="button" (click)="showPassword = !showPassword">\r
            <i\r
              class="fa"\r
              aria-hidden="true"\r
              [ngClass]="{\r
                'fa-eye-slash': !showPassword,\r
                'fa-eye': showPassword,\r
              }"\r
            ></i>\r
          </button>\r
        </div>\r
      }\r
    }\r
\r
    @if (prop.formText) {\r
      <small class="text-muted d-block">{{ prop.formText | abpLocalization }}</small>\r
    }\r
  </div>\r
</ng-container>\r
\r
<ng-template #label let-classes>\r
  <label [htmlFor]="prop.id" [ngClass]="classes || 'form-label'">\r
    @if (prop.displayTextResolver) {\r
      {{ prop.displayTextResolver(data) | abpLocalization }}\r
    } @else {\r
      @if (prop.isExtra) {\r
        {{ '::' + prop.displayName | abpLocalization }}\r
      } @else {\r
        {{ prop.displayName | abpLocalization }}\r
      }\r
    }\r
    {{ asterisk }}\r
    @if (prop.tooltip) {\r
      <i\r
        [ngbTooltip]="prop.tooltip.text | abpLocalization"\r
        [placement]="prop.tooltip.placement || 'auto'"\r
        container="body"\r
        class="bi bi-info-circle"\r
      ></i>\r
    }\r
  </label>\r
</ng-template>\r
`
    }]
  }], null, {
    data: [{
      type: Input
    }],
    prop: [{
      type: Input
    }],
    first: [{
      type: Input
    }],
    isFirstGroup: [{
      type: Input
    }],
    fieldRef: [{
      type: ViewChild,
      args: ["field"]
    }]
  });
})();
var ActionList = class extends LinkedList {
};
var ActionData = class {
  get data() {
    return {
      getInjected: this.getInjected,
      index: this.index,
      record: this.record
    };
  }
};
var ActionsFactory = class {
  constructor() {
    this.contributorCallbacks = {};
  }
  get(name) {
    this.contributorCallbacks[name] = this.contributorCallbacks[name] || [];
    return new this._ctor(this.contributorCallbacks[name]);
  }
};
var Actions = class {
  get actions() {
    const actionList = new this._ctor();
    this.callbackList.forEach((callback) => callback(actionList));
    return actionList;
  }
  constructor(callbackList) {
    this.callbackList = callbackList;
  }
  addContributor(contributeCallback) {
    this.callbackList.push(contributeCallback);
  }
  clearContributors() {
    while (this.callbackList.length) this.callbackList.pop();
  }
};
var EntityActionList = class extends ActionList {
};
var EntityActions = class extends Actions {
  constructor() {
    super(...arguments);
    this._ctor = EntityActionList;
  }
};
var EntityActionsFactory = class extends ActionsFactory {
  constructor() {
    super(...arguments);
    this._ctor = EntityActions;
  }
};
var EntityPropList = class extends PropList {
};
var EntityProps = class extends Props {
  constructor() {
    super(...arguments);
    this._ctor = EntityPropList;
  }
};
var EntityPropsFactory = class extends PropsFactory {
  constructor() {
    super(...arguments);
    this._ctor = EntityProps;
  }
};
var EntityProp = class _EntityProp extends Prop {
  constructor(options) {
    super(options.type, options.name, options.displayName || "", options.permission || "", options.visible, options.isExtra);
    this.columnVisible = options.columnVisible || (() => true);
    this.columnWidth = options.columnWidth;
    this.sortable = options.sortable || false;
    this.valueResolver = options.valueResolver || ((data) => of(escapeHtmlChars(data.record[this.name])));
    if (options.action) {
      this.action = options.action;
    }
    if (options.component) {
      this.component = options.component;
    }
    if (options.enumList) {
      this.enumList = options.enumList;
    }
    this.tooltip = options.tooltip;
  }
  static create(options) {
    return new _EntityProp(options);
  }
  static createMany(arrayOfOptions) {
    return arrayOfOptions.map(_EntityProp.create);
  }
};
var ToolbarActionList = class extends ActionList {
};
var ToolbarActions = class extends Actions {
  constructor() {
    super(...arguments);
    this._ctor = ToolbarActionList;
  }
};
var ToolbarActionsFactory = class extends ActionsFactory {
  constructor() {
    super(...arguments);
    this._ctor = ToolbarActions;
  }
};
var ExtensionsService = class _ExtensionsService {
  constructor() {
    this.entityActions = new EntityActionsFactory();
    this.toolbarActions = new ToolbarActionsFactory();
    this.entityProps = new EntityPropsFactory();
    this.createFormProps = new CreateFormPropsFactory();
    this.editFormProps = new EditFormPropsFactory();
  }
  static {
    this.ɵfac = function ExtensionsService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensionsService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ExtensionsService,
      factory: _ExtensionsService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensionsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var PropDataDirective = class _PropDataDirective extends PropData {
  constructor(tempRef, vcRef, injector) {
    super();
    this.tempRef = tempRef;
    this.vcRef = vcRef;
    this.getInjected = injector.get.bind(injector);
  }
  ngOnChanges() {
    this.vcRef.clear();
    this.vcRef.createEmbeddedView(this.tempRef, {
      $implicit: this.data,
      index: 0
    });
  }
  ngOnDestroy() {
    this.vcRef.clear();
  }
  static {
    this.ɵfac = function PropDataDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PropDataDirective)(ɵɵdirectiveInject(TemplateRef), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Injector));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _PropDataDirective,
      selectors: [["", "abpPropData", ""]],
      inputs: {
        propList: [0, "abpPropDataFromList", "propList"],
        record: [0, "abpPropDataWithRecord", "record"],
        index: [0, "abpPropDataAtIndex", "index"]
      },
      exportAs: ["abpPropData"],
      standalone: true,
      features: [ɵɵInheritDefinitionFeature, ɵɵNgOnChangesFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PropDataDirective, [{
    type: Directive,
    args: [{
      exportAs: "abpPropData",
      selector: "[abpPropData]",
      standalone: true
    }]
  }], () => [{
    type: TemplateRef
  }, {
    type: ViewContainerRef
  }, {
    type: Injector
  }], {
    propList: [{
      type: Input,
      args: ["abpPropDataFromList"]
    }],
    record: [{
      type: Input,
      args: ["abpPropDataWithRecord"]
    }],
    index: [{
      type: Input,
      args: ["abpPropDataAtIndex"]
    }]
  });
})();
var ExtensibleFormComponent = class _ExtensibleFormComponent {
  constructor() {
    this.cdRef = inject(ChangeDetectorRef);
    this.track = inject(TrackByService);
    this.container = inject(ControlContainer);
    this.extensions = inject(ExtensionsService);
    this.identifier = inject(EXTENSIONS_IDENTIFIER);
    this.extraPropertiesKey = EXTRA_PROPERTIES_KEY;
  }
  set selectedRecord(record) {
    const type = !record || JSON.stringify(record) === "{}" ? "create" : "edit";
    const propList = this.extensions[`${type}FormProps`].get(this.identifier).props;
    this.groupedPropList = this.createGroupedList(propList);
    this.record = record;
  }
  get form() {
    return this.container ? this.container.control : {
      controls: {}
    };
  }
  get extraProperties() {
    return this.form.controls.extraProperties || {
      controls: {}
    };
  }
  createGroupedList(propList) {
    const groupedFormPropList = new GroupedFormPropList();
    propList.forEach((item) => {
      groupedFormPropList.addItem(item.value);
    });
    return groupedFormPropList;
  }
  //TODO: Reactor this method
  isAnyGroupMemberVisible(index, data) {
    const {
      items
    } = this.groupedPropList;
    const formPropList = items[index].formPropList.toArray();
    return formPropList.some((prop) => prop.visible(data));
  }
  static {
    this.ɵfac = function ExtensibleFormComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensibleFormComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ExtensibleFormComponent,
      selectors: [["abp-extensible-form"]],
      viewQuery: function ExtensibleFormComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(ExtensibleFormPropComponent, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.formProps = _t);
        }
      },
      inputs: {
        selectedRecord: "selectedRecord"
      },
      exportAs: ["abpExtensibleForm"],
      standalone: true,
      features: [ɵɵProvidersFeature([], [{
        provide: ControlContainer,
        useFactory: selfFactory,
        deps: [[new Optional(), new SkipSelf(), ControlContainer]]
      }]), ɵɵStandaloneFeature],
      decls: 3,
      vars: 1,
      consts: [["propListTemplate", ""], [4, "abpPropData", "abpPropDataFromList", "abpPropDataWithRecord"], [3, "ngClass"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"], [3, "formGroupName"], [3, "prop", "data"], [3, "class", "prop", "data", "first", "isFirstGroup"], [3, "prop", "data", "first", "isFirstGroup"]],
      template: function ExtensibleFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, ExtensibleFormComponent_Conditional_0_Template, 2, 0)(1, ExtensibleFormComponent_ng_template_1_Template, 2, 0, "ng-template", null, 0, ɵɵtemplateRefExtractor);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.form ? 0 : -1);
        }
      },
      dependencies: [CommonModule, NgClass, NgTemplateOutlet, PropDataDirective, ReactiveFormsModule, NgControlStatusGroup, FormGroupName, ExtensibleFormPropComponent],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensibleFormComponent, [{
    type: Component,
    args: [{
      standalone: true,
      exportAs: "abpExtensibleForm",
      selector: "abp-extensible-form",
      imports: [CommonModule, PropDataDirective, ReactiveFormsModule, ExtensibleFormPropComponent],
      changeDetection: ChangeDetectionStrategy.OnPush,
      viewProviders: [{
        provide: ControlContainer,
        useFactory: selfFactory,
        deps: [[new Optional(), new SkipSelf(), ControlContainer]]
      }],
      template: '@if (form) {\r\n  @for (groupedProp of groupedPropList.items; track i; let i = $index; let first = $first) {\r\n    <ng-container *abpPropData="let data; fromList: groupedProp.formPropList; withRecord: record">\r\n      @if (isAnyGroupMemberVisible(i, data) && groupedProp.group?.className) {\r\n        <div\r\n          [ngClass]="groupedProp.group?.className"\r\n          [attr.data-name]="groupedProp.group?.name || groupedProp.group?.className"\r\n        >\r\n          <ng-container\r\n            [ngTemplateOutlet]="propListTemplate"\r\n            [ngTemplateOutletContext]="{ groupedProp: groupedProp, data: data, isFirstGroup: first}"\r\n          >\r\n          </ng-container>\r\n        </div>\r\n      } @else {\r\n        <ng-container\r\n          [ngTemplateOutlet]="propListTemplate"\r\n          [ngTemplateOutletContext]="{ groupedProp: groupedProp, data: data, isFirstGroup: first }"\r\n        >\r\n        </ng-container>\r\n      }\r\n    </ng-container>\r\n  }\r\n}\r\n\r\n<ng-template let-groupedProp="groupedProp" let-data="data" let-isFirstGroup="isFirstGroup" #propListTemplate>\r\n  @for (prop of groupedProp.formPropList; let index = $index; let first = $first; track prop.name) {\r\n    @if (prop.visible(data)) {\r\n      @if (extraProperties.controls[prop.name]) {\r\n        <ng-container [formGroupName]="extraPropertiesKey">\r\n          <abp-extensible-form-prop [prop]="prop" [data]="data" [class]="prop.className" />\r\n        </ng-container>\r\n      } @else {\r\n        @if (form.get(prop.name)) {\r\n          <abp-extensible-form-prop\r\n            [class]="prop.className"\r\n            [prop]="prop"\r\n            [data]="data"\r\n            [first]="first"\r\n            [isFirstGroup]="isFirstGroup"\r\n          />\r\n        }\r\n      }\r\n    }\r\n  }\r\n</ng-template>\r\n'
    }]
  }], null, {
    formProps: [{
      type: ViewChildren,
      args: [ExtensibleFormPropComponent]
    }],
    selectedRecord: [{
      type: Input
    }]
  });
})();
var AbstractActionsComponent = class _AbstractActionsComponent extends ActionData {
  constructor(injector) {
    super();
    this.getInjected = injector.get.bind(injector);
    const extensions = injector.get(ExtensionsService);
    const name = injector.get(EXTENSIONS_IDENTIFIER);
    const type = injector.get(EXTENSIONS_ACTION_TYPE);
    this.actionList = extensions[type].get(name).actions;
  }
  static {
    this.ɵfac = function AbstractActionsComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AbstractActionsComponent)(ɵɵdirectiveInject(Injector));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _AbstractActionsComponent,
      inputs: {
        record: "record"
      },
      features: [ɵɵInheritDefinitionFeature]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AbstractActionsComponent, [{
    type: Directive
  }], () => [{
    type: Injector
  }], {
    record: [{
      type: Input
    }]
  });
})();
var GridActionsComponent = class _GridActionsComponent extends AbstractActionsComponent {
  constructor(injector) {
    super(injector);
    this.icon = "fa fa-cog";
    this.text = "";
    this.trackByFn = (_, item) => item.text;
  }
  static {
    this.ɵfac = function GridActionsComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _GridActionsComponent)(ɵɵdirectiveInject(Injector));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _GridActionsComponent,
      selectors: [["abp-grid-actions"]],
      inputs: {
        icon: "icon",
        index: "index",
        text: "text"
      },
      exportAs: ["abpGridActions"],
      standalone: true,
      features: [ɵɵProvidersFeature([{
        provide: EXTENSIONS_ACTION_TYPE,
        useValue: "entityActions"
      }]), ɵɵInheritDefinitionFeature, ɵɵStandaloneFeature],
      decls: 8,
      vars: 2,
      consts: [["dropDownBtnItemTmp", ""], ["buttonContentTmp", ""], ["btnTmp", ""], ["ngbDropdown", "", "container", "body", 1, "d-inline-block"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"], ["data-toggle", "dropdown", "aria-haspopup", "true", "ngbDropdownToggle", "", 1, "btn", "btn-primary", "btn-sm", "dropdown-toggle"], [3, "ngClass"], ["ngbDropdownMenu", ""], ["ngbDropdownItem", "", "type", "button"], ["ngbDropdownItem", "", "type", "button", 3, "click", 4, "abpPermission", "abpPermissionRunChangeDetection"], ["ngbDropdownItem", "", "type", "button", 3, "click"], [4, "ngTemplateOutlet", "ngTemplateOutletContext"], ["abpEllipsis", ""], ["type", "button", "triggers", "hover", "container", "body", 3, "class", "style", "ngbTooltip", "placement"], ["type", "button", 3, "class", "style"], ["type", "button", "triggers", "hover", "container", "body", 3, "class", "style", "ngbTooltip", "placement", "click", 4, "abpPermission", "abpPermissionRunChangeDetection"], ["type", "button", "triggers", "hover", "container", "body", 3, "click", "ngbTooltip", "placement"], ["type", "button", 3, "class", "style", "click", 4, "abpPermission", "abpPermissionRunChangeDetection"], ["type", "button", 3, "click"]],
      template: function GridActionsComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, GridActionsComponent_Conditional_0_Template, 8, 6, "div", 3)(1, GridActionsComponent_Conditional_1_Template, 1, 4, "ng-container", 4)(2, GridActionsComponent_ng_template_2_Template, 1, 1, "ng-template", null, 0, ɵɵtemplateRefExtractor)(4, GridActionsComponent_ng_template_4_Template, 2, 4, "ng-template", null, 1, ɵɵtemplateRefExtractor)(6, GridActionsComponent_ng_template_6_Template, 1, 1, "ng-template", null, 2, ɵɵtemplateRefExtractor);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.actionList.length > 1 ? 0 : -1);
          ɵɵadvance();
          ɵɵconditional(ctx.actionList.length === 1 ? 1 : -1);
        }
      },
      dependencies: [NgbDropdownModule, NgbDropdown, NgbDropdownToggle, NgbDropdownMenu, NgbDropdownItem, NgbDropdownButtonItem, EllipsisDirective, PermissionDirective, NgClass, LocalizationModule, LocalizationPipe, NgTemplateOutlet, NgbTooltipModule, NgbTooltip],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GridActionsComponent, [{
    type: Component,
    args: [{
      exportAs: "abpGridActions",
      standalone: true,
      imports: [NgbDropdownModule, EllipsisDirective, PermissionDirective, NgClass, LocalizationModule, NgTemplateOutlet, NgbTooltipModule],
      selector: "abp-grid-actions",
      providers: [{
        provide: EXTENSIONS_ACTION_TYPE,
        useValue: "entityActions"
      }],
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `@if (actionList.length > 1) {\r
  <div ngbDropdown container="body" class="d-inline-block">\r
    <button\r
      class="btn btn-primary btn-sm dropdown-toggle"\r
      data-toggle="dropdown"\r
      aria-haspopup="true"\r
      ngbDropdownToggle\r
    >\r
      <i [ngClass]="icon" [class.me-1]="icon"></i>{{ text | abpLocalization }}\r
    </button>\r
    <div ngbDropdownMenu>\r
      @for (action of actionList; track action.text) {\r
        <ng-container\r
          [ngTemplateOutlet]="dropDownBtnItemTmp"\r
          [ngTemplateOutletContext]="{ $implicit: action }"\r
        >\r
        </ng-container>\r
      }\r
    </div>\r
  </div>\r
}\r
\r
@if (actionList.length === 1) {\r
  <ng-container\r
    [ngTemplateOutlet]="btnTmp"\r
    [ngTemplateOutletContext]="{ $implicit: actionList.get(0).value }"\r
  ></ng-container>\r
}\r
\r
<ng-template #dropDownBtnItemTmp let-action>\r
  @if (action.visible(data)) {\r
    <button\r
      ngbDropdownItem\r
      *abpPermission="action.permission; runChangeDetection: false"\r
      (click)="action.action(data)"\r
      type="button"\r
    >\r
      <ng-container\r
        *ngTemplateOutlet="buttonContentTmp; context: { $implicit: action }"\r
      ></ng-container>\r
    </button>\r
  }\r
</ng-template>\r
\r
<ng-template #buttonContentTmp let-action>\r
  <i [ngClass]="action.icon" [class.me-1]="action.icon && !action.showOnlyIcon"></i>\r
  @if (!action.showOnlyIcon) {\r
    @if (action.icon) {\r
      <span>{{ action.text | abpLocalization }}</span>\r
    } @else {\r
      <div abpEllipsis>{{ action.text | abpLocalization }}</div>\r
    }\r
  }\r
</ng-template>\r
\r
<ng-template #btnTmp let-action>\r
  @if (action.visible(data)) {\r
    @if (action.tooltip) {\r
      <button\r
        *abpPermission="action.permission; runChangeDetection: false"\r
        (click)="action.action(data)"\r
        type="button"\r
        [class]="action.btnClass"\r
        [style]="action.btnStyle"\r
        [ngbTooltip]="action.tooltip.text | abpLocalization"\r
        [placement]="action.tooltip.placement || 'auto'"\r
        triggers="hover"\r
        container="body"\r
      >\r
        <ng-container\r
          *ngTemplateOutlet="buttonContentTmp; context: { $implicit: action }"\r
        ></ng-container>\r
      </button>\r
    } @else {\r
      <button\r
        *abpPermission="action.permission; runChangeDetection: false"\r
        (click)="action.action(data)"\r
        type="button"\r
        [class]="action.btnClass"\r
        [style]="action.btnStyle"\r
      >\r
        <ng-container\r
          *ngTemplateOutlet="buttonContentTmp; context: { $implicit: action }"\r
        ></ng-container>\r
      </button>\r
    }\r
  }\r
</ng-template>\r
`
    }]
  }], () => [{
    type: Injector
  }], {
    icon: [{
      type: Input
    }],
    index: [{
      type: Input
    }],
    text: [{
      type: Input
    }]
  });
})();
var DEFAULT_ACTIONS_COLUMN_WIDTH = 150;
var ExtensibleTableComponent = class _ExtensibleTableComponent {
  set actionsText(value) {
    this._actionsText = value;
  }
  get actionsText() {
    return this._actionsText ?? (this.actionList.length > 1 ? "AbpUi::Actions" : "");
  }
  set actionsColumnWidth(width) {
    this.setColumnWidths(width ? Number(width) : void 0);
  }
  #injector;
  constructor() {
    this.tableActivate = new EventEmitter();
    this.trackByFn = (_, item) => item.name;
    this.locale = inject(LOCALE_ID);
    this.config = inject(ConfigStateService);
    this.entityPropTypeClasses = inject(ENTITY_PROP_TYPE_CLASSES);
    this.#injector = inject(Injector);
    this.getInjected = this.#injector.get.bind(this.#injector);
    this.permissionService = this.#injector.get(PermissionService);
    const extensions = this.#injector.get(ExtensionsService);
    const name = this.#injector.get(EXTENSIONS_IDENTIFIER);
    this.propList = extensions.entityProps.get(name).props;
    this.actionList = extensions["entityActions"].get(name).actions;
    this.hasAtLeastOnePermittedAction = this.permissionService.filterItemsByPolicy(this.actionList.toArray().map((action) => ({
      requiredPolicy: action.permission
    }))).length > 0;
    this.setColumnWidths(DEFAULT_ACTIONS_COLUMN_WIDTH);
  }
  setColumnWidths(actionsColumn) {
    const widths = [actionsColumn];
    this.propList.forEach(({
      value: prop
    }) => {
      widths.push(prop.columnWidth);
    });
    this.columnWidths = widths;
  }
  getDate(value, format) {
    return value && format ? formatDate(value, format, this.locale) : "";
  }
  getIcon(value) {
    return value ? '<div class="text-success"><i class="fa fa-check" aria-hidden="true"></i></div>' : '<div class="text-danger"><i class="fa fa-times" aria-hidden="true"></i></div>';
  }
  getEnum(rowValue, list) {
    if (!list || list.length < 1) return rowValue;
    const {
      key
    } = list.find(({
      value
    }) => value === rowValue) || {};
    return key;
  }
  getContent(prop, data) {
    return prop.valueResolver(data).pipe(map((value) => {
      switch (prop.type) {
        case "boolean":
          return this.getIcon(value);
        case "date":
          return this.getDate(value, getShortDateFormat(this.config));
        case "time":
          return this.getDate(value, getShortTimeFormat(this.config));
        case "datetime":
          return this.getDate(value, getShortDateShortTimeFormat(this.config));
        case "enum":
          return this.getEnum(value, prop.enumList || []);
        default:
          return value;
      }
    }));
  }
  ngOnChanges({
    data
  }) {
    if (!data?.currentValue) return;
    if (data.currentValue.length < 1) {
      this.list.totalCount = this.recordsTotal;
    }
    this.data = data.currentValue.map((record, index) => {
      this.propList.forEach((prop) => {
        const propData = {
          getInjected: this.getInjected,
          record,
          index
        };
        const value = this.getContent(prop.value, propData);
        const propKey = `_${prop.value.name}`;
        record[propKey] = {
          visible: prop.value.visible(propData),
          value
        };
        if (prop.value.component) {
          record[propKey].injector = Injector.create({
            providers: [{
              provide: PROP_DATA_STREAM,
              useValue: value
            }],
            parent: this.#injector
          });
          record[propKey].component = prop.value.component;
        }
      });
      return record;
    });
  }
  isVisibleActions(rowData) {
    const actions = this.actionList.toArray();
    const visibleActions = actions.filter((action) => {
      const {
        visible,
        permission
      } = action;
      let isVisible = true;
      let hasPermission = true;
      if (visible) {
        isVisible = visible({
          record: rowData,
          getInjected: this.getInjected
        });
      }
      if (permission) {
        hasPermission = this.permissionService.getGrantedPolicy(permission);
      }
      return isVisible && hasPermission;
    });
    return visibleActions.length > 0;
  }
  static {
    this.ɵfac = function ExtensibleTableComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensibleTableComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ExtensibleTableComponent,
      selectors: [["abp-extensible-table"]],
      inputs: {
        actionsText: "actionsText",
        data: "data",
        list: "list",
        recordsTotal: "recordsTotal",
        actionsColumnWidth: "actionsColumnWidth",
        actionsTemplate: "actionsTemplate"
      },
      outputs: {
        tableActivate: "tableActivate"
      },
      exportAs: ["abpExtensibleTable"],
      standalone: true,
      features: [ɵɵNgOnChangesFeature, ɵɵStandaloneFeature],
      decls: 4,
      vars: 4,
      consts: [["gridActions", ""], ["default", "", 3, "activate", "rows", "count", "list"], [3, "name", "maxWidth", "width", "sortable"], [3, "width", "name", "prop", "sortable"], ["ngx-datatable-cell-template", ""], [4, "ngTemplateOutlet", "ngTemplateOutletContext"], ["text", "AbpUi::Actions", 3, "index", "record"], [3, "width", "name", "prop", "sortable", 4, "abpVisible"], ["ngx-datatable-header-template", ""], ["container", "body", 3, "ngbTooltip", "placement"], ["aria-hidden", "true", 1, "fa", "fa-info-circle"], [4, "abpPermission", "abpPermissionRunChangeDetection"], [4, "abpVisible"], [3, "innerHTML", "ngClass", "pointer"], [3, "click", "innerHTML", "ngClass"], [4, "ngComponentOutlet", "ngComponentOutletInjector"]],
      template: function ExtensibleTableComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "ngx-datatable", 1);
          ɵɵlistener("activate", function ExtensibleTableComponent_Template_ngx_datatable_activate_0_listener($event) {
            return ctx.tableActivate.emit($event);
          });
          ɵɵtemplate(1, ExtensibleTableComponent_Conditional_1_Template, 3, 6, "ngx-datatable-column", 2);
          ɵɵrepeaterCreate(2, ExtensibleTableComponent_For_3_Template, 1, 1, "ngx-datatable-column", 3, _forTrack1);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("rows", ctx.data)("count", ctx.recordsTotal)("list", ctx.list);
          ɵɵadvance();
          ɵɵconditional(ctx.actionsTemplate || ctx.actionList.length && ctx.hasAtLeastOnePermittedAction ? 1 : -1);
          ɵɵadvance();
          ɵɵrepeater(ctx.propList);
        }
      },
      dependencies: [AbpVisibleDirective, NgxDatatableModule, DatatableComponent, DataTableColumnDirective, DataTableColumnHeaderDirective, DataTableColumnCellDirective, GridActionsComponent, NgbTooltip, NgxDatatableDefaultDirective, NgxDatatableListDirective, PermissionDirective, LocalizationModule, LocalizationPipe, AsyncPipe, NgTemplateOutlet, NgComponentOutlet],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensibleTableComponent, [{
    type: Component,
    args: [{
      exportAs: "abpExtensibleTable",
      selector: "abp-extensible-table",
      standalone: true,
      imports: [AbpVisibleDirective, NgxDatatableModule, GridActionsComponent, NgbTooltip, NgxDatatableDefaultDirective, NgxDatatableListDirective, PermissionDirective, LocalizationModule, AsyncPipe, NgTemplateOutlet, NgComponentOutlet],
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `<ngx-datatable\r
  default\r
  [rows]="data"\r
  [count]="recordsTotal"\r
  [list]="list"\r
  (activate)="tableActivate.emit($event)"\r
>\r
  @if (actionsTemplate || (actionList.length && hasAtLeastOnePermittedAction)) {\r
    <ngx-datatable-column\r
      [name]="actionsText | abpLocalization"\r
      [maxWidth]="columnWidths[0]"\r
      [width]="columnWidths[0]"\r
      [sortable]="false"\r
    >\r
      <ng-template let-row="row" let-i="rowIndex" ngx-datatable-cell-template>\r
        <ng-container\r
          *ngTemplateOutlet="actionsTemplate || gridActions; context: { $implicit: row, index: i }"\r
        ></ng-container>\r
        <ng-template #gridActions>\r
          @if (isVisibleActions(row)) {\r
            <abp-grid-actions [index]="i" [record]="row" text="AbpUi::Actions"></abp-grid-actions>\r
          }\r
        </ng-template>\r
      </ng-template>\r
    </ngx-datatable-column>\r
  }\r
  @for (prop of propList; track prop.name; let i = $index) {\r
    <ngx-datatable-column\r
      *abpVisible="prop.columnVisible(getInjected)"\r
      [width]="columnWidths[i + 1] || 200"\r
      [name]="prop.displayName | abpLocalization"\r
      [prop]="prop.name"\r
      [sortable]="prop.sortable"\r
    >\r
      <ng-template ngx-datatable-header-template let-column="column">\r
        @if (prop.tooltip) {\r
          <span\r
            [ngbTooltip]="prop.tooltip.text | abpLocalization"\r
            [placement]="prop.tooltip.placement || 'auto'"\r
            container="body"\r
          >\r
            {{ column.name }} <i class="fa fa-info-circle" aria-hidden="true"></i>\r
          </span>\r
        } @else {\r
          {{ column.name }}\r
        }\r
      </ng-template>\r
      <ng-template let-row="row" let-i="index" ngx-datatable-cell-template>\r
        <ng-container *abpPermission="prop.permission; runChangeDetection: false">\r
          <ng-container *abpVisible="row['_' + prop.name]?.visible">\r
            @if (!row['_' + prop.name].component) {\r
              <div\r
                [innerHTML]="\r
                  !prop.isExtra\r
                    ? (row['_' + prop.name]?.value | async)\r
                    : ('::' + (row['_' + prop.name]?.value | async) | abpLocalization)\r
                "\r
                (click)="\r
                  prop.action && prop.action({ getInjected: getInjected, record: row, index: i })\r
                "\r
                [ngClass]="entityPropTypeClasses[prop.type]"\r
                [class.pointer]="prop.action"\r
              ></div>\r
            } @else {\r
              <ng-container\r
                *ngComponentOutlet="\r
                  row['_' + prop.name].component;\r
                  injector: row['_' + prop.name].injector\r
                "\r
              ></ng-container>\r
            }\r
          </ng-container>\r
        </ng-container>\r
      </ng-template>\r
    </ngx-datatable-column>\r
  }\r
</ngx-datatable>\r
`
    }]
  }], () => [], {
    actionsText: [{
      type: Input
    }],
    data: [{
      type: Input
    }],
    list: [{
      type: Input
    }],
    recordsTotal: [{
      type: Input
    }],
    actionsColumnWidth: [{
      type: Input
    }],
    actionsTemplate: [{
      type: Input
    }],
    tableActivate: [{
      type: Output
    }]
  });
})();
var PageToolbarComponent = class _PageToolbarComponent extends AbstractActionsComponent {
  constructor(injector) {
    super(injector);
    this.injector = injector;
    this.defaultBtnClass = "btn btn-sm btn-primary";
    this.getData = () => this.data;
    this.trackByFn = (_, item) => item.action || item.component;
  }
  asToolbarAction(value) {
    return {
      value
    };
  }
  static {
    this.ɵfac = function PageToolbarComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PageToolbarComponent)(ɵɵdirectiveInject(Injector));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _PageToolbarComponent,
      selectors: [["abp-page-toolbar"]],
      exportAs: ["abpPageToolbar"],
      standalone: true,
      features: [ɵɵProvidersFeature([{
        provide: EXTENSIONS_ACTION_TYPE,
        useValue: "toolbarActions"
      }]), ɵɵInheritDefinitionFeature, ɵɵStandaloneFeature],
      decls: 3,
      vars: 0,
      consts: [["id", "AbpContentToolbar", 1, "row", "justify-content-end", "mx-0", "gap-2"], [1, "col-auto", "px-0", "pt-0", 3, "pe-0"], [1, "col-auto", "px-0", "pt-0"], [4, "abpPermission", "abpPermissionRunChangeDetection"], [4, "ngComponentOutlet", "ngComponentOutletInjector"], ["type", "button", 1, "d-inline-flex", "align-items-center", "gap-1", 3, "ngClass"], ["type", "button", 1, "d-inline-flex", "align-items-center", "gap-1", 3, "click", "ngClass"], [3, "ngClass"]],
      template: function PageToolbarComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵrepeaterCreate(1, PageToolbarComponent_For_2_Template, 2, 3, "div", 1, _forTrack3);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵrepeater(ctx.actionList);
        }
      },
      dependencies: [CreateInjectorPipe, PermissionDirective, LocalizationModule, LocalizationPipe, NgClass, NgComponentOutlet],
      encapsulation: 2,
      changeDetection: 0
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PageToolbarComponent, [{
    type: Component,
    args: [{
      exportAs: "abpPageToolbar",
      selector: "abp-page-toolbar",
      standalone: true,
      imports: [CreateInjectorPipe, PermissionDirective, LocalizationModule, NgClass, NgComponentOutlet],
      providers: [{
        provide: EXTENSIONS_ACTION_TYPE,
        useValue: "toolbarActions"
      }],
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: '<div class="row justify-content-end mx-0 gap-2" id="AbpContentToolbar">\r\n  @for (action of actionList; track action.component || action.action; let last = $last) {\r\n  <div class="col-auto px-0 pt-0" [class.pe-0]="last">\r\n    @if (action.visible(data)) {\r\n    <ng-container *abpPermission="action.permission; runChangeDetection: false">\r\n      @if (action.component; as component) {\r\n      <ng-container\r\n        *ngComponentOutlet="component; injector: record | createInjector: action:this"\r\n      ></ng-container>\r\n\r\n      }@else {\r\n         @if (asToolbarAction(action).value; as toolbarAction ) {\r\n          <button\r\n            (click)="action.action(data)"\r\n            type="button"\r\n            [ngClass]="toolbarAction?.btnClass ? toolbarAction?.btnClass : defaultBtnClass"\r\n            class="d-inline-flex align-items-center gap-1"\r\n          >\r\n            <i [ngClass]="toolbarAction?.icon" [class.me-1]="toolbarAction?.icon"></i>\r\n            {{ toolbarAction?.text | abpLocalization }}\r\n          </button>\r\n        } \r\n      }\r\n    </ng-container>\r\n    }\r\n  </div>\r\n  }\r\n</div>\r\n\r\n'
    }]
  }], () => [{
    type: Injector
  }], null);
})();
var objectExtensions = Object.freeze({
  __proto__: null
});
var EXTENSIBLE_FORM_VIEW_PROVIDER = {
  provide: ControlContainer,
  useExisting: FormGroupDirective
};
function generateFormFromProps(data) {
  const extensions = data.getInjected(ExtensionsService);
  const identifier = data.getInjected(EXTENSIONS_IDENTIFIER);
  const form = new UntypedFormGroup({});
  const extraForm = new UntypedFormGroup({});
  form.addControl(EXTRA_PROPERTIES_KEY, extraForm);
  const record = data.record || {};
  const type = JSON.stringify(record) === "{}" ? "create" : "edit";
  const props = extensions[`${type}FormProps`].get(identifier).props;
  const extraProperties = record[EXTRA_PROPERTIES_KEY] || {};
  props.forEach(({
    value: prop
  }) => {
    const name = prop.name;
    const isExtraProperty = prop.isExtra || name in extraProperties;
    let value = isExtraProperty ? extraProperties[name] : name in record ? record[name] : void 0;
    if (typeof value === "undefined") value = prop.defaultValue;
    if (value) {
      let adapter;
      switch (prop.type) {
        case "date":
          adapter = new DateAdapter();
          value = adapter.toModel(adapter.fromModel(value));
          break;
        case "time":
          adapter = new TimeAdapter();
          value = adapter.toModel(adapter.fromModel(value));
          break;
        case "datetime":
          adapter = new DateTimeAdapter();
          value = adapter.toModel(adapter.fromModel(value));
          break;
        default:
          break;
      }
    }
    const formControl = new UntypedFormControl(value, {
      asyncValidators: prop.asyncValidators(data),
      validators: prop.validators(data)
    });
    (isExtraProperty ? extraForm : form).addControl(name, formControl);
  });
  return form;
}
function createExtraPropertyValueResolver(name) {
  return (data) => of(data.record[EXTRA_PROPERTIES_KEY][name]);
}
function mergeWithDefaultProps(extension, defaultProps, ...contributors) {
  Object.keys(defaultProps).forEach((name) => {
    const props = extension.get(name);
    props.clearContributors();
    props.addContributor((propList) => propList.addManyTail(defaultProps[name]));
    contributors.forEach((contributor) => (contributor[name] || []).forEach((callback) => props.addContributor(callback)));
  });
}
function createEnum(members) {
  const enumObject = {};
  members.forEach(({
    name = "",
    value
  }) => {
    enumObject[enumObject[name] = value] = name;
  });
  return enumObject;
}
function createEnumValueResolver(enumType, lookupEnum, propName) {
  return (data) => {
    const value = data.record[EXTRA_PROPERTIES_KEY][propName];
    const key = lookupEnum.transformed[value];
    const l10n = data.getInjected(LocalizationService);
    const localizeEnum = createEnumLocalizer(l10n, enumType, lookupEnum);
    return createLocalizationStream(l10n, localizeEnum(key));
  };
}
function createEnumOptions(enumType, lookupEnum) {
  return (data) => {
    const l10n = data.getInjected(LocalizationService);
    const localizeEnum = createEnumLocalizer(l10n, enumType, lookupEnum);
    return createLocalizationStream(l10n, lookupEnum.fields.map(({
      name = "",
      value
    }) => ({
      key: localizeEnum(name),
      value
    })));
  };
}
function createLocalizationStream(l10n, mapTarget) {
  return merge(of(null), l10n.languageChange$).pipe(map(() => mapTarget));
}
function createEnumLocalizer(l10n, enumType, lookupEnum) {
  const resource = lookupEnum.localizationResource;
  const shortType = getShortEnumType(enumType);
  return (key) => l10n.localizeWithFallbackSync([resource || ""], ["Enum:" + shortType + "." + key, shortType + "." + key, key], key);
}
function getShortEnumType(enumType) {
  return enumType.split(".").pop();
}
function createDisplayNameLocalizationPipeKeyGenerator(localization) {
  const generateLocalizationPipeKey = createLocalizationPipeKeyGenerator(localization);
  return (displayName, fallback) => {
    if (displayName && displayName.name) return generateLocalizationPipeKey([displayName.resource || ""], [displayName.name], displayName.name);
    const key = generateLocalizationPipeKey([fallback.resource || ""], ["DisplayName:" + fallback.name], void 0);
    if (key) return key;
    return generateLocalizationPipeKey([fallback.resource || ""], [fallback.name || ""], fallback.name);
  };
}
function getValidatorsFromProperty(property) {
  const validators = [];
  property.attributes.forEach((attr) => {
    if (attr.typeSimple && attr.typeSimple in AbpValidators) {
      validators.push(AbpValidators[attr.typeSimple](attr.config));
    }
  });
  return validators;
}
function selectObjectExtensions(configState) {
  return configState.getOne$("objectExtensions");
}
function selectLocalization(configState) {
  return configState.getOne$("localization");
}
function selectEnums(configState) {
  return selectObjectExtensions(configState).pipe(map((extensions) => Object.keys(extensions.enums).reduce((acc, key) => {
    const {
      fields,
      localizationResource
    } = extensions.enums[key];
    acc[key] = {
      fields,
      localizationResource,
      transformed: createEnum(fields)
    };
    return acc;
  }, {})));
}
function getObjectExtensionEntitiesFromStore(configState, moduleKey) {
  return selectObjectExtensions(configState).pipe(map((extensions) => {
    if (!extensions) return null;
    return (extensions.modules[moduleKey] || {}).entities;
  }), map((entities) => isUndefined(entities) ? {} : entities), filter(Boolean), take(1));
}
function mapEntitiesToContributors(configState, resource) {
  return pipe(switchMap((entities) => zip(selectLocalization(configState), selectEnums(configState)).pipe(map(([localization, enums]) => {
    const generateDisplayName = createDisplayNameLocalizationPipeKeyGenerator(localization);
    return Object.keys(entities).reduce((acc, key) => {
      acc.prop[key] = [];
      acc.createForm[key] = [];
      acc.editForm[key] = [];
      const entity = entities[key];
      if (!entity) return acc;
      const properties = entity.properties;
      if (!properties) return acc;
      const mapPropertiesToContributors = createPropertiesToContributorsMapper(generateDisplayName, resource, enums);
      return mapPropertiesToContributors(properties, acc, key);
    }, {
      prop: {},
      createForm: {},
      editForm: {}
    });
  }))), take(1));
}
function createPropertiesToContributorsMapper(generateDisplayName, resource, enums) {
  return (properties, contributors, key) => {
    const isExtra = true;
    const generateTypeaheadDisplayName = createTypeaheadDisplayNameGenerator(generateDisplayName, properties);
    Object.keys(properties).forEach((name) => {
      const property = properties[name];
      const propName = name;
      const lookup = property.ui.lookup || {};
      const type = getTypeaheadType(lookup, name) || getTypeFromProperty(property);
      const generateDN = hasTypeaheadTextSuffix(name) ? generateTypeaheadDisplayName : generateDisplayName;
      const displayName = generateDN(property.displayName, {
        name,
        resource
      });
      if (property.ui.onTable.isVisible) {
        const sortable = Boolean(property.ui.onTable.isSortable);
        const columnWidth = type === "boolean" ? 150 : 250;
        const valueResolver = type === "enum" && property.type ? createEnumValueResolver(property.type, enums[property.type], propName) : createExtraPropertyValueResolver(propName);
        const entityProp = new EntityProp({
          type,
          name: propName,
          displayName,
          sortable,
          columnWidth,
          valueResolver,
          isExtra
        });
        const contributor = (propList) => propList.addTail(entityProp);
        contributors.prop[key].push(contributor);
      }
      const isOnCreateForm = property.ui.onCreateForm.isVisible;
      const isOnEditForm = property.ui.onEditForm.isVisible;
      if (isOnCreateForm || isOnEditForm) {
        const defaultValue = property.defaultValue;
        const formText = property.formText;
        const validators = () => getValidatorsFromProperty(property);
        let options;
        if (type === "enum") options = createEnumOptions(propName, enums[property.type || ""]);
        else if (type === "typeahead") options = createTypeaheadOptions(lookup);
        const formProp = new FormProp({
          type,
          name: propName,
          displayName,
          options,
          defaultValue,
          validators,
          isExtra,
          formText
        });
        const formContributor = (propList) => propList.addTail(formProp);
        if (isOnCreateForm) contributors.createForm[key].push(formContributor);
        if (isOnEditForm) contributors.editForm[key].push(formContributor);
      }
    });
    return contributors;
  };
}
function getTypeFromProperty(property) {
  return property?.typeSimple?.replace(/\?$/, "");
}
function isUndefined(obj) {
  return typeof obj === "undefined";
}
var importWithExport = [DisabledDirective, ExtensibleDateTimePickerComponent, ExtensibleFormPropComponent, GridActionsComponent, PropDataDirective, PageToolbarComponent, CreateInjectorPipe, ExtensibleFormComponent, ExtensibleTableComponent];
var ExtensibleModule = class _ExtensibleModule {
  static {
    this.ɵfac = function ExtensibleModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ExtensibleModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _ExtensibleModule,
      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule, NgbDatepickerModule, NgbDropdownModule, NgbTimepickerModule, NgbTypeaheadModule, NgbTooltipModule, DisabledDirective, ExtensibleDateTimePickerComponent, ExtensibleFormPropComponent, GridActionsComponent, PropDataDirective, PageToolbarComponent, CreateInjectorPipe, ExtensibleFormComponent, ExtensibleTableComponent],
      exports: [DisabledDirective, ExtensibleDateTimePickerComponent, ExtensibleFormPropComponent, GridActionsComponent, PropDataDirective, PageToolbarComponent, CreateInjectorPipe, ExtensibleFormComponent, ExtensibleTableComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule, NgbDatepickerModule, NgbDropdownModule, NgbTimepickerModule, NgbTypeaheadModule, NgbTooltipModule, ExtensibleDateTimePickerComponent, ExtensibleFormPropComponent, GridActionsComponent, PageToolbarComponent, ExtensibleFormComponent, ExtensibleTableComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ExtensibleModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [CoreModule, ThemeSharedModule, NgxValidateCoreModule, NgbDatepickerModule, NgbDropdownModule, NgbTimepickerModule, NgbTypeaheadModule, NgbTooltipModule, ...importWithExport],
      exports: [...importWithExport]
    }]
  }], null, null);
})();

// node_modules/@abp/ng.account/fesm2022/abp-ng.account.mjs
function ForgotPasswordComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "form", 1);
    ɵɵlistener("ngSubmit", function ForgotPasswordComponent_Conditional_3_Template_form_ngSubmit_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onSubmit());
    });
    ɵɵelementStart(1, "p");
    ɵɵtext(2);
    ɵɵpipe(3, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(4, "div", 2)(5, "label", 3);
    ɵɵtext(6);
    ɵɵpipe(7, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(8, "span");
    ɵɵtext(9, " * ");
    ɵɵelementEnd();
    ɵɵelement(10, "input", 4);
    ɵɵelementEnd();
    ɵɵelementStart(11, "abp-button", 5);
    ɵɵtext(12);
    ɵɵpipe(13, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(14, "a", 6);
    ɵɵelement(15, "i", 7);
    ɵɵtext(16);
    ɵɵpipe(17, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("formGroup", ctx_r1.form);
    ɵɵadvance(2);
    ɵɵtextInterpolate(ɵɵpipeBind1(3, 7, "AbpAccount::SendPasswordResetLink_Information"));
    ɵɵadvance(4);
    ɵɵtextInterpolate(ɵɵpipeBind1(7, 9, "AbpAccount::EmailAddress"));
    ɵɵadvance(5);
    ɵɵproperty("loading", ctx_r1.inProgress)("disabled", ctx_r1.form == null ? null : ctx_r1.form.invalid);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(13, 11, "AbpAccount::Submit"), " ");
    ɵɵadvance(4);
    ɵɵtextInterpolate(ɵɵpipeBind1(17, 13, "AbpAccount::Login"));
  }
}
function ForgotPasswordComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "p");
    ɵɵtext(1);
    ɵɵpipe(2, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(3, "a", 6)(4, "button", 8);
    ɵɵelement(5, "i", 7);
    ɵɵtext(6);
    ɵɵpipe(7, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 2, "AbpAccount::PasswordResetMailSentMessage"), " ");
    ɵɵadvance(5);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(7, 4, "AbpAccount::BackToLogin"), " ");
  }
}
function LoginComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "strong");
    ɵɵtext(1);
    ɵɵpipe(2, "abpLocalization");
    ɵɵelementStart(3, "a", 14);
    ɵɵtext(4);
    ɵɵpipe(5, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 2, "AbpAccount::AreYouANewUser"), " ");
    ɵɵadvance(3);
    ɵɵtextInterpolate(ɵɵpipeBind1(5, 4, "AbpAccount::Register"));
  }
}
function ChangePasswordComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 1)(1, "label", 7);
    ɵɵtext(2);
    ɵɵpipe(3, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(4, "span");
    ɵɵtext(5, " * ");
    ɵɵelementEnd();
    ɵɵelement(6, "input", 8);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    ɵɵadvance(2);
    ɵɵtextInterpolate(ɵɵpipeBind1(3, 1, "AbpIdentity::DisplayName:CurrentPassword"));
  }
}
function PersonalSettingsComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "form", 1);
    ɵɵlistener("ngSubmit", function PersonalSettingsComponent_Conditional_0_Template_form_ngSubmit_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.submit());
    });
    ɵɵelement(1, "abp-extensible-form", 2);
    ɵɵelementStart(2, "abp-button", 3);
    ɵɵtext(3);
    ɵɵpipe(4, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("formGroup", ctx_r1.form);
    ɵɵadvance();
    ɵɵproperty("selectedRecord", ctx_r1.selected);
    ɵɵadvance();
    ɵɵproperty("loading", ctx_r1.inProgress);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(4, 4, "AbpIdentity::Save"), "");
  }
}
var _c02 = (a0) => ({
  active: a0
});
var _c12 = (a0) => ({
  componentKey: a0
});
function ManageProfileComponent_Conditional_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "li", 10);
    ɵɵlistener("click", function ManageProfileComponent_Conditional_7_Template_li_click_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.selectedTab = 0);
    });
    ɵɵelementStart(1, "a", 8);
    ɵɵtext(2);
    ɵɵpipe(3, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵproperty("ngClass", ɵɵpureFunction1(4, _c02, ctx_r1.selectedTab === 0));
    ɵɵadvance();
    ɵɵtextInterpolate(ɵɵpipeBind1(3, 2, "AbpUi::ChangePassword"));
  }
}
function ManageProfileComponent_Conditional_13_Conditional_1_abp_change_password_form_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "abp-change-password-form");
  }
}
function ManageProfileComponent_Conditional_13_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 11)(1, "div", 12)(2, "h4");
    ɵɵtext(3);
    ɵɵpipe(4, "abpLocalization");
    ɵɵelement(5, "hr");
    ɵɵelementEnd();
    ɵɵtemplate(6, ManageProfileComponent_Conditional_13_Conditional_1_abp_change_password_form_6_Template, 1, 0, "abp-change-password-form", 13);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext(2);
    ɵɵproperty("@fadeIn", void 0);
    ɵɵadvance(3);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(4, 3, "AbpIdentity::ChangePassword"), " ");
    ɵɵadvance(3);
    ɵɵproperty("abpReplaceableTemplate", ɵɵpureFunction1(5, _c12, ctx_r1.changePasswordKey));
  }
}
function ManageProfileComponent_Conditional_13_Conditional_2_abp_personal_settings_form_6_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelement(0, "abp-personal-settings-form");
  }
}
function ManageProfileComponent_Conditional_13_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 11)(1, "div", 12)(2, "h4");
    ɵɵtext(3);
    ɵɵpipe(4, "abpLocalization");
    ɵɵelement(5, "hr");
    ɵɵelementEnd();
    ɵɵtemplate(6, ManageProfileComponent_Conditional_13_Conditional_2_abp_personal_settings_form_6_Template, 1, 0, "abp-personal-settings-form", 13);
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext(2);
    ɵɵproperty("@fadeIn", void 0);
    ɵɵadvance(3);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(4, 3, "AbpIdentity::PersonalSettings"), " ");
    ɵɵadvance(3);
    ɵɵproperty("abpReplaceableTemplate", ɵɵpureFunction1(5, _c12, ctx_r1.personalSettingsKey));
  }
}
function ManageProfileComponent_Conditional_13_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "div", 9);
    ɵɵtemplate(1, ManageProfileComponent_Conditional_13_Conditional_1_Template, 7, 7, "div", 11)(2, ManageProfileComponent_Conditional_13_Conditional_2_Template, 7, 7, "div", 11);
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵadvance();
    ɵɵconditional(ctx_r1.selectedTab === 0 ? 1 : -1);
    ɵɵadvance();
    ɵɵconditional(ctx_r1.selectedTab === 1 ? 2 : -1);
  }
}
function RegisterComponent_Conditional_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "form", 2);
    ɵɵlistener("ngSubmit", function RegisterComponent_Conditional_9_Template_form_ngSubmit_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onSubmit());
    });
    ɵɵelementStart(1, "div", 3)(2, "label", 4);
    ɵɵtext(3);
    ɵɵpipe(4, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(5, "span");
    ɵɵtext(6, " * ");
    ɵɵelementEnd();
    ɵɵelement(7, "input", 5);
    ɵɵelementEnd();
    ɵɵelementStart(8, "div", 3)(9, "label", 6);
    ɵɵtext(10);
    ɵɵpipe(11, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(12, "span");
    ɵɵtext(13, " * ");
    ɵɵelementEnd();
    ɵɵelement(14, "input", 7);
    ɵɵelementEnd();
    ɵɵelementStart(15, "div", 3)(16, "label", 8);
    ɵɵtext(17);
    ɵɵpipe(18, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(19, "span");
    ɵɵtext(20, " * ");
    ɵɵelementEnd();
    ɵɵelement(21, "input", 9);
    ɵɵelementEnd();
    ɵɵelementStart(22, "abp-button", 10);
    ɵɵtext(23);
    ɵɵpipe(24, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("formGroup", ctx_r1.form);
    ɵɵadvance(3);
    ɵɵtextInterpolate(ɵɵpipeBind1(4, 6, "AbpAccount::UserName"));
    ɵɵadvance(7);
    ɵɵtextInterpolate(ɵɵpipeBind1(11, 8, "AbpAccount::EmailAddress"));
    ɵɵadvance(7);
    ɵɵtextInterpolate(ɵɵpipeBind1(18, 10, "AbpAccount::Password"));
    ɵɵadvance(5);
    ɵɵproperty("loading", ctx_r1.inProgress);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(24, 12, "AbpAccount::Register"), " ");
  }
}
function ResetPasswordComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = ɵɵgetCurrentView();
    ɵɵelementStart(0, "form", 1);
    ɵɵlistener("ngSubmit", function ResetPasswordComponent_Conditional_3_Template_form_ngSubmit_0_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onSubmit());
    });
    ɵɵelementStart(1, "p");
    ɵɵtext(2);
    ɵɵpipe(3, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(4, "div", 2)(5, "label", 3);
    ɵɵtext(6);
    ɵɵpipe(7, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(8, "span");
    ɵɵtext(9, " * ");
    ɵɵelementEnd();
    ɵɵelement(10, "input", 4);
    ɵɵelementEnd();
    ɵɵelementStart(11, "div", 2)(12, "label", 5);
    ɵɵtext(13);
    ɵɵpipe(14, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(15, "span");
    ɵɵtext(16, " * ");
    ɵɵelementEnd();
    ɵɵelement(17, "input", 6);
    ɵɵelementEnd();
    ɵɵelementStart(18, "button", 7);
    ɵɵtext(19);
    ɵɵpipe(20, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(21, "abp-button", 8);
    ɵɵlistener("click", function ResetPasswordComponent_Conditional_3_Template_abp_button_click_21_listener() {
      ɵɵrestoreView(_r1);
      const ctx_r1 = ɵɵnextContext();
      return ɵɵresetView(ctx_r1.onSubmit());
    });
    ɵɵtext(22);
    ɵɵpipe(23, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("formGroup", ctx_r1.form)("mapErrorsFn", ctx_r1.mapErrorsFn);
    ɵɵadvance(2);
    ɵɵtextInterpolate(ɵɵpipeBind1(3, 8, "AbpAccount::ResetPassword_Information"));
    ɵɵadvance(4);
    ɵɵtextInterpolate(ɵɵpipeBind1(7, 10, "AbpAccount::Password"));
    ɵɵadvance(7);
    ɵɵtextInterpolate(ɵɵpipeBind1(14, 12, "AbpAccount::ConfirmPassword"));
    ɵɵadvance(6);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(20, 14, "AbpAccount::Cancel"), " ");
    ɵɵadvance(2);
    ɵɵproperty("loading", ctx_r1.inProgress);
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(23, 16, "AbpAccount::Submit"), " ");
  }
}
function ResetPasswordComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "p");
    ɵɵtext(1);
    ɵɵpipe(2, "abpLocalization");
    ɵɵelementEnd();
    ɵɵelementStart(3, "a", 9)(4, "button", 10);
    ɵɵtext(5);
    ɵɵpipe(6, "abpLocalization");
    ɵɵelementEnd()();
  }
  if (rf & 2) {
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 2, "AbpAccount::YourPasswordIsSuccessfullyReset"), " ");
    ɵɵadvance(4);
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(6, 4, "AbpAccount::BackToLogin"), " ");
  }
}
var ForgotPasswordComponent = class _ForgotPasswordComponent {
  constructor(fb, accountService) {
    this.fb = fb;
    this.accountService = accountService;
    this.isEmailSent = false;
    this.form = this.fb.group({
      email: ["", [Validators.required, Validators.email]]
    });
  }
  onSubmit() {
    if (this.form.invalid) return;
    this.inProgress = true;
    this.accountService.sendPasswordResetCode({
      email: this.form.get("email")?.value,
      appName: "Angular"
    }).pipe(finalize(() => this.inProgress = false)).subscribe(() => {
      this.isEmailSent = true;
    });
  }
  static {
    this.ɵfac = function ForgotPasswordComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ForgotPasswordComponent)(ɵɵdirectiveInject(UntypedFormBuilder), ɵɵdirectiveInject(AccountService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ForgotPasswordComponent,
      selectors: [["abp-forgot-password"]],
      decls: 5,
      vars: 4,
      consts: [["validateOnSubmit", "", 3, "formGroup"], ["validateOnSubmit", "", 3, "ngSubmit", "formGroup"], [1, "mb-3", "form-group"], ["for", "input-email-address", 1, "form-label"], ["type", "email", "id", "input-email-address", "formControlName", "email", 1, "form-control"], ["buttonClass", "mt-2 mb-3 btn btn-primary btn-block", "buttonType", "submit", 1, "d-block", 3, "loading", "disabled"], ["routerLink", "/account/login"], ["aria-hidden", "true", 1, "fa", "fa-long-arrow-left", "me-1"], [1, "d-block", "mt-2", "mb-3", "btn", "btn-primary", "btn-block"]],
      template: function ForgotPasswordComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "h4");
          ɵɵtext(1);
          ɵɵpipe(2, "abpLocalization");
          ɵɵelementEnd();
          ɵɵtemplate(3, ForgotPasswordComponent_Conditional_3_Template, 18, 15, "form", 0)(4, ForgotPasswordComponent_Conditional_4_Template, 8, 6);
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵtextInterpolate(ɵɵpipeBind1(2, 2, "AbpAccount::ForgotPassword"));
          ɵɵadvance(2);
          ɵɵconditional(!ctx.isEmailSent ? 3 : 4);
        }
      },
      dependencies: [ɵNgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterLink, FormSubmitDirective, ValidationGroupDirective, ValidationDirective, ButtonComponent, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ForgotPasswordComponent, [{
    type: Component,
    args: [{
      selector: "abp-forgot-password",
      template: `<h4>{{ 'AbpAccount::ForgotPassword' | abpLocalization }}</h4>\r
\r
@if (!isEmailSent) {\r
  <form [formGroup]="form" (ngSubmit)="onSubmit()" validateOnSubmit>\r
    <p>{{ 'AbpAccount::SendPasswordResetLink_Information' | abpLocalization }}</p>\r
    <div class="mb-3 form-group">\r
      <label for="input-email-address" class="form-label">{{\r
        'AbpAccount::EmailAddress' | abpLocalization\r
      }}</label\r
      ><span> * </span>\r
      <input type="email" id="input-email-address" class="form-control" formControlName="email" />\r
    </div>\r
    <abp-button\r
      class="d-block"\r
      buttonClass="mt-2 mb-3 btn btn-primary btn-block"\r
      [loading]="inProgress"\r
      buttonType="submit"\r
      [disabled]="form?.invalid"\r
    >\r
      {{ 'AbpAccount::Submit' | abpLocalization }}\r
    </abp-button>\r
    <a routerLink="/account/login"\r
      ><i class="fa fa-long-arrow-left me-1" aria-hidden="true"></i\r
      >{{ 'AbpAccount::Login' | abpLocalization }}</a\r
    >\r
  </form>\r
} @else {\r
  <p>\r
    {{ 'AbpAccount::PasswordResetMailSentMessage' | abpLocalization }}\r
  </p>\r
\r
  <a routerLink="/account/login">\r
    <button class="d-block mt-2 mb-3 btn btn-primary btn-block">\r
      <i class="fa fa-long-arrow-left me-1" aria-hidden="true"></i>\r
      {{ 'AbpAccount::BackToLogin' | abpLocalization }}\r
    </button>\r
  </a>\r
}\r
`
    }]
  }], () => [{
    type: UntypedFormBuilder
  }, {
    type: AccountService
  }], null);
})();
var ACCOUNT_CONFIG_OPTIONS = new InjectionToken("ACCOUNT_CONFIG_OPTIONS");
function getRedirectUrl(injector) {
  const route = injector.get(ActivatedRoute);
  const options = injector.get(ACCOUNT_CONFIG_OPTIONS);
  return route.snapshot.queryParams.returnUrl || options.redirectUrl || "/";
}
var {
  maxLength: maxLength$2,
  required: required$3
} = Validators;
var LoginComponent = class _LoginComponent {
  constructor() {
    this.injector = inject(Injector);
    this.fb = inject(UntypedFormBuilder);
    this.toasterService = inject(ToasterService);
    this.authService = inject(AuthService);
    this.configState = inject(ConfigStateService);
    this.isSelfRegistrationEnabled = true;
    this.authWrapperKey = "Account.AuthWrapperComponent";
  }
  ngOnInit() {
    this.init();
    this.buildForm();
  }
  init() {
    this.isSelfRegistrationEnabled = (this.configState.getSetting("Abp.Account.IsSelfRegistrationEnabled") || "").toLowerCase() !== "false";
  }
  buildForm() {
    this.form = this.fb.group({
      username: ["", [required$3, maxLength$2(255)]],
      password: ["", [required$3, maxLength$2(128)]],
      rememberMe: [false]
    });
  }
  onSubmit() {
    if (this.form.invalid) return;
    this.inProgress = true;
    const {
      username,
      password,
      rememberMe
    } = this.form.value;
    const redirectUrl = getRedirectUrl(this.injector);
    this.authService.login({
      username,
      password,
      rememberMe,
      redirectUrl
    }).pipe(catchError((err) => {
      this.toasterService.error(err.error?.error_description || err.error?.error.message || "AbpAccount::DefaultErrorMessage", "", {
        life: 7e3
      });
      return throwError(err);
    }), finalize(() => this.inProgress = false)).subscribe();
  }
  static {
    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _LoginComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _LoginComponent,
      selectors: [["abp-login"]],
      decls: 29,
      vars: 21,
      consts: [["validateOnSubmit", "", 1, "mt-4", 3, "ngSubmit", "formGroup"], [1, "mb-3", "form-group"], ["for", "login-input-user-name-or-email-address", 1, "form-label"], ["type", "text", "id", "login-input-user-name-or-email-address", "formControlName", "username", "autocomplete", "username", "autofocus", "", 1, "form-control"], ["for", "login-input-password", 1, "form-label"], ["type", "password", "id", "login-input-password", "formControlName", "password", "autocomplete", "current-password", 1, "form-control"], [1, "row"], [1, "col"], [1, "form-check"], ["for", "login-input-remember-me", 1, "form-check-label", "mb-2"], ["type", "checkbox", "id", "login-input-remember-me", "formControlName", "rememberMe", 1, "form-check-input"], [1, "text-end", "col"], ["routerLink", "/account/forgot-password"], ["buttonType", "submit", "name", "Action", "buttonClass", "btn-block btn-lg mt-3 btn btn-primary", 3, "loading"], ["routerLink", "/account/register", "queryParamsHandling", "preserve", 1, "text-decoration-none"]],
      template: function LoginComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "h4");
          ɵɵtext(1);
          ɵɵpipe(2, "abpLocalization");
          ɵɵelementEnd();
          ɵɵtemplate(3, LoginComponent_Conditional_3_Template, 6, 6, "strong");
          ɵɵelementStart(4, "form", 0);
          ɵɵlistener("ngSubmit", function LoginComponent_Template_form_ngSubmit_4_listener() {
            return ctx.onSubmit();
          });
          ɵɵelementStart(5, "div", 1)(6, "label", 2);
          ɵɵtext(7);
          ɵɵpipe(8, "abpLocalization");
          ɵɵelementEnd();
          ɵɵelement(9, "input", 3);
          ɵɵelementEnd();
          ɵɵelementStart(10, "div", 1)(11, "label", 4);
          ɵɵtext(12);
          ɵɵpipe(13, "abpLocalization");
          ɵɵelementEnd();
          ɵɵelement(14, "input", 5);
          ɵɵelementEnd();
          ɵɵelementStart(15, "div", 6)(16, "div", 7)(17, "div", 8)(18, "label", 9);
          ɵɵelement(19, "input", 10);
          ɵɵtext(20);
          ɵɵpipe(21, "abpLocalization");
          ɵɵelementEnd()()();
          ɵɵelementStart(22, "div", 11)(23, "a", 12);
          ɵɵtext(24);
          ɵɵpipe(25, "abpLocalization");
          ɵɵelementEnd()()();
          ɵɵelementStart(26, "abp-button", 13);
          ɵɵtext(27);
          ɵɵpipe(28, "abpLocalization");
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵtextInterpolate(ɵɵpipeBind1(2, 9, "AbpAccount::Login"));
          ɵɵadvance(2);
          ɵɵconditional(ctx.isSelfRegistrationEnabled ? 3 : -1);
          ɵɵadvance();
          ɵɵproperty("formGroup", ctx.form);
          ɵɵadvance(3);
          ɵɵtextInterpolate(ɵɵpipeBind1(8, 11, "AbpAccount::UserNameOrEmailAddress"));
          ɵɵadvance(5);
          ɵɵtextInterpolate(ɵɵpipeBind1(13, 13, "AbpAccount::Password"));
          ɵɵadvance(8);
          ɵɵtextInterpolate1(" ", ɵɵpipeBind1(21, 15, "AbpAccount::RememberMe"), " ");
          ɵɵadvance(4);
          ɵɵtextInterpolate(ɵɵpipeBind1(25, 17, "AbpAccount::ForgotPassword"));
          ɵɵadvance(2);
          ɵɵproperty("loading", ctx.inProgress);
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ɵɵpipeBind1(28, 19, "AbpAccount::Login"), " ");
        }
      },
      dependencies: [ɵNgNoValidate, DefaultValueAccessor, CheckboxControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterLink, AutofocusDirective, FormSubmitDirective, ValidationGroupDirective, ValidationDirective, ButtonComponent, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{
      selector: "abp-login",
      template: `<h4>{{ 'AbpAccount::Login' | abpLocalization }}</h4>\r
@if (isSelfRegistrationEnabled) {\r
  <strong>\r
    {{ 'AbpAccount::AreYouANewUser' | abpLocalization }}\r
    <a class="text-decoration-none" routerLink="/account/register" queryParamsHandling="preserve">{{\r
      'AbpAccount::Register' | abpLocalization\r
    }}</a>\r
  </strong>\r
}\r
<form [formGroup]="form" (ngSubmit)="onSubmit()" validateOnSubmit class="mt-4">\r
  <div class="mb-3 form-group">\r
    <label for="login-input-user-name-or-email-address" class="form-label">{{\r
      'AbpAccount::UserNameOrEmailAddress' | abpLocalization\r
    }}</label>\r
    <input\r
      class="form-control"\r
      type="text"\r
      id="login-input-user-name-or-email-address"\r
      formControlName="username"\r
      autocomplete="username"\r
      autofocus\r
    />\r
  </div>\r
  <div class="mb-3 form-group">\r
    <label for="login-input-password" class="form-label">{{\r
      'AbpAccount::Password' | abpLocalization\r
    }}</label>\r
    <input\r
      class="form-control"\r
      type="password"\r
      id="login-input-password"\r
      formControlName="password"\r
      autocomplete="current-password"\r
    />\r
  </div>\r
\r
  <div class="row">\r
    <div class="col">\r
      <div class="form-check">\r
        <label class="form-check-label mb-2" for="login-input-remember-me">\r
          <input\r
            class="form-check-input"\r
            type="checkbox"\r
            id="login-input-remember-me"\r
            formControlName="rememberMe"\r
          />\r
          {{ 'AbpAccount::RememberMe' | abpLocalization }}\r
        </label>\r
      </div>\r
    </div>\r
    <div class="text-end col">\r
      <a routerLink="/account/forgot-password">{{\r
        'AbpAccount::ForgotPassword' | abpLocalization\r
      }}</a>\r
    </div>\r
  </div>\r
\r
  <abp-button\r
    [loading]="inProgress"\r
    buttonType="submit"\r
    name="Action"\r
    buttonClass="btn-block btn-lg mt-3 btn btn-primary"\r
  >\r
    {{ 'AbpAccount::Login' | abpLocalization }}\r
  </abp-button>\r
</form>\r
`
    }]
  }], null, null);
})();
var ManageProfileStateService = class _ManageProfileStateService {
  constructor() {
    this.store = new InternalStore({});
  }
  get createOnUpdateStream() {
    return this.store.sliceUpdate;
  }
  getProfile$() {
    return this.store.sliceState((state) => state.profile);
  }
  getProfile() {
    return this.store.state.profile;
  }
  setProfile(profile) {
    this.store.patch({
      profile
    });
  }
  static {
    this.ɵfac = function ManageProfileStateService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ManageProfileStateService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _ManageProfileStateService,
      factory: _ManageProfileStateService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ManageProfileStateService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var {
  required: required$2
} = Validators;
var PASSWORD_FIELDS$1 = ["newPassword", "repeatNewPassword"];
var ChangePasswordComponent = class _ChangePasswordComponent {
  constructor(fb, injector, toasterService, profileService, manageProfileState) {
    this.fb = fb;
    this.injector = injector;
    this.toasterService = toasterService;
    this.profileService = profileService;
    this.manageProfileState = manageProfileState;
    this.mapErrorsFn = (errors, groupErrors, control) => {
      if (PASSWORD_FIELDS$1.indexOf(String(control?.name)) < 0) return errors;
      return errors.concat(groupErrors.filter(({
        key
      }) => key === "passwordMismatch"));
    };
  }
  ngOnInit() {
    this.hideCurrentPassword = !this.manageProfileState.getProfile()?.hasPassword;
    const passwordValidations = getPasswordValidators(this.injector);
    this.form = this.fb.group({
      password: ["", required$2],
      newPassword: ["", {
        validators: [required$2, ...passwordValidations]
      }],
      repeatNewPassword: ["", {
        validators: [required$2, ...passwordValidations]
      }]
    }, {
      validators: [comparePasswords(PASSWORD_FIELDS$1)]
    });
    if (this.hideCurrentPassword) this.form.removeControl("password");
  }
  onSubmit() {
    if (this.form.invalid) return;
    this.inProgress = true;
    this.profileService.changePassword(__spreadProps(__spreadValues({}, !this.hideCurrentPassword && {
      currentPassword: this.form.get("password")?.value
    }), {
      newPassword: this.form.get("newPassword")?.value
    })).pipe(finalize(() => this.inProgress = false)).subscribe({
      next: () => {
        this.form.reset();
        this.toasterService.success("AbpAccount::PasswordChangedMessage", "", {
          life: 5e3
        });
        if (this.hideCurrentPassword) {
          this.hideCurrentPassword = false;
          this.form.addControl("password", new UntypedFormControl("", [required$2]));
        }
      },
      error: (err) => {
        this.toasterService.error(err.error?.error?.message || "AbpAccount::DefaultErrorMessage");
      }
    });
  }
  static {
    this.ɵfac = function ChangePasswordComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ChangePasswordComponent)(ɵɵdirectiveInject(UntypedFormBuilder), ɵɵdirectiveInject(Injector), ɵɵdirectiveInject(ToasterService), ɵɵdirectiveInject(ProfileService), ɵɵdirectiveInject(ManageProfileStateService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ChangePasswordComponent,
      selectors: [["abp-change-password-form"]],
      exportAs: ["abpChangePasswordForm"],
      decls: 19,
      vars: 14,
      consts: [["validateOnSubmit", "", 1, "abp-md-form", 3, "ngSubmit", "formGroup", "mapErrorsFn"], [1, "mb-3", "form-group"], ["for", "new-password", 1, "form-label"], ["type", "password", "id", "new-password", "formControlName", "newPassword", "autocomplete", "new-password", 1, "form-control"], ["for", "confirm-new-password", 1, "form-label"], ["type", "password", "id", "confirm-new-password", "formControlName", "repeatNewPassword", "autocomplete", "new-password", 1, "form-control"], ["iconClass", "fa fa-check", "buttonClass", "btn btn-primary color-white", "buttonType", "submit", 3, "loading", "disabled"], ["for", "current-password", 1, "form-label"], ["type", "password", "id", "current-password", "formControlName", "password", "autofocus", "", "autocomplete", "current-password", 1, "form-control"]],
      template: function ChangePasswordComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "form", 0);
          ɵɵlistener("ngSubmit", function ChangePasswordComponent_Template_form_ngSubmit_0_listener() {
            return ctx.onSubmit();
          });
          ɵɵtemplate(1, ChangePasswordComponent_Conditional_1_Template, 7, 3, "div", 1);
          ɵɵelementStart(2, "div", 1)(3, "label", 2);
          ɵɵtext(4);
          ɵɵpipe(5, "abpLocalization");
          ɵɵelementEnd();
          ɵɵelementStart(6, "span");
          ɵɵtext(7, " * ");
          ɵɵelementEnd();
          ɵɵelement(8, "input", 3);
          ɵɵelementEnd();
          ɵɵelementStart(9, "div", 1)(10, "label", 4);
          ɵɵtext(11);
          ɵɵpipe(12, "abpLocalization");
          ɵɵelementEnd();
          ɵɵelementStart(13, "span");
          ɵɵtext(14, " * ");
          ɵɵelementEnd();
          ɵɵelement(15, "input", 5);
          ɵɵelementEnd();
          ɵɵelementStart(16, "abp-button", 6);
          ɵɵtext(17);
          ɵɵpipe(18, "abpLocalization");
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          ɵɵproperty("formGroup", ctx.form)("mapErrorsFn", ctx.mapErrorsFn);
          ɵɵadvance();
          ɵɵconditional(!ctx.hideCurrentPassword ? 1 : -1);
          ɵɵadvance(3);
          ɵɵtextInterpolate(ɵɵpipeBind1(5, 8, "AbpIdentity::DisplayName:NewPassword"));
          ɵɵadvance(7);
          ɵɵtextInterpolate(ɵɵpipeBind1(12, 10, "AbpIdentity::DisplayName:NewPasswordConfirm"));
          ɵɵadvance(5);
          ɵɵproperty("loading", ctx.inProgress)("disabled", ctx.form == null ? null : ctx.form.invalid);
          ɵɵadvance();
          ɵɵtextInterpolate(ɵɵpipeBind1(18, 12, "AbpIdentity::Save"));
        }
      },
      dependencies: [ɵNgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, AutofocusDirective, FormSubmitDirective, ValidationGroupDirective, ValidationDirective, ButtonComponent, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ChangePasswordComponent, [{
    type: Component,
    args: [{
      selector: "abp-change-password-form",
      exportAs: "abpChangePasswordForm",
      template: `<form [formGroup]="form" (ngSubmit)="onSubmit()" [mapErrorsFn]="mapErrorsFn" validateOnSubmit class="abp-md-form">\r
  @if (!hideCurrentPassword) {\r
    <div class="mb-3 form-group">\r
      <label for="current-password" class="form-label">{{\r
        'AbpIdentity::DisplayName:CurrentPassword' | abpLocalization\r
      }}</label\r
      ><span> * </span\r
      ><input\r
        type="password"\r
        id="current-password"\r
        class="form-control"\r
        formControlName="password"\r
        autofocus\r
        autocomplete="current-password"\r
      />\r
    </div>\r
  }\r
  <div class="mb-3 form-group">\r
    <label for="new-password" class="form-label">{{\r
      'AbpIdentity::DisplayName:NewPassword' | abpLocalization\r
    }}</label\r
    ><span> * </span\r
    ><input\r
      type="password"\r
      id="new-password"\r
      class="form-control"\r
      formControlName="newPassword"\r
      autocomplete="new-password"\r
    />\r
  </div>\r
  <div class="mb-3 form-group">\r
    <label for="confirm-new-password" class="form-label">{{\r
      'AbpIdentity::DisplayName:NewPasswordConfirm' | abpLocalization\r
    }}</label\r
    ><span> * </span\r
    ><input\r
      type="password"\r
      id="confirm-new-password"\r
      class="form-control"\r
      formControlName="repeatNewPassword"\r
      autocomplete="new-password"\r
    />\r
  </div>\r
  <abp-button\r
    iconClass="fa fa-check"\r
    buttonClass="btn btn-primary color-white"\r
    buttonType="submit"\r
    [loading]="inProgress"\r
    [disabled]="form?.invalid"\r
    >{{ 'AbpIdentity::Save' | abpLocalization }}</abp-button\r
  >\r
</form>\r
`
    }]
  }], () => [{
    type: UntypedFormBuilder
  }, {
    type: Injector
  }, {
    type: ToasterService
  }, {
    type: ProfileService
  }, {
    type: ManageProfileStateService
  }], null);
})();
var RE_LOGIN_CONFIRMATION_TOKEN = new InjectionToken("RE_LOGIN_CONFIRMATION_TOKEN");
var PersonalSettingsHalfRowComponent = class _PersonalSettingsHalfRowComponent {
  constructor(propData) {
    this.propData = propData;
    this.displayName = propData.displayName;
    this.name = propData.name;
    this.id = propData.id || "";
  }
  static {
    this.ɵfac = function PersonalSettingsHalfRowComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PersonalSettingsHalfRowComponent)(ɵɵdirectiveInject(EXTENSIONS_FORM_PROP));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _PersonalSettingsHalfRowComponent,
      selectors: [["abp-personal-settings-half-row"]],
      features: [ɵɵProvidersFeature([], [EXTENSIBLE_FORM_VIEW_PROVIDER])],
      decls: 5,
      vars: 7,
      consts: [[1, "w-50", "d-inline"], [1, "form-label"], ["type", "text", 1, "form-control", 3, "formControlName"]],
      template: function PersonalSettingsHalfRowComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0)(1, "label", 1);
          ɵɵtext(2);
          ɵɵpipe(3, "abpLocalization");
          ɵɵelementEnd();
          ɵɵelement(4, "input", 2);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵattribute("for", ctx.name);
          ɵɵadvance();
          ɵɵtextInterpolate1("", ɵɵpipeBind1(3, 5, ctx.displayName), " ");
          ɵɵadvance(2);
          ɵɵproperty("formControlName", ctx.name);
          ɵɵattribute("id", ctx.id)("name", ctx.name);
        }
      },
      dependencies: [DefaultValueAccessor, NgControlStatus, FormControlName, ValidationDirective, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PersonalSettingsHalfRowComponent, [{
    type: Component,
    args: [{
      selector: "abp-personal-settings-half-row",
      template: ` <div class="w-50 d-inline">
    <label [attr.for]="name" class="form-label">{{ displayName | abpLocalization }} </label>
    <input
      type="text"
      [attr.id]="id"
      class="form-control"
      [attr.name]="name"
      [formControlName]="name"
    />
  </div>`,
      viewProviders: [EXTENSIBLE_FORM_VIEW_PROVIDER]
    }]
  }], () => [{
    type: FormProp,
    decorators: [{
      type: Inject,
      args: [EXTENSIONS_FORM_PROP]
    }]
  }], null);
})();
var {
  maxLength: maxLength$1,
  required: required$1,
  email: email$1
} = Validators;
var DEFAULT_PERSONAL_SETTINGS_UPDATE_FORM_PROPS = FormProp.createMany([{
  type: "string",
  name: "userName",
  displayName: "AbpIdentity::DisplayName:UserName",
  id: "username",
  validators: () => [required$1, maxLength$1(256)]
}, {
  type: "string",
  name: "name",
  displayName: "AbpIdentity::DisplayName:Name",
  id: "name",
  validators: () => [maxLength$1(64)],
  template: PersonalSettingsHalfRowComponent,
  className: "d-inline-block w-50"
}, {
  type: "string",
  name: "surname",
  displayName: "AbpIdentity::DisplayName:Surname",
  id: "surname",
  validators: () => [maxLength$1(64)],
  className: "d-inline-block w-50 ps-4",
  template: PersonalSettingsHalfRowComponent
}, {
  type: "string",
  name: "email",
  displayName: "AbpIdentity::DisplayName:Email",
  id: "email-address",
  validators: () => [required$1, email$1, maxLength$1(256)]
}, {
  type: "string",
  name: "phoneNumber",
  displayName: "AbpIdentity::DisplayName:PhoneNumber",
  id: "phone-number",
  validators: () => [maxLength$1(16)]
}]);
var DEFAULT_ACCOUNT_FORM_PROPS = {
  [
    "Account.PersonalSettingsComponent"
    /* eAccountComponents.PersonalSettings */
  ]: DEFAULT_PERSONAL_SETTINGS_UPDATE_FORM_PROPS
};
var ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS = new InjectionToken("ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS");
var PersonalSettingsComponent = class _PersonalSettingsComponent {
  constructor() {
    this.fb = inject(UntypedFormBuilder);
    this.toasterService = inject(ToasterService);
    this.profileService = inject(ProfileService);
    this.manageProfileState = inject(ManageProfileStateService);
    this.authService = inject(AuthService);
    this.confirmationService = inject(ConfirmationService);
    this.configState = inject(ConfigStateService);
    this.isPersonalSettingsChangedConfirmationActive = inject(RE_LOGIN_CONFIRMATION_TOKEN);
    this.injector = inject(Injector);
    this.logoutConfirmation = () => {
      this.authService.logout().subscribe();
    };
  }
  buildForm() {
    this.selected = this.manageProfileState.getProfile();
    if (!this.selected) {
      return;
    }
    const data = new FormPropData(this.injector, this.selected);
    this.form = generateFormFromProps(data);
  }
  ngOnInit() {
    this.buildForm();
  }
  submit() {
    if (this.form.invalid) return;
    const isLogOutConfirmMessageVisible = this.isLogoutConfirmMessageActive();
    const isRefreshTokenExists = this.authService.getRefreshToken();
    this.inProgress = true;
    this.profileService.update(this.form.value).pipe(finalize(() => this.inProgress = false)).subscribe((profile) => {
      this.manageProfileState.setProfile(profile);
      this.configState.refreshAppState();
      this.toasterService.success("AbpAccount::PersonalSettingsSaved", "Success", {
        life: 5e3
      });
      if (isRefreshTokenExists) {
        return this.authService.refreshToken();
      }
      if (isLogOutConfirmMessageVisible) {
        this.showLogoutConfirmMessage();
      }
    });
  }
  isLogoutConfirmMessageActive() {
    return this.isPersonalSettingsChangedConfirmationActive;
  }
  showLogoutConfirmMessage() {
    this.confirmationService.info("AbpAccount::PersonalSettingsChangedConfirmationModalDescription", "AbpAccount::PersonalSettingsChangedConfirmationModalTitle").pipe(filter((status) => status === Confirmation.Status.confirm)).subscribe(this.logoutConfirmation);
  }
  static {
    this.ɵfac = function PersonalSettingsComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _PersonalSettingsComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _PersonalSettingsComponent,
      selectors: [["abp-personal-settings-form"]],
      exportAs: ["abpPersonalSettingsForm"],
      features: [ɵɵProvidersFeature([{
        provide: EXTENSIONS_IDENTIFIER,
        useValue: "Account.PersonalSettingsComponent"
        /* eAccountComponents.PersonalSettings */
      }])],
      decls: 1,
      vars: 1,
      consts: [["validateOnSubmit", "", 1, "abp-md-form", 3, "formGroup"], ["validateOnSubmit", "", 1, "abp-md-form", 3, "ngSubmit", "formGroup"], [3, "selectedRecord"], ["buttonType", "submit", "iconClass", "fa fa-check", "buttonClass", "btn btn-primary color-white", 3, "loading"]],
      template: function PersonalSettingsComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵtemplate(0, PersonalSettingsComponent_Conditional_0_Template, 5, 6, "form", 0);
        }
        if (rf & 2) {
          ɵɵconditional(ctx.form ? 0 : -1);
        }
      },
      dependencies: [ɵNgNoValidate, NgControlStatusGroup, FormGroupDirective, FormSubmitDirective, ValidationGroupDirective, ButtonComponent, ExtensibleFormComponent, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PersonalSettingsComponent, [{
    type: Component,
    args: [{
      selector: "abp-personal-settings-form",
      exportAs: "abpPersonalSettingsForm",
      providers: [{
        provide: EXTENSIONS_IDENTIFIER,
        useValue: "Account.PersonalSettingsComponent"
        /* eAccountComponents.PersonalSettings */
      }],
      template: `@if (form) {\r
  <form [formGroup]="form" (ngSubmit)="submit()" validateOnSubmit class="abp-md-form">\r
    <abp-extensible-form [selectedRecord]="selected"></abp-extensible-form>\r
  \r
    <abp-button\r
      buttonType="submit"\r
      iconClass="fa fa-check"\r
      buttonClass="btn btn-primary color-white"\r
      [loading]="inProgress"\r
    >\r
      {{ 'AbpIdentity::Save' | abpLocalization }}</abp-button\r
    >\r
  </form>\r
}\r
`
    }]
  }], null, null);
})();
var ManageProfileComponent = class _ManageProfileComponent {
  constructor(profileService, manageProfileState) {
    this.profileService = profileService;
    this.manageProfileState = manageProfileState;
    this.selectedTab = 0;
    this.changePasswordKey = "Account.ChangePasswordComponent";
    this.personalSettingsKey = "Account.PersonalSettingsComponent";
    this.profile$ = this.manageProfileState.getProfile$();
  }
  ngOnInit() {
    this.profileService.get().subscribe((profile) => {
      this.manageProfileState.setProfile(profile);
      if (profile.isExternal) {
        this.hideChangePasswordTab = true;
        this.selectedTab = 1;
      }
    });
  }
  static {
    this.ɵfac = function ManageProfileComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ManageProfileComponent)(ɵɵdirectiveInject(ProfileService), ɵɵdirectiveInject(ManageProfileStateService));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ManageProfileComponent,
      selectors: [["abp-manage-profile"]],
      decls: 15,
      vars: 15,
      consts: [["id", "AbpContentToolbar"], [1, "card", "border-0", "shadow-sm", "min-h-400", 3, "abpLoading"], [1, "card-body"], [1, "row"], [1, "col-12", "col-md-3"], ["id", "nav-tab", "role", "tablist", 1, "nav", "flex-column", "nav-pills"], [1, "nav-item"], [1, "nav-item", "mb-2", 3, "click"], ["role", "tab", "href", "javascript:void(0)", 1, "nav-link", 3, "ngClass"], [1, "col-12", "col-md-9"], [1, "nav-item", 3, "click"], [1, "tab-content"], ["role", "tabpanel", 1, "tab-pane", "active"], [4, "abpReplaceableTemplate"]],
      template: function ManageProfileComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelement(0, "div", 0);
          ɵɵelementStart(1, "div", 1);
          ɵɵpipe(2, "async");
          ɵɵelementStart(3, "div", 2)(4, "div", 3)(5, "div", 4)(6, "ul", 5);
          ɵɵtemplate(7, ManageProfileComponent_Conditional_7_Template, 4, 6, "li", 6);
          ɵɵpipe(8, "async");
          ɵɵelementStart(9, "li", 7);
          ɵɵlistener("click", function ManageProfileComponent_Template_li_click_9_listener() {
            return ctx.selectedTab = 1;
          });
          ɵɵelementStart(10, "a", 8);
          ɵɵtext(11);
          ɵɵpipe(12, "abpLocalization");
          ɵɵelementEnd()()()();
          ɵɵtemplate(13, ManageProfileComponent_Conditional_13_Template, 3, 2, "div", 9);
          ɵɵpipe(14, "async");
          ɵɵelementEnd()()();
        }
        if (rf & 2) {
          let tmp_0_0;
          ɵɵadvance();
          ɵɵproperty("abpLoading", !((tmp_0_0 = ɵɵpipeBind1(2, 5, ctx.profile$)) == null ? null : tmp_0_0.userName));
          ɵɵadvance(6);
          ɵɵconditional(!ctx.hideChangePasswordTab && ɵɵpipeBind1(8, 7, ctx.profile$) ? 7 : -1);
          ɵɵadvance(3);
          ɵɵproperty("ngClass", ɵɵpureFunction1(13, _c02, ctx.selectedTab === 1));
          ɵɵadvance();
          ɵɵtextInterpolate(ɵɵpipeBind1(12, 9, "AbpAccount::PersonalSettings"));
          ɵɵadvance(2);
          ɵɵconditional(ɵɵpipeBind1(14, 11, ctx.profile$) ? 13 : -1);
        }
      },
      dependencies: [NgClass, ReplaceableTemplateDirective, LoadingDirective, ChangePasswordComponent, PersonalSettingsComponent, AsyncPipe, LocalizationPipe],
      styles: [".min-h-400[_ngcontent-%COMP%]{min-height:400px}"],
      data: {
        animation: [trigger("fadeIn", [transition(":enter", useAnimation(fadeIn))])]
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ManageProfileComponent, [{
    type: Component,
    args: [{
      selector: "abp-manage-profile",
      animations: [trigger("fadeIn", [transition(":enter", useAnimation(fadeIn))])],
      template: `<div id="AbpContentToolbar"></div>\r
<div class="card border-0 shadow-sm min-h-400" [abpLoading]="!(profile$ | async)?.userName">\r
  <div class="card-body">\r
    <div class="row">\r
      <div class="col-12 col-md-3">\r
        <ul class="nav flex-column nav-pills" id="nav-tab" role="tablist">\r
          @if (!hideChangePasswordTab && (profile$ | async)) {\r
            <li class="nav-item" (click)="selectedTab = 0">\r
              <a\r
                class="nav-link"\r
                [ngClass]="{ active: selectedTab === 0 }"\r
                role="tab"\r
                href="javascript:void(0)"\r
                >{{ 'AbpUi::ChangePassword' | abpLocalization }}</a\r
              >\r
            </li>\r
          }\r
          <li class="nav-item mb-2" (click)="selectedTab = 1">\r
            <a\r
              class="nav-link"\r
              [ngClass]="{ active: selectedTab === 1 }"\r
              role="tab"\r
              href="javascript:void(0)"\r
              >{{ 'AbpAccount::PersonalSettings' | abpLocalization }}</a\r
            >\r
          </li>\r
        </ul>\r
      </div>\r
      @if (profile$ | async) {\r
        <div class="col-12 col-md-9">\r
          @if (selectedTab === 0) {\r
            <div class="tab-content" [@fadeIn]>\r
              <div class="tab-pane active" role="tabpanel">\r
                <h4>\r
                  {{ 'AbpIdentity::ChangePassword' | abpLocalization }}\r
                  <hr />\r
                </h4>\r
                <abp-change-password-form\r
                  *abpReplaceableTemplate="{\r
                    componentKey: changePasswordKey\r
                  }"\r
                ></abp-change-password-form>\r
              </div>\r
            </div>\r
          }\r
          @if (selectedTab === 1) {\r
            <div class="tab-content" [@fadeIn]>\r
              <div class="tab-pane active" role="tabpanel">\r
                <h4>\r
                  {{ 'AbpIdentity::PersonalSettings' | abpLocalization }}\r
                  <hr />\r
                </h4>\r
                <abp-personal-settings-form\r
                  *abpReplaceableTemplate="{\r
                    componentKey: personalSettingsKey\r
                  }"\r
                ></abp-personal-settings-form>\r
              </div>\r
            </div>\r
          }\r
        </div>\r
      }\r
    </div>\r
  </div>\r
</div>\r
`,
      styles: [".min-h-400{min-height:400px}\n"]
    }]
  }], () => [{
    type: ProfileService
  }, {
    type: ManageProfileStateService
  }], null);
})();
var {
  maxLength,
  required,
  email
} = Validators;
var RegisterComponent = class _RegisterComponent {
  constructor(fb, accountService, configState, toasterService, authService, injector) {
    this.fb = fb;
    this.accountService = accountService;
    this.configState = configState;
    this.toasterService = toasterService;
    this.authService = authService;
    this.injector = injector;
    this.isSelfRegistrationEnabled = true;
    this.authWrapperKey = "Account.AuthWrapperComponent";
  }
  ngOnInit() {
    this.init();
    this.buildForm();
  }
  init() {
    this.isSelfRegistrationEnabled = (this.configState.getSetting("Abp.Account.IsSelfRegistrationEnabled") || "").toLowerCase() !== "false";
    if (!this.isSelfRegistrationEnabled) {
      this.toasterService.warn({
        key: "AbpAccount::SelfRegistrationDisabledMessage",
        defaultValue: "Self registration is disabled."
      }, "", {
        life: 1e4
      });
      return;
    }
  }
  buildForm() {
    this.form = this.fb.group({
      username: ["", [required, maxLength(255)]],
      password: ["", [required, ...getPasswordValidators(this.injector)]],
      email: ["", [required, email]]
    });
  }
  onSubmit() {
    if (this.form.invalid) return;
    this.inProgress = true;
    const newUser = {
      userName: this.form.get("username")?.value,
      password: this.form.get("password")?.value,
      emailAddress: this.form.get("email")?.value,
      appName: "Angular"
    };
    this.accountService.register(newUser).pipe(switchMap(() => this.authService.login({
      username: newUser.userName,
      password: newUser.password,
      redirectUrl: getRedirectUrl(this.injector)
    })), catchError((err) => {
      this.toasterService.error(err.error?.error_description || err.error?.error.message || "AbpAccount::DefaultErrorMessage", "", {
        life: 7e3
      });
      return throwError(err);
    }), finalize(() => this.inProgress = false)).subscribe();
  }
  static {
    this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _RegisterComponent)(ɵɵdirectiveInject(UntypedFormBuilder), ɵɵdirectiveInject(AccountService), ɵɵdirectiveInject(ConfigStateService), ɵɵdirectiveInject(ToasterService), ɵɵdirectiveInject(AuthService), ɵɵdirectiveInject(Injector));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _RegisterComponent,
      selectors: [["abp-register"]],
      decls: 10,
      vars: 10,
      consts: [["routerLink", "/account/login", 1, "text-decoration-none"], ["validateOnSubmit", "", 1, "mt-4", 3, "formGroup"], ["validateOnSubmit", "", 1, "mt-4", 3, "ngSubmit", "formGroup"], [1, "mb-3", "form-group"], ["for", "input-user-name", 1, "form-label"], ["autofocus", "", "type", "text", "id", "input-user-name", "formControlName", "username", "autocomplete", "username", 1, "form-control"], ["for", "input-email-address", 1, "form-label"], ["type", "email", "id", "input-email-address", "formControlName", "email", 1, "form-control"], ["for", "input-password", 1, "form-label"], ["type", "password", "id", "input-password", "formControlName", "password", "autocomplete", "current-password", 1, "form-control"], ["buttonType", "submit", "name", "Action", "buttonClass", "btn-block btn-lg mt-3 btn btn-primary", 3, "loading"]],
      template: function RegisterComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "h4");
          ɵɵtext(1);
          ɵɵpipe(2, "abpLocalization");
          ɵɵelementEnd();
          ɵɵelementStart(3, "strong");
          ɵɵtext(4);
          ɵɵpipe(5, "abpLocalization");
          ɵɵelementStart(6, "a", 0);
          ɵɵtext(7);
          ɵɵpipe(8, "abpLocalization");
          ɵɵelementEnd()();
          ɵɵtemplate(9, RegisterComponent_Conditional_9_Template, 25, 14, "form", 1);
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵtextInterpolate(ɵɵpipeBind1(2, 4, "AbpAccount::Register"));
          ɵɵadvance(3);
          ɵɵtextInterpolate1(" ", ɵɵpipeBind1(5, 6, "AbpAccount::AlreadyRegistered"), " ");
          ɵɵadvance(3);
          ɵɵtextInterpolate(ɵɵpipeBind1(8, 8, "AbpAccount::Login"));
          ɵɵadvance(2);
          ɵɵconditional(ctx.isSelfRegistrationEnabled ? 9 : -1);
        }
      },
      dependencies: [ɵNgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterLink, AutofocusDirective, FormSubmitDirective, ValidationGroupDirective, ValidationDirective, ButtonComponent, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RegisterComponent, [{
    type: Component,
    args: [{
      selector: "abp-register",
      template: `<h4>{{ 'AbpAccount::Register' | abpLocalization }}</h4>\r
<strong>\r
  {{ 'AbpAccount::AlreadyRegistered' | abpLocalization }}\r
  <a class="text-decoration-none" routerLink="/account/login">{{\r
    'AbpAccount::Login' | abpLocalization\r
  }}</a>\r
</strong>\r
@if (isSelfRegistrationEnabled) {\r
  <form\r
    [formGroup]="form"\r
    (ngSubmit)="onSubmit()"\r
    validateOnSubmit\r
    class="mt-4"\r
  >\r
    <div class="mb-3 form-group">\r
      <label for="input-user-name" class="form-label">{{\r
        'AbpAccount::UserName' | abpLocalization\r
      }}</label\r
      ><span> * </span\r
      ><input\r
        autofocus\r
        type="text"\r
        id="input-user-name"\r
        class="form-control"\r
        formControlName="username"\r
        autocomplete="username"\r
      />\r
    </div>\r
    <div class="mb-3 form-group">\r
      <label for="input-email-address" class="form-label">{{\r
        'AbpAccount::EmailAddress' | abpLocalization\r
      }}</label\r
      ><span> * </span\r
      ><input type="email" id="input-email-address" class="form-control" formControlName="email" />\r
    </div>\r
    <div class="mb-3 form-group">\r
      <label for="input-password" class="form-label">{{\r
        'AbpAccount::Password' | abpLocalization\r
      }}</label\r
      ><span> * </span\r
      ><input\r
        type="password"\r
        id="input-password"\r
        class="form-control"\r
        formControlName="password"\r
        autocomplete="current-password"\r
      />\r
    </div>\r
    <abp-button\r
      [loading]="inProgress"\r
      buttonType="submit"\r
      name="Action"\r
      buttonClass="btn-block btn-lg mt-3 btn btn-primary"\r
    >\r
      {{ 'AbpAccount::Register' | abpLocalization }}\r
    </abp-button>\r
  </form>\r
}\r
`
    }]
  }], () => [{
    type: UntypedFormBuilder
  }, {
    type: AccountService
  }, {
    type: ConfigStateService
  }, {
    type: ToasterService
  }, {
    type: AuthService
  }, {
    type: Injector
  }], null);
})();
var PASSWORD_FIELDS = ["password", "confirmPassword"];
var ResetPasswordComponent = class _ResetPasswordComponent {
  constructor(fb, accountService, route, router, injector) {
    this.fb = fb;
    this.accountService = accountService;
    this.route = route;
    this.router = router;
    this.injector = injector;
    this.inProgress = false;
    this.isPasswordReset = false;
    this.mapErrorsFn = (errors, groupErrors, control) => {
      if (PASSWORD_FIELDS.indexOf(String(control?.name)) < 0) return errors;
      return errors.concat(groupErrors.filter(({
        key
      }) => key === "passwordMismatch"));
    };
  }
  ngOnInit() {
    this.route.queryParams.subscribe(({
      userId,
      resetToken
    }) => {
      if (!userId || !resetToken) this.router.navigateByUrl("/account/login");
      this.form = this.fb.group({
        userId: [userId, [Validators.required]],
        resetToken: [resetToken, [Validators.required]],
        password: ["", [Validators.required, ...getPasswordValidators(this.injector)]],
        confirmPassword: ["", [Validators.required, ...getPasswordValidators(this.injector)]]
      }, {
        validators: [comparePasswords(PASSWORD_FIELDS)]
      });
    });
  }
  onSubmit() {
    if (this.form.invalid || this.inProgress) return;
    this.inProgress = true;
    this.accountService.resetPassword({
      userId: this.form.get("userId")?.value,
      resetToken: this.form.get("resetToken")?.value,
      password: this.form.get("password")?.value
    }).pipe(finalize(() => this.inProgress = false)).subscribe(() => {
      this.isPasswordReset = true;
    });
  }
  static {
    this.ɵfac = function ResetPasswordComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ResetPasswordComponent)(ɵɵdirectiveInject(UntypedFormBuilder), ɵɵdirectiveInject(AccountService), ɵɵdirectiveInject(ActivatedRoute), ɵɵdirectiveInject(Router), ɵɵdirectiveInject(Injector));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ResetPasswordComponent,
      selectors: [["abp-reset-password"]],
      decls: 5,
      vars: 4,
      consts: [["validateOnSubmit", "", 3, "formGroup", "mapErrorsFn"], ["validateOnSubmit", "", 3, "ngSubmit", "formGroup", "mapErrorsFn"], [1, "mb-3", "form-group"], ["for", "input-password", 1, "form-label"], ["type", "password", "id", "input-password", "formControlName", "password", 1, "form-control"], ["for", "input-confirm-password", 1, "form-label"], ["type", "password", "id", "input-confirm-password", "formControlName", "confirmPassword", 1, "form-control"], ["type", "button", "routerLink", "/account/login", 1, "me-2", "btn", "btn-outline-primary"], ["buttonType", "submit", "buttonClass", "me-2 btn btn-primary", 3, "click", "loading"], ["routerLink", "/account/login"], [1, "d-block", "mt-2", "mb-3", "btn", "btn-primary"]],
      template: function ResetPasswordComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "h4");
          ɵɵtext(1);
          ɵɵpipe(2, "abpLocalization");
          ɵɵelementEnd();
          ɵɵtemplate(3, ResetPasswordComponent_Conditional_3_Template, 24, 18, "form", 0)(4, ResetPasswordComponent_Conditional_4_Template, 7, 6);
        }
        if (rf & 2) {
          ɵɵadvance();
          ɵɵtextInterpolate(ɵɵpipeBind1(2, 2, "AbpAccount::ResetPassword"));
          ɵɵadvance(2);
          ɵɵconditional(!ctx.isPasswordReset ? 3 : 4);
        }
      },
      dependencies: [ɵNgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterLink, FormSubmitDirective, ValidationGroupDirective, ValidationDirective, ButtonComponent, LocalizationPipe],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ResetPasswordComponent, [{
    type: Component,
    args: [{
      selector: "abp-reset-password",
      template: `<h4>{{ 'AbpAccount::ResetPassword' | abpLocalization }}</h4>\r
\r
@if (!isPasswordReset) {\r
  <form [formGroup]="form" [mapErrorsFn]="mapErrorsFn" (ngSubmit)="onSubmit()" validateOnSubmit>\r
    <p>{{ 'AbpAccount::ResetPassword_Information' | abpLocalization }}</p>\r
    <div class="mb-3 form-group">\r
      <label for="input-password" class="form-label">{{\r
        'AbpAccount::Password' | abpLocalization\r
      }}</label\r
      ><span> * </span>\r
      <input type="password" id="input-password" class="form-control" formControlName="password" />\r
    </div>\r
    <div class="mb-3 form-group">\r
      <label for="input-confirm-password" class="form-label">{{\r
        'AbpAccount::ConfirmPassword' | abpLocalization\r
      }}</label\r
      ><span> * </span>\r
      <input\r
        type="password"\r
        id="input-confirm-password"\r
        class="form-control"\r
        formControlName="confirmPassword"\r
      />\r
    </div>\r
    <button class="me-2 btn btn-outline-primary" type="button" routerLink="/account/login">\r
      {{ 'AbpAccount::Cancel' | abpLocalization }}\r
    </button>\r
    <abp-button\r
      buttonType="submit"\r
      buttonClass="me-2 btn btn-primary"\r
      [loading]="inProgress"\r
      (click)="onSubmit()"\r
    >\r
      {{ 'AbpAccount::Submit' | abpLocalization }}\r
    </abp-button>\r
  </form>\r
} @else {\r
  <p>\r
    {{ 'AbpAccount::YourPasswordIsSuccessfullyReset' | abpLocalization }}\r
  </p>\r
\r
  <a routerLink="/account/login">\r
    <button class="d-block mt-2 mb-3 btn btn-primary">\r
      {{ 'AbpAccount::BackToLogin' | abpLocalization }}\r
    </button>\r
  </a>\r
}\r
`
    }]
  }], () => [{
    type: UntypedFormBuilder
  }, {
    type: AccountService
  }, {
    type: ActivatedRoute
  }, {
    type: Router
  }, {
    type: Injector
  }], null);
})();
var AuthenticationFlowGuard = class _AuthenticationFlowGuard {
  constructor() {
    this.authService = inject(AuthService);
  }
  canActivate() {
    if (this.authService.isInternalAuth) return true;
    this.authService.navigateToLogin();
    return false;
  }
  static {
    this.ɵfac = function AuthenticationFlowGuard_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AuthenticationFlowGuard)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AuthenticationFlowGuard,
      factory: _AuthenticationFlowGuard.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthenticationFlowGuard, [{
    type: Injectable
  }], null, null);
})();
var authenticationFlowGuard = () => {
  const authService = inject(AuthService);
  if (authService.isInternalAuth) return true;
  authService.navigateToLogin();
  return false;
};
var AccountExtensionsGuard = class _AccountExtensionsGuard {
  constructor() {
    this.configState = inject(ConfigStateService);
    this.extensions = inject(ExtensionsService);
  }
  canActivate() {
    const config = {
      optional: true
    };
    const editFormContributors = inject(ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS, config) || {};
    return getObjectExtensionEntitiesFromStore(this.configState, "Identity").pipe(map((entities) => ({
      [
        "Account.PersonalSettingsComponent"
        /* eAccountComponents.PersonalSettings */
      ]: entities.User
    })), mapEntitiesToContributors(this.configState, "AbpIdentity"), tap((objectExtensionContributors) => {
      mergeWithDefaultProps(this.extensions.editFormProps, DEFAULT_ACCOUNT_FORM_PROPS, objectExtensionContributors.editForm, editFormContributors);
    }), map(() => true));
  }
  static {
    this.ɵfac = function AccountExtensionsGuard_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AccountExtensionsGuard)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _AccountExtensionsGuard,
      factory: _AccountExtensionsGuard.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountExtensionsGuard, [{
    type: Injectable
  }], null, null);
})();
var accountExtensionsResolver = () => {
  const configState = inject(ConfigStateService);
  const extensions = inject(ExtensionsService);
  const config = {
    optional: true
  };
  const editFormContributors = inject(ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS, config) || {};
  return getObjectExtensionEntitiesFromStore(configState, "Identity").pipe(map((entities) => ({
    [
      "Account.PersonalSettingsComponent"
      /* eAccountComponents.PersonalSettings */
    ]: entities.User
  })), mapEntitiesToContributors(configState, "AbpIdentity"), tap((objectExtensionContributors) => {
    mergeWithDefaultProps(extensions.editFormProps, DEFAULT_ACCOUNT_FORM_PROPS, objectExtensionContributors.editForm, editFormContributors);
  }));
};
var canActivate = [authenticationFlowGuard];
var routes = [{
  path: "",
  pathMatch: "full",
  redirectTo: "login"
}, {
  path: "",
  component: RouterOutletComponent,
  children: [{
    path: "login",
    component: ReplaceableRouteContainerComponent,
    canActivate,
    data: {
      replaceableComponent: {
        key: "Account.LoginComponent",
        defaultComponent: LoginComponent
      }
    },
    title: "AbpAccount::Login"
  }, {
    path: "register",
    component: ReplaceableRouteContainerComponent,
    canActivate,
    data: {
      replaceableComponent: {
        key: "Account.RegisterComponent",
        defaultComponent: RegisterComponent
      }
    },
    title: "AbpAccount::Register"
  }, {
    path: "forgot-password",
    component: ReplaceableRouteContainerComponent,
    canActivate,
    data: {
      replaceableComponent: {
        key: "Account.ForgotPasswordComponent",
        defaultComponent: ForgotPasswordComponent
      }
    },
    title: "AbpAccount::ForgotPassword"
  }, {
    path: "reset-password",
    component: ReplaceableRouteContainerComponent,
    canActivate: [],
    data: {
      tenantBoxVisible: false,
      replaceableComponent: {
        key: "Account.ResetPasswordComponent",
        defaultComponent: ResetPasswordComponent
      }
    },
    title: "AbpAccount::ResetPassword"
  }, {
    path: "manage",
    component: ReplaceableRouteContainerComponent,
    canActivate: [authGuard],
    resolve: [accountExtensionsResolver],
    data: {
      replaceableComponent: {
        key: "Account.ManageProfileComponent",
        defaultComponent: ManageProfileComponent
      }
    },
    title: "AbpAccount::MyAccount"
  }]
}];
var AccountRoutingModule = class _AccountRoutingModule {
  static {
    this.ɵfac = function AccountRoutingModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AccountRoutingModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _AccountRoutingModule,
      imports: [RouterModule],
      exports: [RouterModule]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [RouterModule.forChild(routes), RouterModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountRoutingModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();
function accountConfigOptionsFactory(options) {
  return __spreadValues({
    redirectUrl: "/"
  }, options);
}
var declarations = [LoginComponent, RegisterComponent, ChangePasswordComponent, ManageProfileComponent, PersonalSettingsComponent, ForgotPasswordComponent, ResetPasswordComponent, PersonalSettingsHalfRowComponent];
var AccountModule = class _AccountModule {
  static forChild(options = {}) {
    return {
      ngModule: _AccountModule,
      providers: [AuthenticationFlowGuard, {
        provide: ACCOUNT_CONFIG_OPTIONS,
        useValue: options
      }, {
        provide: "ACCOUNT_OPTIONS",
        useFactory: accountConfigOptionsFactory,
        deps: [ACCOUNT_CONFIG_OPTIONS]
      }, {
        provide: RE_LOGIN_CONFIRMATION_TOKEN,
        useValue: options.isPersonalSettingsChangedConfirmationActive ?? true
      }, {
        provide: ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS,
        useValue: options.editFormPropContributors
      }, AccountExtensionsGuard]
    };
  }
  static forLazy(options = {}) {
    return new LazyModuleFactory(_AccountModule.forChild(options));
  }
  static {
    this.ɵfac = function AccountModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _AccountModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _AccountModule,
      declarations: [LoginComponent, RegisterComponent, ChangePasswordComponent, ManageProfileComponent, PersonalSettingsComponent, ForgotPasswordComponent, ResetPasswordComponent, PersonalSettingsHalfRowComponent],
      imports: [CoreModule, AccountRoutingModule, ThemeSharedModule, NgbDropdownModule, NgxValidateCoreModule, ExtensibleModule],
      exports: [LoginComponent, RegisterComponent, ChangePasswordComponent, ManageProfileComponent, PersonalSettingsComponent, ForgotPasswordComponent, ResetPasswordComponent, PersonalSettingsHalfRowComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [CoreModule, AccountRoutingModule, ThemeSharedModule, NgbDropdownModule, NgxValidateCoreModule, ExtensibleModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AccountModule, [{
    type: NgModule,
    args: [{
      declarations: [...declarations],
      imports: [CoreModule, AccountRoutingModule, ThemeSharedModule, NgbDropdownModule, NgxValidateCoreModule, ExtensibleModule],
      exports: [...declarations]
    }]
  }], null, null);
})();
export {
  ACCOUNT_CONFIG_OPTIONS,
  ACCOUNT_EDIT_FORM_PROP_CONTRIBUTORS,
  AccountExtensionsGuard,
  AccountModule,
  AuthenticationFlowGuard,
  ChangePasswordComponent,
  DEFAULT_ACCOUNT_FORM_PROPS,
  ForgotPasswordComponent,
  LoginComponent,
  ManageProfileComponent,
  ManageProfileStateService,
  PersonalSettingsComponent,
  PersonalSettingsHalfRowComponent,
  RE_LOGIN_CONFIRMATION_TOKEN,
  RegisterComponent,
  ResetPasswordComponent,
  accountConfigOptionsFactory,
  accountExtensionsResolver,
  authenticationFlowGuard,
  getRedirectUrl
};
//# sourceMappingURL=@abp_ng__account.js.map
