import { AsyncPipe, JsonPipe } from '@angular/common';
import { Component, computed, input, output, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { LeafletModule } from '@bluehalo/ngx-leaflet';
import { LeafletDrawModule } from '@bluehalo/ngx-leaflet-draw';
import {
  divIcon,
  DrawEvents,
  featureGroup,
  FeatureGroup,
  LatLng,
  latLng,
  LatLngTuple,
  Layer,
  LayerGroup,
  Map,
  MapOptions,
  marker,
  polyline,
  tileLayer,
} from 'leaflet';

import { map, skip } from 'rxjs';

export interface changeNodeDto {
  event: any;
  layers: FeatureGroup;
}
@Component({
  selector: 'app-map',
  template: `
    {{ draw$ | async | json }}
    <div
      class="h-full"
      leaflet
      [leafletOptions]="options$()"
      [leafletZoom]="zoom$()"
      [leafletCenter]="center$()"
      (leafletMapReady)="setMap($event)"
    >
      @if (showDraw()) {
      <div
        leafletDraw
        [leafletDrawOptions]="drawOptions()"
        (leafletDrawCreated)="onDrawCreated($event)"
        (leafletDrawEditStop)="onDrawEdit($event)"
        (leafletDrawDeleteStop)="onDrawDelete($event)"
      ></div>
      }
      <div [leafletLayer]="drawnItems"></div>
    </div>
  `,
  standalone: true,
  imports: [LeafletModule, LeafletDrawModule, JsonPipe, AsyncPipe],
})
export class MapComponent {
  LayerChange = input<boolean>(true);

  nodesId: number | null = null;
  linesId: number | null = null;
  LayerChange$ = toObservable(this.LayerChange).pipe(
    takeUntilDestroyed(),
    skip(1),
    map(data => {
      this.changeLayer();
    })
  );

  nodes = input<Layer[]>();
  nodes$ = toObservable(this.nodes).pipe(
    takeUntilDestroyed(),
    map(data => {
      if (data && data.length > 0) {
        if (this.nodesId) {
          this.drawnItems.removeLayer(this.nodesId);
        }
        const group = new LayerGroup(data);
        this.drawnItems.addLayer(group);
        this.nodesId = this.drawnItems.getLayerId(group);
      }
    })
  );

  line = input<Layer[]>();
  line$ = toObservable(this.line).pipe(
    takeUntilDestroyed(),
    map(data => {
      if (data && data.length > 0) {
        if (this.linesId) {
          this.drawnItems.removeLayer(this.linesId);
        }
        const group = new LayerGroup(data);
        this.drawnItems.addLayer(group);
        this.linesId = this.drawnItems.getLayerId(group);
      }
    })
  );

  draw = input<{
    polyline?: boolean;
    marker?: boolean;
    circlemarker?: boolean;
    rectangle?: boolean;
    polygon?: boolean;
    circle?: boolean;
  }>();
  draw$ = toObservable(this.draw).pipe(
    map(data => {
      if (data) {
        this.drawOptions.update(value => {
          return {
            ...value,
            draw: {
              polyline: data.polyline,
              marker: data.marker,
              circlemarker: data.circlemarker,
              rectangle: data.rectangle,
              polygon: data.polygon,
              circle: data.circle,
            },
          };
        });
      }
    })
  );
  showDraw = input(false);
  change_nodes = output<changeNodeDto>();

  drawnItems: FeatureGroup = featureGroup();

  drawOptions = signal<any>({
    position: 'topright',
    draw: {
      polyline: false,
      marker: false,
      circlemarker: false,
      rectangle: false,
      polygon: false,
      circle: false,
    },
    edit: {
      featureGroup: this.drawnItems,
    },
    remove: false,
  });

  public onDrawCreated(e: DrawEvents.Created) {
    const layer = e.layer;
    this.drawnItems.addLayer(layer);
    this.change_nodes.emit({ event: e, layers: this.drawnItems });
  }
  public onDrawDelete(e: DrawEvents.DeleteStop) {
    this.change_nodes.emit({ event: e, layers: this.drawnItems });
  }
  public onDrawEdit(e: DrawEvents.EditStop) {
    this.change_nodes.emit({ event: e, layers: this.drawnItems });
  }

  GoogleSateliteLyr = tileLayer('http://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
    maxZoom: 20,
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
  });
  OpenStreetLyr = tileLayer('http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 22,
    attribution: '..',
  });

  baseOptions = signal<MapOptions>({
    layers: [this.OpenStreetLyr],
    zoom: 5,
    center: latLng(34.71344, 38.6883),
    zoomControl: false,
  });
  options = input<MapOptions>({
    layers: [this.OpenStreetLyr],
    zoom: 5,
    center: latLng(34.71344, 38.6883),
  });
  changeOptions = signal<MapOptions>({});
  options$ = computed<MapOptions>(() => {
    const o = {
      ...this.baseOptions(),
      ...this.options(),
      ...this.changeOptions(),
    };
    return o;
  });
  zoom$ = computed(() => {
    return this.options$().zoom;
  });
  center$ = computed<LatLng>(() => {
    return this.options$().center as LatLng;
  });

  map: Map;
  setMap(map: Map) {
    this.map = map;
  }

  changeLayer() {
    if (this.map.hasLayer(this.GoogleSateliteLyr)) {
      this.map.removeLayer(this.GoogleSateliteLyr);
      this.map.addLayer(this.OpenStreetLyr);
    } else {
      this.map.removeLayer(this.OpenStreetLyr);
      this.map.addLayer(this.GoogleSateliteLyr);
    }
  }

  ngOnInit(): void {
    this.nodes$.subscribe();
    this.draw$.subscribe();
    this.line$.subscribe();
    this.LayerChange$.subscribe();
  }
}

export const CustomMarker = (point: {
  latlang: LatLngTuple;
  icon: string;
  popup?: () => HTMLElement;
}) => {
  const r = marker(point.latlang, {
    icon: divIcon({
      iconSize: [36, 36],
      className: 'bg-transparent border-none',
      popupAnchor: [0, 130],
      html: point.icon,
    }),
  });
  if (point.popup) {
    r.bindPopup(point.popup());
  }
  return r;
};

export const CustomLine = (data: { line: any; color?: string }) => {
  let line = data.line;
  const color = data.color ?? 'black';
  const r = polyline(line, { color: color });
  return r;
};
