<div class="relative">
  <mat-icon class="absolute top-4 left-4" [routerLink]="['/track-accounts']"
    >keyboard_arrow_left</mat-icon
  >
  <h2 class="text-center bg-white rounded-xl shadow-lg p-4 text-black">
    {{ 'Increase Vehicle' | i18n }}
  </h2>
</div>
<div class="flex flex-col items-center min-h-screen mt-4">
  <mat-card
    class="p-2 md:p-6 rounded-lg shadow-lg transition-all duration-500 card-blue w-10/12 max-w-md"
  >
    <form [formGroup]="form" class="p-4" (ngSubmit)="onSubmit()">
      <div
        class="bg-main_perple text-white bg-opacity-40 rounded-lg p-4 mb-6 flex justify-center flex-col gap-2"
      >
        <div class="text-center">
          <mat-icon class="size-6"> directions_car</mat-icon>
          <span class=""> {{ 'UserPortal:CurrentVehicleCount' | i18n }}</span>
        </div>
        <div class="text-center font-semibold">{{ vehicles().length }} {{ 'vehicle' | i18n }}</div>
      </div>

      <mat-label class="px-2">{{ 'UserPortal:promoCode' | i18n }}</mat-label>
      <mat-form-field>
        <input
          matInput
          type="text"
          [placeholder]="'UserPortal:promoCode' | i18n"
          formControlName="promoCode"
        />
      </mat-form-field>

      <div class="text-main_dark_blue font-semibold mb-4">
        {{ 'UserPortal:add new vihicle here' | i18n }}
      </div>

      <div>
        <div class="mb-4">
          <div class="flex items-center">
            <button mat-mini-fab class="!bg-main_perple me-4" (click)="addVehiclesDialog()">
              <mat-icon class="!text-white">add</mat-icon>
            </button>
            <div class="text-main_dark_blue">{{ 'UserPortal:add new vihicle' | i18n }}</div>
          </div>
        </div>
        <div>
          @for (item of form.controls.trackVehicles.value; track $index) {
          <div
            class="p-4 mb-4 rounded-lg border-2 shadow-xl text-main_gray border-main_gray border-opacity-35 bg-white"
          >
            <div class="flex justify-between items-center">
              <div class="text-main_dark_blue">
                {{ 'UserPortal:Vehicle' | i18n }} {{ $index + 1 }}
              </div>
              <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
              <mat-menu #menu>
                <button mat-menu-item (click)="addVehiclesDialog($index)">
                  <mat-icon>edit</mat-icon> <span>{{ 'edit' | i18n }}</span>
                </button>
                <button mat-menu-item (click)="remove($index)">
                  <mat-icon>delete</mat-icon> <span>{{ 'delete' | i18n }}</span>
                </button>
              </mat-menu>
            </div>
            <div class="my-2">
              <span>{{ 'UserPortal:licensePlateSubClass' | i18n }}:</span>
              <span class="text-main_red">
                {{ item.licensePlateSubClass }}
              </span>
            </div>
            <div class="my-2">
              <span>{{ 'UserPortal:licensePlateSerial' | i18n }}:</span>
              <span class="text-main_red">
                {{ item.licensePlateSerial }}
              </span>
            </div>
            <div class="my-2">
              <span>{{ 'UserPortal:consumptionRate' | i18n }}:</span>
              <span class="text-main_red">
                {{ item.consumptionRate }}{{ 'UserPortal:Km' | i18n }}/{{ totalCapacity
                }}{{ 'UserPortal:L' | i18n }}
              </span>
            </div>
            <div class="flex items-center my-2">
              <span>{{ 'UserPortal:color' | i18n }}:</span>
              <div
                class="inline-block mx-2 rounded-full size-6"
                [ngStyle]="{ background: item.color }"
              ></div>
            </div>
          </div>
          }
        </div>
      </div>
      <div class="flex justify-center mt-6">
        <button mat-button mat-flat-button type="submit" [disabled]="!form.valid">
          {{ 'UserPortal:confirm' | i18n }}
        </button>
      </div>
    </form>
  </mat-card>
</div>
