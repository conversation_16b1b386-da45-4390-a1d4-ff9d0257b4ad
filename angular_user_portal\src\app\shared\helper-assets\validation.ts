import { Signal } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormArray,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { map, Observable, of, tap } from 'rxjs';

export const customValidatitors = {
  minLength(min: number): ValidatorFn | any {
    return (control: AbstractControl[]) => {
      if (!(control instanceof FormArray)) return;
      return control.length < min ? { minLength: true } : null;
    };
  },
  userCountValidation(selectedPlan: Signal<any>): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      return of(control.value < selectedPlan() ? { minimumObserversCount: true } : null);
    };
  },
  smsBundleValidation(withSms: Signal<boolean>): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      return of(!control.value && withSms() ? { smsBundleRequired: true } : null);
    };
  },
};
