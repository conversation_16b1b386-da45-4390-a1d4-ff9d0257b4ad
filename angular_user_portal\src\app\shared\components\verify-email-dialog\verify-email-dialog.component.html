<div class="p-6 bg-white rounded-lg shadow-lg text-sm">
  <h2 class="font-semibold text-indigo-600">
    {{ (data.isVerified ? 'UserPortal:EmailIsVerifyed' : 'UserPortal:VerifyEmail') | i18n }}
  </h2>

  <div class="border-t border-gray-300 my-3"></div>

  @if (!data.isVerified||verify()) {
  <div class="mb-4">
    <p class="mb-3">{{ 'UserPortal:EnterEmailToVerify' | i18n }}</p>
    <mat-form-field appearance="outline" class="w-full">
      <input matInput type="email" [(ngModel)]="email" [placeholder]="'UserPortal:Email' | i18n" />
    </mat-form-field>
  </div>
  } @else {
  <div>
    {{ 'UserPortal:EmailIsVerifyedpanner' | i18n }}
  </div>
  }

  <div class="flex justify-end gap-3 mt-4">
    <button
      mat-button
      (click)="closeDialog()"
      class="px-4 py-1 border border-gray-300 rounded-md hover:bg-gray-50"
    >
      {{ 'UserPortal:Cancel' | i18n }}
    </button>
    <button mat-button mat-flat-button (click)="submit()" class="">
      {{ (data.isVerified && !verify() ? 'UserPortal:change' : 'UserPortal:Submit') | i18n }}
    </button>
  </div>
</div>
