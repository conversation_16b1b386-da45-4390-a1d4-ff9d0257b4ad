import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { IdentityRoleDto, IdentityRoleService } from '@abp/ng.identity/proxy';
import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import {
  AlertService,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { of } from 'rxjs';
import { role } from '../roles.model';

@Component({
  selector: 'app-roles-create-dialog',
  standalone: true,
  imports: [LocalizationModule, MatDialogContent, TtwrFormComponent, MatDialogTitle],
  templateUrl: './roles-update-dialog.component.html',
  styleUrl: './roles-update-dialog.component.scss',
})
export class RolesUpdateDialogComponent {
  private identityRole = inject(IdentityRoleService);
  private localization = inject(LocalizationService);
  private ref = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private data = inject<IdentityRoleDto>(MAT_DIALOG_DATA);
  private loading = inject(LOADING);

  protected config = role.form({
    submitAction: {
      onSubmit: value => {
        this.loading.set(true);
        this.identityRole
          .update(this.data.id!, value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.loading.set(false);
              this.ref.close(true);
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    fields: {
      name: {
        validators: [requiredValidator],
      },
    },
    actions: [
      {
        label: 'Cancel',
        delegateFunc: () => {
          this.ref.close();
        },
      },
    ],
    viewFunc: () => of(this.data),
  });
}
