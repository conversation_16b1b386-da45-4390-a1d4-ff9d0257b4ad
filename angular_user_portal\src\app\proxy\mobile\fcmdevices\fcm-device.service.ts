import type { FcmDeviceRegisterDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FcmDeviceService {
  apiName = 'Default';
  

  register = (registerDto: FcmDeviceRegisterDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/fcmDevice/register',
      body: registerDto,
    },
    { apiName: this.apiName,...config });
  

  removeDeviceToken = (deviceToken: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/fcmDevice/deviceToken',
      params: { deviceToken },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
