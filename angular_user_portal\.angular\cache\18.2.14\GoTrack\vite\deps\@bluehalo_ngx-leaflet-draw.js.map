{"version": 3, "sources": ["../../../../../../node_modules/leaflet-draw/dist/leaflet.draw.js", "../../../../../../node_modules/@bluehalo/ngx-leaflet-draw/fesm2022/bluehalo-ngx-leaflet-draw.mjs"], "sourcesContent": ["/*\n Leaflet.draw 1.0.2, a plugin that adds drawing and editing tools to Leaflet powered maps.\n (c) 2012-2017, <PERSON>, <PERSON>, Smartrak, Leaflet\n\n https://github.com/Leaflet/Leaflet.draw\n http://leafletjs.com\n */\n!function (t, e, i) {\n  function o(t, e) {\n    for (; (t = t.parentElement) && !t.classList.contains(e););\n    return t;\n  }\n  L.drawVersion = \"1.0.2\", L.Draw = {}, L.drawLocal = {\n    draw: {\n      toolbar: {\n        actions: {\n          title: \"Cancel drawing\",\n          text: \"Cancel\"\n        },\n        finish: {\n          title: \"Finish drawing\",\n          text: \"Finish\"\n        },\n        undo: {\n          title: \"Delete last point drawn\",\n          text: \"Delete last point\"\n        },\n        buttons: {\n          polyline: \"Draw a polyline\",\n          polygon: \"Draw a polygon\",\n          rectangle: \"Draw a rectangle\",\n          circle: \"Draw a circle\",\n          marker: \"Draw a marker\",\n          circlemarker: \"Draw a circlemarker\"\n        }\n      },\n      handlers: {\n        circle: {\n          tooltip: {\n            start: \"Click and drag to draw circle.\"\n          },\n          radius: \"Radius\"\n        },\n        circlemarker: {\n          tooltip: {\n            start: \"Click map to place circle marker.\"\n          }\n        },\n        marker: {\n          tooltip: {\n            start: \"Click map to place marker.\"\n          }\n        },\n        polygon: {\n          tooltip: {\n            start: \"Click to start drawing shape.\",\n            cont: \"Click to continue drawing shape.\",\n            end: \"Click first point to close this shape.\"\n          }\n        },\n        polyline: {\n          error: \"<strong>Error:</strong> shape edges cannot cross!\",\n          tooltip: {\n            start: \"Click to start drawing line.\",\n            cont: \"Click to continue drawing line.\",\n            end: \"Click last point to finish line.\"\n          }\n        },\n        rectangle: {\n          tooltip: {\n            start: \"Click and drag to draw rectangle.\"\n          }\n        },\n        simpleshape: {\n          tooltip: {\n            end: \"Release mouse to finish drawing.\"\n          }\n        }\n      }\n    },\n    edit: {\n      toolbar: {\n        actions: {\n          save: {\n            title: \"Save changes\",\n            text: \"Save\"\n          },\n          cancel: {\n            title: \"Cancel editing, discards all changes\",\n            text: \"Cancel\"\n          },\n          clearAll: {\n            title: \"Clear all layers\",\n            text: \"Clear All\"\n          }\n        },\n        buttons: {\n          edit: \"Edit layers\",\n          editDisabled: \"No layers to edit\",\n          remove: \"Delete layers\",\n          removeDisabled: \"No layers to delete\"\n        }\n      },\n      handlers: {\n        edit: {\n          tooltip: {\n            text: \"Drag handles or markers to edit features.\",\n            subtext: \"Click cancel to undo changes.\"\n          }\n        },\n        remove: {\n          tooltip: {\n            text: \"Click on a feature to remove.\"\n          }\n        }\n      }\n    }\n  }, L.Draw.Event = {}, L.Draw.Event.CREATED = \"draw:created\", L.Draw.Event.EDITED = \"draw:edited\", L.Draw.Event.DELETED = \"draw:deleted\", L.Draw.Event.DRAWSTART = \"draw:drawstart\", L.Draw.Event.DRAWSTOP = \"draw:drawstop\", L.Draw.Event.DRAWVERTEX = \"draw:drawvertex\", L.Draw.Event.EDITSTART = \"draw:editstart\", L.Draw.Event.EDITMOVE = \"draw:editmove\", L.Draw.Event.EDITRESIZE = \"draw:editresize\", L.Draw.Event.EDITVERTEX = \"draw:editvertex\", L.Draw.Event.EDITSTOP = \"draw:editstop\", L.Draw.Event.DELETESTART = \"draw:deletestart\", L.Draw.Event.DELETESTOP = \"draw:deletestop\", L.Draw.Event.TOOLBAROPENED = \"draw:toolbaropened\", L.Draw.Event.TOOLBARCLOSED = \"draw:toolbarclosed\", L.Draw.Event.MARKERCONTEXT = \"draw:markercontext\", L.Draw = L.Draw || {}, L.Draw.Feature = L.Handler.extend({\n    initialize: function (t, e) {\n      this._map = t, this._container = t._container, this._overlayPane = t._panes.overlayPane, this._popupPane = t._panes.popupPane, e && e.shapeOptions && (e.shapeOptions = L.Util.extend({}, this.options.shapeOptions, e.shapeOptions)), L.setOptions(this, e);\n      var i = L.version.split(\".\");\n      1 === parseInt(i[0], 10) && parseInt(i[1], 10) >= 2 ? L.Draw.Feature.include(L.Evented.prototype) : L.Draw.Feature.include(L.Mixin.Events);\n    },\n    enable: function () {\n      this._enabled || (L.Handler.prototype.enable.call(this), this.fire(\"enabled\", {\n        handler: this.type\n      }), this._map.fire(L.Draw.Event.DRAWSTART, {\n        layerType: this.type\n      }));\n    },\n    disable: function () {\n      this._enabled && (L.Handler.prototype.disable.call(this), this._map.fire(L.Draw.Event.DRAWSTOP, {\n        layerType: this.type\n      }), this.fire(\"disabled\", {\n        handler: this.type\n      }));\n    },\n    addHooks: function () {\n      var t = this._map;\n      t && (L.DomUtil.disableTextSelection(), t.getContainer().focus(), this._tooltip = new L.Draw.Tooltip(this._map), L.DomEvent.on(this._container, \"keyup\", this._cancelDrawing, this));\n    },\n    removeHooks: function () {\n      this._map && (L.DomUtil.enableTextSelection(), this._tooltip.dispose(), this._tooltip = null, L.DomEvent.off(this._container, \"keyup\", this._cancelDrawing, this));\n    },\n    setOptions: function (t) {\n      L.setOptions(this, t);\n    },\n    _fireCreatedEvent: function (t) {\n      this._map.fire(L.Draw.Event.CREATED, {\n        layer: t,\n        layerType: this.type\n      });\n    },\n    _cancelDrawing: function (t) {\n      27 === t.keyCode && (this._map.fire(\"draw:canceled\", {\n        layerType: this.type\n      }), this.disable());\n    }\n  }), L.Draw.Polyline = L.Draw.Feature.extend({\n    statics: {\n      TYPE: \"polyline\"\n    },\n    Poly: L.Polyline,\n    options: {\n      allowIntersection: !0,\n      repeatMode: !1,\n      drawError: {\n        color: \"#b00b00\",\n        timeout: 2500\n      },\n      icon: new L.DivIcon({\n        iconSize: new L.Point(8, 8),\n        className: \"leaflet-div-icon leaflet-editing-icon\"\n      }),\n      touchIcon: new L.DivIcon({\n        iconSize: new L.Point(20, 20),\n        className: \"leaflet-div-icon leaflet-editing-icon leaflet-touch-icon\"\n      }),\n      guidelineDistance: 20,\n      maxGuideLineLength: 4e3,\n      shapeOptions: {\n        stroke: !0,\n        color: \"#3388ff\",\n        weight: 4,\n        opacity: .5,\n        fill: !1,\n        clickable: !0\n      },\n      metric: !0,\n      feet: !0,\n      nautic: !1,\n      showLength: !0,\n      zIndexOffset: 2e3,\n      factor: 1,\n      maxPoints: 0\n    },\n    initialize: function (t, e) {\n      L.Browser.touch && (this.options.icon = this.options.touchIcon), this.options.drawError.message = L.drawLocal.draw.handlers.polyline.error, e && e.drawError && (e.drawError = L.Util.extend({}, this.options.drawError, e.drawError)), this.type = L.Draw.Polyline.TYPE, L.Draw.Feature.prototype.initialize.call(this, t, e);\n    },\n    addHooks: function () {\n      L.Draw.Feature.prototype.addHooks.call(this), this._map && (this._markers = [], this._markerGroup = new L.LayerGroup(), this._map.addLayer(this._markerGroup), this._poly = new L.Polyline([], this.options.shapeOptions), this._tooltip.updateContent(this._getTooltipText()), this._mouseMarker || (this._mouseMarker = L.marker(this._map.getCenter(), {\n        icon: L.divIcon({\n          className: \"leaflet-mouse-marker\",\n          iconAnchor: [20, 20],\n          iconSize: [40, 40]\n        }),\n        opacity: 0,\n        zIndexOffset: this.options.zIndexOffset\n      })), this._mouseMarker.on(\"mouseout\", this._onMouseOut, this).on(\"mousemove\", this._onMouseMove, this).on(\"mousedown\", this._onMouseDown, this).on(\"mouseup\", this._onMouseUp, this).addTo(this._map), this._map.on(\"mouseup\", this._onMouseUp, this).on(\"mousemove\", this._onMouseMove, this).on(\"zoomlevelschange\", this._onZoomEnd, this).on(\"touchstart\", this._onTouch, this).on(\"zoomend\", this._onZoomEnd, this));\n    },\n    removeHooks: function () {\n      L.Draw.Feature.prototype.removeHooks.call(this), this._clearHideErrorTimeout(), this._cleanUpShape(), this._map.removeLayer(this._markerGroup), delete this._markerGroup, delete this._markers, this._map.removeLayer(this._poly), delete this._poly, this._mouseMarker.off(\"mousedown\", this._onMouseDown, this).off(\"mouseout\", this._onMouseOut, this).off(\"mouseup\", this._onMouseUp, this).off(\"mousemove\", this._onMouseMove, this), this._map.removeLayer(this._mouseMarker), delete this._mouseMarker, this._clearGuides(), this._map.off(\"mouseup\", this._onMouseUp, this).off(\"mousemove\", this._onMouseMove, this).off(\"zoomlevelschange\", this._onZoomEnd, this).off(\"zoomend\", this._onZoomEnd, this).off(\"touchstart\", this._onTouch, this).off(\"click\", this._onTouch, this);\n    },\n    deleteLastVertex: function () {\n      if (!(this._markers.length <= 1)) {\n        var t = this._markers.pop(),\n          e = this._poly,\n          i = e.getLatLngs(),\n          o = i.splice(-1, 1)[0];\n        this._poly.setLatLngs(i), this._markerGroup.removeLayer(t), e.getLatLngs().length < 2 && this._map.removeLayer(e), this._vertexChanged(o, !1);\n      }\n    },\n    addVertex: function (t) {\n      if (this._markers.length >= 2 && !this.options.allowIntersection && this._poly.newLatLngIntersects(t)) return void this._showErrorTooltip();\n      this._errorShown && this._hideErrorTooltip(), this._markers.push(this._createMarker(t)), this._poly.addLatLng(t), 2 === this._poly.getLatLngs().length && this._map.addLayer(this._poly), this._vertexChanged(t, !0);\n    },\n    completeShape: function () {\n      this._markers.length <= 1 || (this._fireCreatedEvent(), this.disable(), this.options.repeatMode && this.enable());\n    },\n    _finishShape: function () {\n      var t = this._poly._defaultShape ? this._poly._defaultShape() : this._poly.getLatLngs(),\n        e = this._poly.newLatLngIntersects(t[t.length - 1]);\n      if (!this.options.allowIntersection && e || !this._shapeIsValid()) return void this._showErrorTooltip();\n      this._fireCreatedEvent(), this.disable(), this.options.repeatMode && this.enable();\n    },\n    _shapeIsValid: function () {\n      return !0;\n    },\n    _onZoomEnd: function () {\n      null !== this._markers && this._updateGuide();\n    },\n    _onMouseMove: function (t) {\n      var e = this._map.mouseEventToLayerPoint(t.originalEvent),\n        i = this._map.layerPointToLatLng(e);\n      this._currentLatLng = i, this._updateTooltip(i), this._updateGuide(e), this._mouseMarker.setLatLng(i), L.DomEvent.preventDefault(t.originalEvent);\n    },\n    _vertexChanged: function (t, e) {\n      this._map.fire(L.Draw.Event.DRAWVERTEX, {\n        layers: this._markerGroup\n      }), this._updateFinishHandler(), this._updateRunningMeasure(t, e), this._clearGuides(), this._updateTooltip();\n    },\n    _onMouseDown: function (t) {\n      if (!this._clickHandled && !this._touchHandled && !this._disableMarkers) {\n        this._onMouseMove(t), this._clickHandled = !0, this._disableNewMarkers();\n        var e = t.originalEvent,\n          i = e.clientX,\n          o = e.clientY;\n        this._startPoint.call(this, i, o);\n      }\n    },\n    _startPoint: function (t, e) {\n      this._mouseDownOrigin = L.point(t, e);\n    },\n    _onMouseUp: function (t) {\n      var e = t.originalEvent,\n        i = e.clientX,\n        o = e.clientY;\n      this._endPoint.call(this, i, o, t), this._clickHandled = null;\n    },\n    _endPoint: function (e, i, o) {\n      if (this._mouseDownOrigin) {\n        var a = L.point(e, i).distanceTo(this._mouseDownOrigin),\n          n = this._calculateFinishDistance(o.latlng);\n        this.options.maxPoints > 1 && this.options.maxPoints == this._markers.length + 1 ? (this.addVertex(o.latlng), this._finishShape()) : n < 10 && L.Browser.touch ? this._finishShape() : Math.abs(a) < 9 * (t.devicePixelRatio || 1) && this.addVertex(o.latlng), this._enableNewMarkers();\n      }\n      this._mouseDownOrigin = null;\n    },\n    _onTouch: function (t) {\n      var e,\n        i,\n        o = t.originalEvent;\n      !o.touches || !o.touches[0] || this._clickHandled || this._touchHandled || this._disableMarkers || (e = o.touches[0].clientX, i = o.touches[0].clientY, this._disableNewMarkers(), this._touchHandled = !0, this._startPoint.call(this, e, i), this._endPoint.call(this, e, i, t), this._touchHandled = null), this._clickHandled = null;\n    },\n    _onMouseOut: function () {\n      this._tooltip && this._tooltip._onMouseOut.call(this._tooltip);\n    },\n    _calculateFinishDistance: function (t) {\n      var e;\n      if (this._markers.length > 0) {\n        var i;\n        if (this.type === L.Draw.Polyline.TYPE) i = this._markers[this._markers.length - 1];else {\n          if (this.type !== L.Draw.Polygon.TYPE) return 1 / 0;\n          i = this._markers[0];\n        }\n        var o = this._map.latLngToContainerPoint(i.getLatLng()),\n          a = new L.Marker(t, {\n            icon: this.options.icon,\n            zIndexOffset: 2 * this.options.zIndexOffset\n          }),\n          n = this._map.latLngToContainerPoint(a.getLatLng());\n        e = o.distanceTo(n);\n      } else e = 1 / 0;\n      return e;\n    },\n    _updateFinishHandler: function () {\n      var t = this._markers.length;\n      t > 1 && this._markers[t - 1].on(\"click\", this._finishShape, this), t > 2 && this._markers[t - 2].off(\"click\", this._finishShape, this);\n    },\n    _createMarker: function (t) {\n      var e = new L.Marker(t, {\n        icon: this.options.icon,\n        zIndexOffset: 2 * this.options.zIndexOffset\n      });\n      return this._markerGroup.addLayer(e), e;\n    },\n    _updateGuide: function (t) {\n      var e = this._markers ? this._markers.length : 0;\n      e > 0 && (t = t || this._map.latLngToLayerPoint(this._currentLatLng), this._clearGuides(), this._drawGuide(this._map.latLngToLayerPoint(this._markers[e - 1].getLatLng()), t));\n    },\n    _updateTooltip: function (t) {\n      var e = this._getTooltipText();\n      t && this._tooltip.updatePosition(t), this._errorShown || this._tooltip.updateContent(e);\n    },\n    _drawGuide: function (t, e) {\n      var i,\n        o,\n        a,\n        n = Math.floor(Math.sqrt(Math.pow(e.x - t.x, 2) + Math.pow(e.y - t.y, 2))),\n        s = this.options.guidelineDistance,\n        r = this.options.maxGuideLineLength,\n        l = n > r ? n - r : s;\n      for (this._guidesContainer || (this._guidesContainer = L.DomUtil.create(\"div\", \"leaflet-draw-guides\", this._overlayPane)); l < n; l += this.options.guidelineDistance) i = l / n, o = {\n        x: Math.floor(t.x * (1 - i) + i * e.x),\n        y: Math.floor(t.y * (1 - i) + i * e.y)\n      }, a = L.DomUtil.create(\"div\", \"leaflet-draw-guide-dash\", this._guidesContainer), a.style.backgroundColor = this._errorShown ? this.options.drawError.color : this.options.shapeOptions.color, L.DomUtil.setPosition(a, o);\n    },\n    _updateGuideColor: function (t) {\n      if (this._guidesContainer) for (var e = 0, i = this._guidesContainer.childNodes.length; e < i; e++) this._guidesContainer.childNodes[e].style.backgroundColor = t;\n    },\n    _clearGuides: function () {\n      if (this._guidesContainer) for (; this._guidesContainer.firstChild;) this._guidesContainer.removeChild(this._guidesContainer.firstChild);\n    },\n    _getTooltipText: function () {\n      var t,\n        e,\n        i = this.options.showLength;\n      return 0 === this._markers.length ? t = {\n        text: L.drawLocal.draw.handlers.polyline.tooltip.start\n      } : (e = i ? this._getMeasurementString() : \"\", t = 1 === this._markers.length ? {\n        text: L.drawLocal.draw.handlers.polyline.tooltip.cont,\n        subtext: e\n      } : {\n        text: L.drawLocal.draw.handlers.polyline.tooltip.end,\n        subtext: e\n      }), t;\n    },\n    _updateRunningMeasure: function (t, e) {\n      var i,\n        o,\n        a = this._markers.length;\n      1 === this._markers.length ? this._measurementRunningTotal = 0 : (i = a - (e ? 2 : 1), o = L.GeometryUtil.isVersion07x() ? t.distanceTo(this._markers[i].getLatLng()) * (this.options.factor || 1) : this._map.distance(t, this._markers[i].getLatLng()) * (this.options.factor || 1), this._measurementRunningTotal += o * (e ? 1 : -1));\n    },\n    _getMeasurementString: function () {\n      var t,\n        e = this._currentLatLng,\n        i = this._markers[this._markers.length - 1].getLatLng();\n      return t = L.GeometryUtil.isVersion07x() ? i && e && e.distanceTo ? this._measurementRunningTotal + e.distanceTo(i) * (this.options.factor || 1) : this._measurementRunningTotal || 0 : i && e ? this._measurementRunningTotal + this._map.distance(e, i) * (this.options.factor || 1) : this._measurementRunningTotal || 0, L.GeometryUtil.readableDistance(t, this.options.metric, this.options.feet, this.options.nautic, this.options.precision);\n    },\n    _showErrorTooltip: function () {\n      this._errorShown = !0, this._tooltip.showAsError().updateContent({\n        text: this.options.drawError.message\n      }), this._updateGuideColor(this.options.drawError.color), this._poly.setStyle({\n        color: this.options.drawError.color\n      }), this._clearHideErrorTimeout(), this._hideErrorTimeout = setTimeout(L.Util.bind(this._hideErrorTooltip, this), this.options.drawError.timeout);\n    },\n    _hideErrorTooltip: function () {\n      this._errorShown = !1, this._clearHideErrorTimeout(), this._tooltip.removeError().updateContent(this._getTooltipText()), this._updateGuideColor(this.options.shapeOptions.color), this._poly.setStyle({\n        color: this.options.shapeOptions.color\n      });\n    },\n    _clearHideErrorTimeout: function () {\n      this._hideErrorTimeout && (clearTimeout(this._hideErrorTimeout), this._hideErrorTimeout = null);\n    },\n    _disableNewMarkers: function () {\n      this._disableMarkers = !0;\n    },\n    _enableNewMarkers: function () {\n      setTimeout(function () {\n        this._disableMarkers = !1;\n      }.bind(this), 50);\n    },\n    _cleanUpShape: function () {\n      this._markers.length > 1 && this._markers[this._markers.length - 1].off(\"click\", this._finishShape, this);\n    },\n    _fireCreatedEvent: function () {\n      var t = new this.Poly(this._poly.getLatLngs(), this.options.shapeOptions);\n      L.Draw.Feature.prototype._fireCreatedEvent.call(this, t);\n    }\n  }), L.Draw.Polygon = L.Draw.Polyline.extend({\n    statics: {\n      TYPE: \"polygon\"\n    },\n    Poly: L.Polygon,\n    options: {\n      showArea: !1,\n      showLength: !1,\n      shapeOptions: {\n        stroke: !0,\n        color: \"#3388ff\",\n        weight: 4,\n        opacity: .5,\n        fill: !0,\n        fillColor: null,\n        fillOpacity: .2,\n        clickable: !0\n      },\n      metric: !0,\n      feet: !0,\n      nautic: !1,\n      precision: {}\n    },\n    initialize: function (t, e) {\n      L.Draw.Polyline.prototype.initialize.call(this, t, e), this.type = L.Draw.Polygon.TYPE;\n    },\n    _updateFinishHandler: function () {\n      var t = this._markers.length;\n      1 === t && this._markers[0].on(\"click\", this._finishShape, this), t > 2 && (this._markers[t - 1].on(\"dblclick\", this._finishShape, this), t > 3 && this._markers[t - 2].off(\"dblclick\", this._finishShape, this));\n    },\n    _getTooltipText: function () {\n      var t, e;\n      return 0 === this._markers.length ? t = L.drawLocal.draw.handlers.polygon.tooltip.start : this._markers.length < 3 ? (t = L.drawLocal.draw.handlers.polygon.tooltip.cont, e = this._getMeasurementString()) : (t = L.drawLocal.draw.handlers.polygon.tooltip.end, e = this._getMeasurementString()), {\n        text: t,\n        subtext: e\n      };\n    },\n    _getMeasurementString: function () {\n      var t = this._area,\n        e = \"\";\n      return t || this.options.showLength ? (this.options.showLength && (e = L.Draw.Polyline.prototype._getMeasurementString.call(this)), t && (e += \"<br>\" + L.GeometryUtil.readableArea(t, this.options.metric, this.options.precision)), e) : null;\n    },\n    _shapeIsValid: function () {\n      return this._markers.length >= 3;\n    },\n    _vertexChanged: function (t, e) {\n      var i;\n      !this.options.allowIntersection && this.options.showArea && (i = this._poly.getLatLngs(), this._area = L.GeometryUtil.geodesicArea(i)), L.Draw.Polyline.prototype._vertexChanged.call(this, t, e);\n    },\n    _cleanUpShape: function () {\n      var t = this._markers.length;\n      t > 0 && (this._markers[0].off(\"click\", this._finishShape, this), t > 2 && this._markers[t - 1].off(\"dblclick\", this._finishShape, this));\n    }\n  }), L.SimpleShape = {}, L.Draw.SimpleShape = L.Draw.Feature.extend({\n    options: {\n      repeatMode: !1\n    },\n    initialize: function (t, e) {\n      this._endLabelText = L.drawLocal.draw.handlers.simpleshape.tooltip.end, L.Draw.Feature.prototype.initialize.call(this, t, e);\n    },\n    addHooks: function () {\n      L.Draw.Feature.prototype.addHooks.call(this), this._map && (this._mapDraggable = this._map.dragging.enabled(), this._mapDraggable && this._map.dragging.disable(), this._container.style.cursor = \"crosshair\", this._tooltip.updateContent({\n        text: this._initialLabelText\n      }), this._map.on(\"mousedown\", this._onMouseDown, this).on(\"mousemove\", this._onMouseMove, this).on(\"touchstart\", this._onMouseDown, this).on(\"touchmove\", this._onMouseMove, this), e.addEventListener(\"touchstart\", L.DomEvent.preventDefault, {\n        passive: !1\n      }));\n    },\n    removeHooks: function () {\n      L.Draw.Feature.prototype.removeHooks.call(this), this._map && (this._mapDraggable && this._map.dragging.enable(), this._container.style.cursor = \"\", this._map.off(\"mousedown\", this._onMouseDown, this).off(\"mousemove\", this._onMouseMove, this).off(\"touchstart\", this._onMouseDown, this).off(\"touchmove\", this._onMouseMove, this), L.DomEvent.off(e, \"mouseup\", this._onMouseUp, this), L.DomEvent.off(e, \"touchend\", this._onMouseUp, this), e.removeEventListener(\"touchstart\", L.DomEvent.preventDefault), this._shape && (this._map.removeLayer(this._shape), delete this._shape)), this._isDrawing = !1;\n    },\n    _getTooltipText: function () {\n      return {\n        text: this._endLabelText\n      };\n    },\n    _onMouseDown: function (t) {\n      this._isDrawing = !0, this._startLatLng = t.latlng, L.DomEvent.on(e, \"mouseup\", this._onMouseUp, this).on(e, \"touchend\", this._onMouseUp, this).preventDefault(t.originalEvent);\n    },\n    _onMouseMove: function (t) {\n      var e = t.latlng;\n      this._tooltip.updatePosition(e), this._isDrawing && (this._tooltip.updateContent(this._getTooltipText()), this._drawShape(e));\n    },\n    _onMouseUp: function () {\n      this._shape && this._fireCreatedEvent(), this.disable(), this.options.repeatMode && this.enable();\n    }\n  }), L.Draw.Rectangle = L.Draw.SimpleShape.extend({\n    statics: {\n      TYPE: \"rectangle\"\n    },\n    options: {\n      shapeOptions: {\n        stroke: !0,\n        color: \"#3388ff\",\n        weight: 4,\n        opacity: .5,\n        fill: !0,\n        fillColor: null,\n        fillOpacity: .2,\n        showArea: !0,\n        clickable: !0\n      },\n      metric: !0\n    },\n    initialize: function (t, e) {\n      this.type = L.Draw.Rectangle.TYPE, this._initialLabelText = L.drawLocal.draw.handlers.rectangle.tooltip.start, L.Draw.SimpleShape.prototype.initialize.call(this, t, e);\n    },\n    disable: function () {\n      this._enabled && (this._isCurrentlyTwoClickDrawing = !1, L.Draw.SimpleShape.prototype.disable.call(this));\n    },\n    _onMouseUp: function (t) {\n      if (!this._shape && !this._isCurrentlyTwoClickDrawing) return void (this._isCurrentlyTwoClickDrawing = !0);\n      this._isCurrentlyTwoClickDrawing && !o(t.target, \"leaflet-pane\") || L.Draw.SimpleShape.prototype._onMouseUp.call(this);\n    },\n    _drawShape: function (t) {\n      this._shape ? this._shape.setBounds(new L.LatLngBounds(this._startLatLng, t)) : (this._shape = new L.Rectangle(new L.LatLngBounds(this._startLatLng, t), this.options.shapeOptions), this._map.addLayer(this._shape));\n    },\n    _fireCreatedEvent: function () {\n      var t = new L.Rectangle(this._shape.getBounds(), this.options.shapeOptions);\n      L.Draw.SimpleShape.prototype._fireCreatedEvent.call(this, t);\n    },\n    _getTooltipText: function () {\n      var t,\n        e,\n        i,\n        o = L.Draw.SimpleShape.prototype._getTooltipText.call(this),\n        a = this._shape,\n        n = this.options.showArea;\n      return a && (t = this._shape._defaultShape ? this._shape._defaultShape() : this._shape.getLatLngs(), e = L.GeometryUtil.geodesicArea(t), i = n ? L.GeometryUtil.readableArea(e, this.options.metric) : \"\"), {\n        text: o.text,\n        subtext: i\n      };\n    }\n  }), L.Draw.Marker = L.Draw.Feature.extend({\n    statics: {\n      TYPE: \"marker\"\n    },\n    options: {\n      icon: new L.Icon.Default(),\n      repeatMode: !1,\n      zIndexOffset: 2e3\n    },\n    initialize: function (t, e) {\n      this.type = L.Draw.Marker.TYPE, this._initialLabelText = L.drawLocal.draw.handlers.marker.tooltip.start, L.Draw.Feature.prototype.initialize.call(this, t, e);\n    },\n    addHooks: function () {\n      L.Draw.Feature.prototype.addHooks.call(this), this._map && (this._tooltip.updateContent({\n        text: this._initialLabelText\n      }), this._mouseMarker || (this._mouseMarker = L.marker(this._map.getCenter(), {\n        icon: L.divIcon({\n          className: \"leaflet-mouse-marker\",\n          iconAnchor: [20, 20],\n          iconSize: [40, 40]\n        }),\n        opacity: 0,\n        zIndexOffset: this.options.zIndexOffset\n      })), this._mouseMarker.on(\"click\", this._onClick, this).addTo(this._map), this._map.on(\"mousemove\", this._onMouseMove, this), this._map.on(\"click\", this._onTouch, this));\n    },\n    removeHooks: function () {\n      L.Draw.Feature.prototype.removeHooks.call(this), this._map && (this._map.off(\"click\", this._onClick, this).off(\"click\", this._onTouch, this), this._marker && (this._marker.off(\"click\", this._onClick, this), this._map.removeLayer(this._marker), delete this._marker), this._mouseMarker.off(\"click\", this._onClick, this), this._map.removeLayer(this._mouseMarker), delete this._mouseMarker, this._map.off(\"mousemove\", this._onMouseMove, this));\n    },\n    _onMouseMove: function (t) {\n      var e = t.latlng;\n      this._tooltip.updatePosition(e), this._mouseMarker.setLatLng(e), this._marker ? (e = this._mouseMarker.getLatLng(), this._marker.setLatLng(e)) : (this._marker = this._createMarker(e), this._marker.on(\"click\", this._onClick, this), this._map.on(\"click\", this._onClick, this).addLayer(this._marker));\n    },\n    _createMarker: function (t) {\n      return new L.Marker(t, {\n        icon: this.options.icon,\n        zIndexOffset: this.options.zIndexOffset\n      });\n    },\n    _onClick: function () {\n      this._fireCreatedEvent(), this.disable(), this.options.repeatMode && this.enable();\n    },\n    _onTouch: function (t) {\n      this._onMouseMove(t), this._onClick();\n    },\n    _fireCreatedEvent: function () {\n      var t = new L.Marker.Touch(this._marker.getLatLng(), {\n        icon: this.options.icon\n      });\n      L.Draw.Feature.prototype._fireCreatedEvent.call(this, t);\n    }\n  }), L.Draw.CircleMarker = L.Draw.Marker.extend({\n    statics: {\n      TYPE: \"circlemarker\"\n    },\n    options: {\n      stroke: !0,\n      color: \"#3388ff\",\n      weight: 4,\n      opacity: .5,\n      fill: !0,\n      fillColor: null,\n      fillOpacity: .2,\n      clickable: !0,\n      zIndexOffset: 2e3\n    },\n    initialize: function (t, e) {\n      this.type = L.Draw.CircleMarker.TYPE, this._initialLabelText = L.drawLocal.draw.handlers.circlemarker.tooltip.start, L.Draw.Feature.prototype.initialize.call(this, t, e);\n    },\n    _fireCreatedEvent: function () {\n      var t = new L.CircleMarker(this._marker.getLatLng(), this.options);\n      L.Draw.Feature.prototype._fireCreatedEvent.call(this, t);\n    },\n    _createMarker: function (t) {\n      return new L.CircleMarker(t, this.options);\n    }\n  }), L.Draw.Circle = L.Draw.SimpleShape.extend({\n    statics: {\n      TYPE: \"circle\"\n    },\n    options: {\n      shapeOptions: {\n        stroke: !0,\n        color: \"#3388ff\",\n        weight: 4,\n        opacity: .5,\n        fill: !0,\n        fillColor: null,\n        fillOpacity: .2,\n        clickable: !0\n      },\n      showRadius: !0,\n      metric: !0,\n      feet: !0,\n      nautic: !1\n    },\n    initialize: function (t, e) {\n      this.type = L.Draw.Circle.TYPE, this._initialLabelText = L.drawLocal.draw.handlers.circle.tooltip.start, L.Draw.SimpleShape.prototype.initialize.call(this, t, e);\n    },\n    _drawShape: function (t) {\n      if (L.GeometryUtil.isVersion07x()) var e = this._startLatLng.distanceTo(t);else var e = this._map.distance(this._startLatLng, t);\n      this._shape ? this._shape.setRadius(e) : (this._shape = new L.Circle(this._startLatLng, e, this.options.shapeOptions), this._map.addLayer(this._shape));\n    },\n    _fireCreatedEvent: function () {\n      var t = new L.Circle(this._startLatLng, this._shape.getRadius(), this.options.shapeOptions);\n      L.Draw.SimpleShape.prototype._fireCreatedEvent.call(this, t);\n    },\n    _onMouseMove: function (t) {\n      var e,\n        i = t.latlng,\n        o = this.options.showRadius,\n        a = this.options.metric;\n      if (this._tooltip.updatePosition(i), this._isDrawing) {\n        this._drawShape(i), e = this._shape.getRadius().toFixed(1);\n        var n = \"\";\n        o && (n = L.drawLocal.draw.handlers.circle.radius + \": \" + L.GeometryUtil.readableDistance(e, a, this.options.feet, this.options.nautic)), this._tooltip.updateContent({\n          text: this._endLabelText,\n          subtext: n\n        });\n      }\n    }\n  }), L.Edit = L.Edit || {}, L.Edit.Marker = L.Handler.extend({\n    initialize: function (t, e) {\n      this._marker = t, L.setOptions(this, e);\n    },\n    addHooks: function () {\n      var t = this._marker;\n      t.dragging.enable(), t.on(\"dragend\", this._onDragEnd, t), this._toggleMarkerHighlight();\n    },\n    removeHooks: function () {\n      var t = this._marker;\n      t.dragging.disable(), t.off(\"dragend\", this._onDragEnd, t), this._toggleMarkerHighlight();\n    },\n    _onDragEnd: function (t) {\n      var e = t.target;\n      e.edited = !0, this._map.fire(L.Draw.Event.EDITMOVE, {\n        layer: e\n      });\n    },\n    _toggleMarkerHighlight: function () {\n      var t = this._marker._icon;\n      t && (t.style.display = \"none\", L.DomUtil.hasClass(t, \"leaflet-edit-marker-selected\") ? (L.DomUtil.removeClass(t, \"leaflet-edit-marker-selected\"), this._offsetMarker(t, -4)) : (L.DomUtil.addClass(t, \"leaflet-edit-marker-selected\"), this._offsetMarker(t, 4)), t.style.display = \"\");\n    },\n    _offsetMarker: function (t, e) {\n      var i = parseInt(t.style.marginTop, 10) - e,\n        o = parseInt(t.style.marginLeft, 10) - e;\n      t.style.marginTop = i + \"px\", t.style.marginLeft = o + \"px\";\n    }\n  }), L.Marker.addInitHook(function () {\n    L.Edit.Marker && (this.editing = new L.Edit.Marker(this), this.options.editable && this.editing.enable());\n  }), L.Edit = L.Edit || {}, L.Edit.Poly = L.Handler.extend({\n    initialize: function (t) {\n      this.latlngs = [t._latlngs], t._holes && (this.latlngs = this.latlngs.concat(t._holes)), this._poly = t, this._poly.on(\"revert-edited\", this._updateLatLngs, this);\n    },\n    _defaultShape: function () {\n      return L.Polyline._flat ? L.Polyline._flat(this._poly._latlngs) ? this._poly._latlngs : this._poly._latlngs[0] : this._poly._latlngs;\n    },\n    _eachVertexHandler: function (t) {\n      for (var e = 0; e < this._verticesHandlers.length; e++) t(this._verticesHandlers[e]);\n    },\n    addHooks: function () {\n      this._initHandlers(), this._eachVertexHandler(function (t) {\n        t.addHooks();\n      });\n    },\n    removeHooks: function () {\n      this._eachVertexHandler(function (t) {\n        t.removeHooks();\n      });\n    },\n    updateMarkers: function () {\n      this._eachVertexHandler(function (t) {\n        t.updateMarkers();\n      });\n    },\n    _initHandlers: function () {\n      this._verticesHandlers = [];\n      for (var t = 0; t < this.latlngs.length; t++) this._verticesHandlers.push(new L.Edit.PolyVerticesEdit(this._poly, this.latlngs[t], this._poly.options.poly));\n    },\n    _updateLatLngs: function (t) {\n      this.latlngs = [t.layer._latlngs], t.layer._holes && (this.latlngs = this.latlngs.concat(t.layer._holes));\n    }\n  }), L.Edit.PolyVerticesEdit = L.Handler.extend({\n    options: {\n      icon: new L.DivIcon({\n        iconSize: new L.Point(8, 8),\n        className: \"leaflet-div-icon leaflet-editing-icon\"\n      }),\n      touchIcon: new L.DivIcon({\n        iconSize: new L.Point(20, 20),\n        className: \"leaflet-div-icon leaflet-editing-icon leaflet-touch-icon\"\n      }),\n      drawError: {\n        color: \"#b00b00\",\n        timeout: 1e3\n      }\n    },\n    initialize: function (t, e, i) {\n      L.Browser.touch && (this.options.icon = this.options.touchIcon), this._poly = t, i && i.drawError && (i.drawError = L.Util.extend({}, this.options.drawError, i.drawError)), this._latlngs = e, L.setOptions(this, i);\n    },\n    _defaultShape: function () {\n      return L.Polyline._flat ? L.Polyline._flat(this._latlngs) ? this._latlngs : this._latlngs[0] : this._latlngs;\n    },\n    addHooks: function () {\n      var t = this._poly,\n        e = t._path;\n      t instanceof L.Polygon || (t.options.fill = !1, t.options.editing && (t.options.editing.fill = !1)), e && t.options.editing.className && (t.options.original.className && t.options.original.className.split(\" \").forEach(function (t) {\n        L.DomUtil.removeClass(e, t);\n      }), t.options.editing.className.split(\" \").forEach(function (t) {\n        L.DomUtil.addClass(e, t);\n      })), t.setStyle(t.options.editing), this._poly._map && (this._map = this._poly._map, this._markerGroup || this._initMarkers(), this._poly._map.addLayer(this._markerGroup));\n    },\n    removeHooks: function () {\n      var t = this._poly,\n        e = t._path;\n      e && t.options.editing.className && (t.options.editing.className.split(\" \").forEach(function (t) {\n        L.DomUtil.removeClass(e, t);\n      }), t.options.original.className && t.options.original.className.split(\" \").forEach(function (t) {\n        L.DomUtil.addClass(e, t);\n      })), t.setStyle(t.options.original), t._map && (t._map.removeLayer(this._markerGroup), delete this._markerGroup, delete this._markers);\n    },\n    updateMarkers: function () {\n      this._markerGroup.clearLayers(), this._initMarkers();\n    },\n    _initMarkers: function () {\n      this._markerGroup || (this._markerGroup = new L.LayerGroup()), this._markers = [];\n      var t,\n        e,\n        i,\n        o,\n        a = this._defaultShape();\n      for (t = 0, i = a.length; t < i; t++) o = this._createMarker(a[t], t), o.on(\"click\", this._onMarkerClick, this), o.on(\"contextmenu\", this._onContextMenu, this), this._markers.push(o);\n      var n, s;\n      for (t = 0, e = i - 1; t < i; e = t++) (0 !== t || L.Polygon && this._poly instanceof L.Polygon) && (n = this._markers[e], s = this._markers[t], this._createMiddleMarker(n, s), this._updatePrevNext(n, s));\n    },\n    _createMarker: function (t, e) {\n      var i = new L.Marker.Touch(t, {\n        draggable: !0,\n        icon: this.options.icon\n      });\n      return i._origLatLng = t, i._index = e, i.on(\"dragstart\", this._onMarkerDragStart, this).on(\"drag\", this._onMarkerDrag, this).on(\"dragend\", this._fireEdit, this).on(\"touchmove\", this._onTouchMove, this).on(\"touchend\", this._fireEdit, this).on(\"MSPointerMove\", this._onTouchMove, this).on(\"MSPointerUp\", this._fireEdit, this), this._markerGroup.addLayer(i), i;\n    },\n    _onMarkerDragStart: function () {\n      this._poly.fire(\"editstart\");\n    },\n    _spliceLatLngs: function () {\n      var t = this._defaultShape(),\n        e = [].splice.apply(t, arguments);\n      return this._poly._convertLatLngs(t, !0), this._poly.redraw(), e;\n    },\n    _removeMarker: function (t) {\n      var e = t._index;\n      this._markerGroup.removeLayer(t), this._markers.splice(e, 1), this._spliceLatLngs(e, 1), this._updateIndexes(e, -1), t.off(\"dragstart\", this._onMarkerDragStart, this).off(\"drag\", this._onMarkerDrag, this).off(\"dragend\", this._fireEdit, this).off(\"touchmove\", this._onMarkerDrag, this).off(\"touchend\", this._fireEdit, this).off(\"click\", this._onMarkerClick, this).off(\"MSPointerMove\", this._onTouchMove, this).off(\"MSPointerUp\", this._fireEdit, this);\n    },\n    _fireEdit: function () {\n      this._poly.edited = !0, this._poly.fire(\"edit\"), this._poly._map.fire(L.Draw.Event.EDITVERTEX, {\n        layers: this._markerGroup,\n        poly: this._poly\n      });\n    },\n    _onMarkerDrag: function (t) {\n      var e = t.target,\n        i = this._poly;\n      if (L.extend(e._origLatLng, e._latlng), e._middleLeft && e._middleLeft.setLatLng(this._getMiddleLatLng(e._prev, e)), e._middleRight && e._middleRight.setLatLng(this._getMiddleLatLng(e, e._next)), i.options.poly) {\n        var o = i._map._editTooltip;\n        if (!i.options.poly.allowIntersection && i.intersects()) {\n          var a = i.options.color;\n          i.setStyle({\n            color: this.options.drawError.color\n          }), 0 !== L.version.indexOf(\"0.7\") && e.dragging._draggable._onUp(t), this._onMarkerClick(t), o && o.updateContent({\n            text: L.drawLocal.draw.handlers.polyline.error\n          }), setTimeout(function () {\n            i.setStyle({\n              color: a\n            }), o && o.updateContent({\n              text: L.drawLocal.edit.handlers.edit.tooltip.text,\n              subtext: L.drawLocal.edit.handlers.edit.tooltip.subtext\n            });\n          }, 1e3);\n        }\n      }\n      this._poly._bounds._southWest = L.latLng(1 / 0, 1 / 0), this._poly._bounds._northEast = L.latLng(-1 / 0, -1 / 0);\n      var n = this._poly.getLatLngs();\n      this._poly._convertLatLngs(n, !0), this._poly.redraw(), this._poly.fire(\"editdrag\");\n    },\n    _onMarkerClick: function (t) {\n      var e = L.Polygon && this._poly instanceof L.Polygon ? 4 : 3,\n        i = t.target;\n      this._defaultShape().length < e || (this._removeMarker(i), this._updatePrevNext(i._prev, i._next), i._middleLeft && this._markerGroup.removeLayer(i._middleLeft), i._middleRight && this._markerGroup.removeLayer(i._middleRight), i._prev && i._next ? this._createMiddleMarker(i._prev, i._next) : i._prev ? i._next || (i._prev._middleRight = null) : i._next._middleLeft = null, this._fireEdit());\n    },\n    _onContextMenu: function (t) {\n      var e = t.target;\n      this._poly;\n      this._poly._map.fire(L.Draw.Event.MARKERCONTEXT, {\n        marker: e,\n        layers: this._markerGroup,\n        poly: this._poly\n      }), L.DomEvent.stopPropagation;\n    },\n    _onTouchMove: function (t) {\n      var e = this._map.mouseEventToLayerPoint(t.originalEvent.touches[0]),\n        i = this._map.layerPointToLatLng(e),\n        o = t.target;\n      L.extend(o._origLatLng, i), o._middleLeft && o._middleLeft.setLatLng(this._getMiddleLatLng(o._prev, o)), o._middleRight && o._middleRight.setLatLng(this._getMiddleLatLng(o, o._next)), this._poly.redraw(), this.updateMarkers();\n    },\n    _updateIndexes: function (t, e) {\n      this._markerGroup.eachLayer(function (i) {\n        i._index > t && (i._index += e);\n      });\n    },\n    _createMiddleMarker: function (t, e) {\n      var i,\n        o,\n        a,\n        n = this._getMiddleLatLng(t, e),\n        s = this._createMarker(n);\n      s.setOpacity(.6), t._middleRight = e._middleLeft = s, o = function () {\n        s.off(\"touchmove\", o, this);\n        var a = e._index;\n        s._index = a, s.off(\"click\", i, this).on(\"click\", this._onMarkerClick, this), n.lat = s.getLatLng().lat, n.lng = s.getLatLng().lng, this._spliceLatLngs(a, 0, n), this._markers.splice(a, 0, s), s.setOpacity(1), this._updateIndexes(a, 1), e._index++, this._updatePrevNext(t, s), this._updatePrevNext(s, e), this._poly.fire(\"editstart\");\n      }, a = function () {\n        s.off(\"dragstart\", o, this), s.off(\"dragend\", a, this), s.off(\"touchmove\", o, this), this._createMiddleMarker(t, s), this._createMiddleMarker(s, e);\n      }, i = function () {\n        o.call(this), a.call(this), this._fireEdit();\n      }, s.on(\"click\", i, this).on(\"dragstart\", o, this).on(\"dragend\", a, this).on(\"touchmove\", o, this), this._markerGroup.addLayer(s);\n    },\n    _updatePrevNext: function (t, e) {\n      t && (t._next = e), e && (e._prev = t);\n    },\n    _getMiddleLatLng: function (t, e) {\n      var i = this._poly._map,\n        o = i.project(t.getLatLng()),\n        a = i.project(e.getLatLng());\n      return i.unproject(o._add(a)._divideBy(2));\n    }\n  }), L.Polyline.addInitHook(function () {\n    this.editing || (L.Edit.Poly && (this.editing = new L.Edit.Poly(this), this.options.editable && this.editing.enable()), this.on(\"add\", function () {\n      this.editing && this.editing.enabled() && this.editing.addHooks();\n    }), this.on(\"remove\", function () {\n      this.editing && this.editing.enabled() && this.editing.removeHooks();\n    }));\n  }), L.Edit = L.Edit || {}, L.Edit.SimpleShape = L.Handler.extend({\n    options: {\n      moveIcon: new L.DivIcon({\n        iconSize: new L.Point(8, 8),\n        className: \"leaflet-div-icon leaflet-editing-icon leaflet-edit-move\"\n      }),\n      resizeIcon: new L.DivIcon({\n        iconSize: new L.Point(8, 8),\n        className: \"leaflet-div-icon leaflet-editing-icon leaflet-edit-resize\"\n      }),\n      touchMoveIcon: new L.DivIcon({\n        iconSize: new L.Point(20, 20),\n        className: \"leaflet-div-icon leaflet-editing-icon leaflet-edit-move leaflet-touch-icon\"\n      }),\n      touchResizeIcon: new L.DivIcon({\n        iconSize: new L.Point(20, 20),\n        className: \"leaflet-div-icon leaflet-editing-icon leaflet-edit-resize leaflet-touch-icon\"\n      })\n    },\n    initialize: function (t, e) {\n      L.Browser.touch && (this.options.moveIcon = this.options.touchMoveIcon, this.options.resizeIcon = this.options.touchResizeIcon), this._shape = t, L.Util.setOptions(this, e);\n    },\n    addHooks: function () {\n      var t = this._shape;\n      this._shape._map && (this._map = this._shape._map, t.setStyle(t.options.editing), t._map && (this._map = t._map, this._markerGroup || this._initMarkers(), this._map.addLayer(this._markerGroup)));\n    },\n    removeHooks: function () {\n      var t = this._shape;\n      if (t.setStyle(t.options.original), t._map) {\n        this._unbindMarker(this._moveMarker);\n        for (var e = 0, i = this._resizeMarkers.length; e < i; e++) this._unbindMarker(this._resizeMarkers[e]);\n        this._resizeMarkers = null, this._map.removeLayer(this._markerGroup), delete this._markerGroup;\n      }\n      this._map = null;\n    },\n    updateMarkers: function () {\n      this._markerGroup.clearLayers(), this._initMarkers();\n    },\n    _initMarkers: function () {\n      this._markerGroup || (this._markerGroup = new L.LayerGroup()), this._createMoveMarker(), this._createResizeMarker();\n    },\n    _createMoveMarker: function () {},\n    _createResizeMarker: function () {},\n    _createMarker: function (t, e) {\n      var i = new L.Marker.Touch(t, {\n        draggable: !0,\n        icon: e,\n        zIndexOffset: 10\n      });\n      return this._bindMarker(i), this._markerGroup.addLayer(i), i;\n    },\n    _bindMarker: function (t) {\n      t.on(\"dragstart\", this._onMarkerDragStart, this).on(\"drag\", this._onMarkerDrag, this).on(\"dragend\", this._onMarkerDragEnd, this).on(\"touchstart\", this._onTouchStart, this).on(\"touchmove\", this._onTouchMove, this).on(\"MSPointerMove\", this._onTouchMove, this).on(\"touchend\", this._onTouchEnd, this).on(\"MSPointerUp\", this._onTouchEnd, this);\n    },\n    _unbindMarker: function (t) {\n      t.off(\"dragstart\", this._onMarkerDragStart, this).off(\"drag\", this._onMarkerDrag, this).off(\"dragend\", this._onMarkerDragEnd, this).off(\"touchstart\", this._onTouchStart, this).off(\"touchmove\", this._onTouchMove, this).off(\"MSPointerMove\", this._onTouchMove, this).off(\"touchend\", this._onTouchEnd, this).off(\"MSPointerUp\", this._onTouchEnd, this);\n    },\n    _onMarkerDragStart: function (t) {\n      t.target.setOpacity(0), this._shape.fire(\"editstart\");\n    },\n    _fireEdit: function () {\n      this._shape.edited = !0, this._shape.fire(\"edit\");\n    },\n    _onMarkerDrag: function (t) {\n      var e = t.target,\n        i = e.getLatLng();\n      e === this._moveMarker ? this._move(i) : this._resize(i), this._shape.redraw(), this._shape.fire(\"editdrag\");\n    },\n    _onMarkerDragEnd: function (t) {\n      t.target.setOpacity(1), this._fireEdit();\n    },\n    _onTouchStart: function (t) {\n      if (L.Edit.SimpleShape.prototype._onMarkerDragStart.call(this, t), \"function\" == typeof this._getCorners) {\n        var e = this._getCorners(),\n          i = t.target,\n          o = i._cornerIndex;\n        i.setOpacity(0), this._oppositeCorner = e[(o + 2) % 4], this._toggleCornerMarkers(0, o);\n      }\n      this._shape.fire(\"editstart\");\n    },\n    _onTouchMove: function (t) {\n      var e = this._map.mouseEventToLayerPoint(t.originalEvent.touches[0]),\n        i = this._map.layerPointToLatLng(e);\n      return t.target === this._moveMarker ? this._move(i) : this._resize(i), this._shape.redraw(), !1;\n    },\n    _onTouchEnd: function (t) {\n      t.target.setOpacity(1), this.updateMarkers(), this._fireEdit();\n    },\n    _move: function () {},\n    _resize: function () {}\n  }), L.Edit = L.Edit || {}, L.Edit.Rectangle = L.Edit.SimpleShape.extend({\n    _createMoveMarker: function () {\n      var t = this._shape.getBounds(),\n        e = t.getCenter();\n      this._moveMarker = this._createMarker(e, this.options.moveIcon);\n    },\n    _createResizeMarker: function () {\n      var t = this._getCorners();\n      this._resizeMarkers = [];\n      for (var e = 0, i = t.length; e < i; e++) this._resizeMarkers.push(this._createMarker(t[e], this.options.resizeIcon)), this._resizeMarkers[e]._cornerIndex = e;\n    },\n    _onMarkerDragStart: function (t) {\n      L.Edit.SimpleShape.prototype._onMarkerDragStart.call(this, t);\n      var e = this._getCorners(),\n        i = t.target,\n        o = i._cornerIndex;\n      this._oppositeCorner = e[(o + 2) % 4], this._toggleCornerMarkers(0, o);\n    },\n    _onMarkerDragEnd: function (t) {\n      var e,\n        i,\n        o = t.target;\n      o === this._moveMarker && (e = this._shape.getBounds(), i = e.getCenter(), o.setLatLng(i)), this._toggleCornerMarkers(1), this._repositionCornerMarkers(), L.Edit.SimpleShape.prototype._onMarkerDragEnd.call(this, t);\n    },\n    _move: function (t) {\n      for (var e, i = this._shape._defaultShape ? this._shape._defaultShape() : this._shape.getLatLngs(), o = this._shape.getBounds(), a = o.getCenter(), n = [], s = 0, r = i.length; s < r; s++) e = [i[s].lat - a.lat, i[s].lng - a.lng], n.push([t.lat + e[0], t.lng + e[1]]);\n      this._shape.setLatLngs(n), this._repositionCornerMarkers(), this._map.fire(L.Draw.Event.EDITMOVE, {\n        layer: this._shape\n      });\n    },\n    _resize: function (t) {\n      var e;\n      this._shape.setBounds(L.latLngBounds(t, this._oppositeCorner)), e = this._shape.getBounds(), this._moveMarker.setLatLng(e.getCenter()), this._map.fire(L.Draw.Event.EDITRESIZE, {\n        layer: this._shape\n      });\n    },\n    _getCorners: function () {\n      var t = this._shape.getBounds();\n      return [t.getNorthWest(), t.getNorthEast(), t.getSouthEast(), t.getSouthWest()];\n    },\n    _toggleCornerMarkers: function (t) {\n      for (var e = 0, i = this._resizeMarkers.length; e < i; e++) this._resizeMarkers[e].setOpacity(t);\n    },\n    _repositionCornerMarkers: function () {\n      for (var t = this._getCorners(), e = 0, i = this._resizeMarkers.length; e < i; e++) this._resizeMarkers[e].setLatLng(t[e]);\n    }\n  }), L.Rectangle.addInitHook(function () {\n    L.Edit.Rectangle && (this.editing = new L.Edit.Rectangle(this), this.options.editable && this.editing.enable());\n  }), L.Edit = L.Edit || {}, L.Edit.CircleMarker = L.Edit.SimpleShape.extend({\n    _createMoveMarker: function () {\n      var t = this._shape.getLatLng();\n      this._moveMarker = this._createMarker(t, this.options.moveIcon);\n    },\n    _createResizeMarker: function () {\n      this._resizeMarkers = [];\n    },\n    _move: function (t) {\n      if (this._resizeMarkers.length) {\n        var e = this._getResizeMarkerPoint(t);\n        this._resizeMarkers[0].setLatLng(e);\n      }\n      this._shape.setLatLng(t), this._map.fire(L.Draw.Event.EDITMOVE, {\n        layer: this._shape\n      });\n    }\n  }), L.CircleMarker.addInitHook(function () {\n    L.Edit.CircleMarker && (this.editing = new L.Edit.CircleMarker(this), this.options.editable && this.editing.enable()), this.on(\"add\", function () {\n      this.editing && this.editing.enabled() && this.editing.addHooks();\n    }), this.on(\"remove\", function () {\n      this.editing && this.editing.enabled() && this.editing.removeHooks();\n    });\n  }), L.Edit = L.Edit || {}, L.Edit.Circle = L.Edit.CircleMarker.extend({\n    _createResizeMarker: function () {\n      var t = this._shape.getLatLng(),\n        e = this._getResizeMarkerPoint(t);\n      this._resizeMarkers = [], this._resizeMarkers.push(this._createMarker(e, this.options.resizeIcon));\n    },\n    _getResizeMarkerPoint: function (t) {\n      var e = this._shape._radius * Math.cos(Math.PI / 4),\n        i = this._map.project(t);\n      return this._map.unproject([i.x + e, i.y - e]);\n    },\n    _resize: function (t) {\n      var e = this._moveMarker.getLatLng();\n      L.GeometryUtil.isVersion07x() ? radius = e.distanceTo(t) : radius = this._map.distance(e, t), this._shape.setRadius(radius), this._map.editTooltip && this._map._editTooltip.updateContent({\n        text: L.drawLocal.edit.handlers.edit.tooltip.subtext + \"<br />\" + L.drawLocal.edit.handlers.edit.tooltip.text,\n        subtext: L.drawLocal.draw.handlers.circle.radius + \": \" + L.GeometryUtil.readableDistance(radius, !0, this.options.feet, this.options.nautic)\n      }), this._shape.setRadius(radius), this._map.fire(L.Draw.Event.EDITRESIZE, {\n        layer: this._shape\n      });\n    }\n  }), L.Circle.addInitHook(function () {\n    L.Edit.Circle && (this.editing = new L.Edit.Circle(this), this.options.editable && this.editing.enable()), this.on(\"add\", function () {\n      this.editing && this.editing.enabled() && this.editing.addHooks();\n    }), this.on(\"remove\", function () {\n      this.editing && this.editing.enabled() && this.editing.removeHooks();\n    });\n  }), L.Map.mergeOptions({\n    touchExtend: !0\n  }), L.Map.TouchExtend = L.Handler.extend({\n    initialize: function (t) {\n      this._map = t, this._container = t._container, this._pane = t._panes.overlayPane;\n    },\n    addHooks: function () {\n      L.DomEvent.on(this._container, \"touchstart\", this._onTouchStart, this), L.DomEvent.on(this._container, \"touchend\", this._onTouchEnd, this), L.DomEvent.on(this._container, \"touchmove\", this._onTouchMove, this), this._detectIE() ? (L.DomEvent.on(this._container, \"MSPointerDown\", this._onTouchStart, this), L.DomEvent.on(this._container, \"MSPointerUp\", this._onTouchEnd, this), L.DomEvent.on(this._container, \"MSPointerMove\", this._onTouchMove, this), L.DomEvent.on(this._container, \"MSPointerCancel\", this._onTouchCancel, this)) : (L.DomEvent.on(this._container, \"touchcancel\", this._onTouchCancel, this), L.DomEvent.on(this._container, \"touchleave\", this._onTouchLeave, this));\n    },\n    removeHooks: function () {\n      L.DomEvent.off(this._container, \"touchstart\", this._onTouchStart), L.DomEvent.off(this._container, \"touchend\", this._onTouchEnd), L.DomEvent.off(this._container, \"touchmove\", this._onTouchMove), this._detectIE() ? (L.DomEvent.off(this._container, \"MSPointerDowm\", this._onTouchStart), L.DomEvent.off(this._container, \"MSPointerUp\", this._onTouchEnd), L.DomEvent.off(this._container, \"MSPointerMove\", this._onTouchMove), L.DomEvent.off(this._container, \"MSPointerCancel\", this._onTouchCancel)) : (L.DomEvent.off(this._container, \"touchcancel\", this._onTouchCancel), L.DomEvent.off(this._container, \"touchleave\", this._onTouchLeave));\n    },\n    _touchEvent: function (t, e) {\n      var i = {};\n      if (void 0 !== t.touches) {\n        if (!t.touches.length) return;\n        i = t.touches[0];\n      } else {\n        if (\"touch\" !== t.pointerType) return;\n        if (i = t, !this._filterClick(t)) return;\n      }\n      var o = this._map.mouseEventToContainerPoint(i),\n        a = this._map.mouseEventToLayerPoint(i),\n        n = this._map.layerPointToLatLng(a);\n      this._map.fire(e, {\n        latlng: n,\n        layerPoint: a,\n        containerPoint: o,\n        pageX: i.pageX,\n        pageY: i.pageY,\n        originalEvent: t\n      });\n    },\n    _filterClick: function (t) {\n      var e = t.timeStamp || t.originalEvent.timeStamp,\n        i = L.DomEvent._lastClick && e - L.DomEvent._lastClick;\n      return i && i > 100 && i < 500 || t.target._simulatedClick && !t._simulated ? (L.DomEvent.stop(t), !1) : (L.DomEvent._lastClick = e, !0);\n    },\n    _onTouchStart: function (t) {\n      if (this._map._loaded) {\n        this._touchEvent(t, \"touchstart\");\n      }\n    },\n    _onTouchEnd: function (t) {\n      if (this._map._loaded) {\n        this._touchEvent(t, \"touchend\");\n      }\n    },\n    _onTouchCancel: function (t) {\n      if (this._map._loaded) {\n        var e = \"touchcancel\";\n        this._detectIE() && (e = \"pointercancel\"), this._touchEvent(t, e);\n      }\n    },\n    _onTouchLeave: function (t) {\n      if (this._map._loaded) {\n        this._touchEvent(t, \"touchleave\");\n      }\n    },\n    _onTouchMove: function (t) {\n      if (this._map._loaded) {\n        this._touchEvent(t, \"touchmove\");\n      }\n    },\n    _detectIE: function () {\n      var e = t.navigator.userAgent,\n        i = e.indexOf(\"MSIE \");\n      if (i > 0) return parseInt(e.substring(i + 5, e.indexOf(\".\", i)), 10);\n      if (e.indexOf(\"Trident/\") > 0) {\n        var o = e.indexOf(\"rv:\");\n        return parseInt(e.substring(o + 3, e.indexOf(\".\", o)), 10);\n      }\n      var a = e.indexOf(\"Edge/\");\n      return a > 0 && parseInt(e.substring(a + 5, e.indexOf(\".\", a)), 10);\n    }\n  }), L.Map.addInitHook(\"addHandler\", \"touchExtend\", L.Map.TouchExtend), L.Marker.Touch = L.Marker.extend({\n    _initInteraction: function () {\n      return this.addInteractiveTarget ? L.Marker.prototype._initInteraction.apply(this) : this._initInteractionLegacy();\n    },\n    _initInteractionLegacy: function () {\n      if (this.options.clickable) {\n        var t = this._icon,\n          e = [\"dblclick\", \"mousedown\", \"mouseover\", \"mouseout\", \"contextmenu\", \"touchstart\", \"touchend\", \"touchmove\"];\n        this._detectIE ? e.concat([\"MSPointerDown\", \"MSPointerUp\", \"MSPointerMove\", \"MSPointerCancel\"]) : e.concat([\"touchcancel\"]), L.DomUtil.addClass(t, \"leaflet-clickable\"), L.DomEvent.on(t, \"click\", this._onMouseClick, this), L.DomEvent.on(t, \"keypress\", this._onKeyPress, this);\n        for (var i = 0; i < e.length; i++) L.DomEvent.on(t, e[i], this._fireMouseEvent, this);\n        L.Handler.MarkerDrag && (this.dragging = new L.Handler.MarkerDrag(this), this.options.draggable && this.dragging.enable());\n      }\n    },\n    _detectIE: function () {\n      var e = t.navigator.userAgent,\n        i = e.indexOf(\"MSIE \");\n      if (i > 0) return parseInt(e.substring(i + 5, e.indexOf(\".\", i)), 10);\n      if (e.indexOf(\"Trident/\") > 0) {\n        var o = e.indexOf(\"rv:\");\n        return parseInt(e.substring(o + 3, e.indexOf(\".\", o)), 10);\n      }\n      var a = e.indexOf(\"Edge/\");\n      return a > 0 && parseInt(e.substring(a + 5, e.indexOf(\".\", a)), 10);\n    }\n  }), L.LatLngUtil = {\n    cloneLatLngs: function (t) {\n      for (var e = [], i = 0, o = t.length; i < o; i++) Array.isArray(t[i]) ? e.push(L.LatLngUtil.cloneLatLngs(t[i])) : e.push(this.cloneLatLng(t[i]));\n      return e;\n    },\n    cloneLatLng: function (t) {\n      return L.latLng(t.lat, t.lng);\n    }\n  }, function () {\n    var t = {\n      km: 2,\n      ha: 2,\n      m: 0,\n      mi: 2,\n      ac: 2,\n      yd: 0,\n      ft: 0,\n      nm: 2\n    };\n    L.GeometryUtil = L.extend(L.GeometryUtil || {}, {\n      geodesicArea: function (t) {\n        var e,\n          i,\n          o = t.length,\n          a = 0,\n          n = Math.PI / 180;\n        if (o > 2) {\n          for (var s = 0; s < o; s++) e = t[s], i = t[(s + 1) % o], a += (i.lng - e.lng) * n * (2 + Math.sin(e.lat * n) + Math.sin(i.lat * n));\n          a = 6378137 * a * 6378137 / 2;\n        }\n        return Math.abs(a);\n      },\n      formattedNumber: function (t, e) {\n        var i = parseFloat(t).toFixed(e),\n          o = L.drawLocal.format && L.drawLocal.format.numeric,\n          a = o && o.delimiters,\n          n = a && a.thousands,\n          s = a && a.decimal;\n        if (n || s) {\n          var r = i.split(\".\");\n          i = n ? r[0].replace(/(\\d)(?=(\\d{3})+(?!\\d))/g, \"$1\" + n) : r[0], s = s || \".\", r.length > 1 && (i = i + s + r[1]);\n        }\n        return i;\n      },\n      readableArea: function (e, i, o) {\n        var a,\n          n,\n          o = L.Util.extend({}, t, o);\n        return i ? (n = [\"ha\", \"m\"], type = typeof i, \"string\" === type ? n = [i] : \"boolean\" !== type && (n = i), a = e >= 1e6 && -1 !== n.indexOf(\"km\") ? L.GeometryUtil.formattedNumber(1e-6 * e, o.km) + \" km²\" : e >= 1e4 && -1 !== n.indexOf(\"ha\") ? L.GeometryUtil.formattedNumber(1e-4 * e, o.ha) + \" ha\" : L.GeometryUtil.formattedNumber(e, o.m) + \" m²\") : (e /= .836127, a = e >= 3097600 ? L.GeometryUtil.formattedNumber(e / 3097600, o.mi) + \" mi²\" : e >= 4840 ? L.GeometryUtil.formattedNumber(e / 4840, o.ac) + \" acres\" : L.GeometryUtil.formattedNumber(e, o.yd) + \" yd²\"), a;\n      },\n      readableDistance: function (e, i, o, a, n) {\n        var s,\n          n = L.Util.extend({}, t, n);\n        switch (i ? \"string\" == typeof i ? i : \"metric\" : o ? \"feet\" : a ? \"nauticalMile\" : \"yards\") {\n          case \"metric\":\n            s = e > 1e3 ? L.GeometryUtil.formattedNumber(e / 1e3, n.km) + \" km\" : L.GeometryUtil.formattedNumber(e, n.m) + \" m\";\n            break;\n          case \"feet\":\n            e *= 3.28083, s = L.GeometryUtil.formattedNumber(e, n.ft) + \" ft\";\n            break;\n          case \"nauticalMile\":\n            e *= .53996, s = L.GeometryUtil.formattedNumber(e / 1e3, n.nm) + \" nm\";\n            break;\n          case \"yards\":\n          default:\n            e *= 1.09361, s = e > 1760 ? L.GeometryUtil.formattedNumber(e / 1760, n.mi) + \" miles\" : L.GeometryUtil.formattedNumber(e, n.yd) + \" yd\";\n        }\n        return s;\n      },\n      isVersion07x: function () {\n        var t = L.version.split(\".\");\n        return 0 === parseInt(t[0], 10) && 7 === parseInt(t[1], 10);\n      }\n    });\n  }(), L.Util.extend(L.LineUtil, {\n    segmentsIntersect: function (t, e, i, o) {\n      return this._checkCounterclockwise(t, i, o) !== this._checkCounterclockwise(e, i, o) && this._checkCounterclockwise(t, e, i) !== this._checkCounterclockwise(t, e, o);\n    },\n    _checkCounterclockwise: function (t, e, i) {\n      return (i.y - t.y) * (e.x - t.x) > (e.y - t.y) * (i.x - t.x);\n    }\n  }), L.Polyline.include({\n    intersects: function () {\n      var t,\n        e,\n        i,\n        o = this._getProjectedPoints(),\n        a = o ? o.length : 0;\n      if (this._tooFewPointsForIntersection()) return !1;\n      for (t = a - 1; t >= 3; t--) if (e = o[t - 1], i = o[t], this._lineSegmentsIntersectsRange(e, i, t - 2)) return !0;\n      return !1;\n    },\n    newLatLngIntersects: function (t, e) {\n      return !!this._map && this.newPointIntersects(this._map.latLngToLayerPoint(t), e);\n    },\n    newPointIntersects: function (t, e) {\n      var i = this._getProjectedPoints(),\n        o = i ? i.length : 0,\n        a = i ? i[o - 1] : null,\n        n = o - 2;\n      return !this._tooFewPointsForIntersection(1) && this._lineSegmentsIntersectsRange(a, t, n, e ? 1 : 0);\n    },\n    _tooFewPointsForIntersection: function (t) {\n      var e = this._getProjectedPoints(),\n        i = e ? e.length : 0;\n      return i += t || 0, !e || i <= 3;\n    },\n    _lineSegmentsIntersectsRange: function (t, e, i, o) {\n      var a,\n        n,\n        s = this._getProjectedPoints();\n      o = o || 0;\n      for (var r = i; r > o; r--) if (a = s[r - 1], n = s[r], L.LineUtil.segmentsIntersect(t, e, a, n)) return !0;\n      return !1;\n    },\n    _getProjectedPoints: function () {\n      if (!this._defaultShape) return this._originalPoints;\n      for (var t = [], e = this._defaultShape(), i = 0; i < e.length; i++) t.push(this._map.latLngToLayerPoint(e[i]));\n      return t;\n    }\n  }), L.Polygon.include({\n    intersects: function () {\n      var t,\n        e,\n        i,\n        o,\n        a = this._getProjectedPoints();\n      return !this._tooFewPointsForIntersection() && (!!L.Polyline.prototype.intersects.call(this) || (t = a.length, e = a[0], i = a[t - 1], o = t - 2, this._lineSegmentsIntersectsRange(i, e, o, 1)));\n    }\n  }), L.Control.Draw = L.Control.extend({\n    options: {\n      position: \"topleft\",\n      draw: {},\n      edit: !1\n    },\n    initialize: function (t) {\n      if (L.version < \"0.7\") throw new Error(\"Leaflet.draw 0.2.3+ requires Leaflet 0.7.0+. Download latest from https://github.com/Leaflet/Leaflet/\");\n      L.Control.prototype.initialize.call(this, t);\n      var e;\n      this._toolbars = {}, L.DrawToolbar && this.options.draw && (e = new L.DrawToolbar(this.options.draw), this._toolbars[L.DrawToolbar.TYPE] = e, this._toolbars[L.DrawToolbar.TYPE].on(\"enable\", this._toolbarEnabled, this)), L.EditToolbar && this.options.edit && (e = new L.EditToolbar(this.options.edit), this._toolbars[L.EditToolbar.TYPE] = e, this._toolbars[L.EditToolbar.TYPE].on(\"enable\", this._toolbarEnabled, this)), L.toolbar = this;\n    },\n    onAdd: function (t) {\n      var e,\n        i = L.DomUtil.create(\"div\", \"leaflet-draw\"),\n        o = !1;\n      for (var a in this._toolbars) this._toolbars.hasOwnProperty(a) && (e = this._toolbars[a].addToolbar(t)) && (o || (L.DomUtil.hasClass(e, \"leaflet-draw-toolbar-top\") || L.DomUtil.addClass(e.childNodes[0], \"leaflet-draw-toolbar-top\"), o = !0), i.appendChild(e));\n      return i;\n    },\n    onRemove: function () {\n      for (var t in this._toolbars) this._toolbars.hasOwnProperty(t) && this._toolbars[t].removeToolbar();\n    },\n    setDrawingOptions: function (t) {\n      for (var e in this._toolbars) this._toolbars[e] instanceof L.DrawToolbar && this._toolbars[e].setOptions(t);\n    },\n    _toolbarEnabled: function (t) {\n      var e = t.target;\n      for (var i in this._toolbars) this._toolbars[i] !== e && this._toolbars[i].disable();\n    }\n  }), L.Map.mergeOptions({\n    drawControlTooltips: !0,\n    drawControl: !1\n  }), L.Map.addInitHook(function () {\n    this.options.drawControl && (this.drawControl = new L.Control.Draw(), this.addControl(this.drawControl));\n  }), L.Toolbar = L.Class.extend({\n    initialize: function (t) {\n      L.setOptions(this, t), this._modes = {}, this._actionButtons = [], this._activeMode = null;\n      var e = L.version.split(\".\");\n      1 === parseInt(e[0], 10) && parseInt(e[1], 10) >= 2 ? L.Toolbar.include(L.Evented.prototype) : L.Toolbar.include(L.Mixin.Events);\n    },\n    enabled: function () {\n      return null !== this._activeMode;\n    },\n    disable: function () {\n      this.enabled() && this._activeMode.handler.disable();\n    },\n    addToolbar: function (t) {\n      var e,\n        i = L.DomUtil.create(\"div\", \"leaflet-draw-section\"),\n        o = 0,\n        a = this._toolbarClass || \"\",\n        n = this.getModeHandlers(t);\n      for (this._toolbarContainer = L.DomUtil.create(\"div\", \"leaflet-draw-toolbar leaflet-bar\"), this._map = t, e = 0; e < n.length; e++) n[e].enabled && this._initModeHandler(n[e].handler, this._toolbarContainer, o++, a, n[e].title);\n      if (o) return this._lastButtonIndex = --o, this._actionsContainer = L.DomUtil.create(\"ul\", \"leaflet-draw-actions\"), i.appendChild(this._toolbarContainer), i.appendChild(this._actionsContainer), i;\n    },\n    removeToolbar: function () {\n      for (var t in this._modes) this._modes.hasOwnProperty(t) && (this._disposeButton(this._modes[t].button, this._modes[t].handler.enable, this._modes[t].handler), this._modes[t].handler.disable(), this._modes[t].handler.off(\"enabled\", this._handlerActivated, this).off(\"disabled\", this._handlerDeactivated, this));\n      this._modes = {};\n      for (var e = 0, i = this._actionButtons.length; e < i; e++) this._disposeButton(this._actionButtons[e].button, this._actionButtons[e].callback, this);\n      this._actionButtons = [], this._actionsContainer = null;\n    },\n    _initModeHandler: function (t, e, i, o, a) {\n      var n = t.type;\n      this._modes[n] = {}, this._modes[n].handler = t, this._modes[n].button = this._createButton({\n        type: n,\n        title: a,\n        className: o + \"-\" + n,\n        container: e,\n        callback: this._modes[n].handler.enable,\n        context: this._modes[n].handler\n      }), this._modes[n].buttonIndex = i, this._modes[n].handler.on(\"enabled\", this._handlerActivated, this).on(\"disabled\", this._handlerDeactivated, this);\n    },\n    _detectIOS: function () {\n      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !t.MSStream;\n    },\n    _createButton: function (t) {\n      var e = L.DomUtil.create(\"a\", t.className || \"\", t.container),\n        i = L.DomUtil.create(\"span\", \"sr-only\", t.container);\n      e.href = \"#\", e.appendChild(i), t.title && (e.title = t.title, i.innerHTML = t.title), t.text && (e.innerHTML = t.text, i.innerHTML = t.text);\n      var o = this._detectIOS() ? \"touchstart\" : \"click\";\n      return L.DomEvent.on(e, \"click\", L.DomEvent.stopPropagation).on(e, \"mousedown\", L.DomEvent.stopPropagation).on(e, \"dblclick\", L.DomEvent.stopPropagation).on(e, \"touchstart\", L.DomEvent.stopPropagation).on(e, \"click\", L.DomEvent.preventDefault).on(e, o, t.callback, t.context), e;\n    },\n    _disposeButton: function (t, e) {\n      var i = this._detectIOS() ? \"touchstart\" : \"click\";\n      L.DomEvent.off(t, \"click\", L.DomEvent.stopPropagation).off(t, \"mousedown\", L.DomEvent.stopPropagation).off(t, \"dblclick\", L.DomEvent.stopPropagation).off(t, \"touchstart\", L.DomEvent.stopPropagation).off(t, \"click\", L.DomEvent.preventDefault).off(t, i, e);\n    },\n    _handlerActivated: function (t) {\n      this.disable(), this._activeMode = this._modes[t.handler], L.DomUtil.addClass(this._activeMode.button, \"leaflet-draw-toolbar-button-enabled\"), this._showActionsToolbar(), this.fire(\"enable\");\n    },\n    _handlerDeactivated: function () {\n      this._hideActionsToolbar(), L.DomUtil.removeClass(this._activeMode.button, \"leaflet-draw-toolbar-button-enabled\"), this._activeMode = null, this.fire(\"disable\");\n    },\n    _createActions: function (t) {\n      var e,\n        i,\n        o,\n        a,\n        n = this._actionsContainer,\n        s = this.getActions(t),\n        r = s.length;\n      for (i = 0, o = this._actionButtons.length; i < o; i++) this._disposeButton(this._actionButtons[i].button, this._actionButtons[i].callback);\n      for (this._actionButtons = []; n.firstChild;) n.removeChild(n.firstChild);\n      for (var l = 0; l < r; l++) \"enabled\" in s[l] && !s[l].enabled || (e = L.DomUtil.create(\"li\", \"\", n), a = this._createButton({\n        title: s[l].title,\n        text: s[l].text,\n        container: e,\n        callback: s[l].callback,\n        context: s[l].context\n      }), this._actionButtons.push({\n        button: a,\n        callback: s[l].callback\n      }));\n    },\n    _showActionsToolbar: function () {\n      var t = this._activeMode.buttonIndex,\n        e = this._lastButtonIndex,\n        i = this._activeMode.button.offsetTop - 1;\n      this._createActions(this._activeMode.handler), this._actionsContainer.style.top = i + \"px\", 0 === t && (L.DomUtil.addClass(this._toolbarContainer, \"leaflet-draw-toolbar-notop\"), L.DomUtil.addClass(this._actionsContainer, \"leaflet-draw-actions-top\")), t === e && (L.DomUtil.addClass(this._toolbarContainer, \"leaflet-draw-toolbar-nobottom\"), L.DomUtil.addClass(this._actionsContainer, \"leaflet-draw-actions-bottom\")), this._actionsContainer.style.display = \"block\", this._map.fire(L.Draw.Event.TOOLBAROPENED);\n    },\n    _hideActionsToolbar: function () {\n      this._actionsContainer.style.display = \"none\", L.DomUtil.removeClass(this._toolbarContainer, \"leaflet-draw-toolbar-notop\"), L.DomUtil.removeClass(this._toolbarContainer, \"leaflet-draw-toolbar-nobottom\"), L.DomUtil.removeClass(this._actionsContainer, \"leaflet-draw-actions-top\"), L.DomUtil.removeClass(this._actionsContainer, \"leaflet-draw-actions-bottom\"), this._map.fire(L.Draw.Event.TOOLBARCLOSED);\n    }\n  }), L.Draw = L.Draw || {}, L.Draw.Tooltip = L.Class.extend({\n    initialize: function (t) {\n      this._map = t, this._popupPane = t._panes.popupPane, this._visible = !1, this._container = t.options.drawControlTooltips ? L.DomUtil.create(\"div\", \"leaflet-draw-tooltip\", this._popupPane) : null, this._singleLineLabel = !1, this._map.on(\"mouseout\", this._onMouseOut, this);\n    },\n    dispose: function () {\n      this._map.off(\"mouseout\", this._onMouseOut, this), this._container && (this._popupPane.removeChild(this._container), this._container = null);\n    },\n    updateContent: function (t) {\n      return this._container ? (t.subtext = t.subtext || \"\", 0 !== t.subtext.length || this._singleLineLabel ? t.subtext.length > 0 && this._singleLineLabel && (L.DomUtil.removeClass(this._container, \"leaflet-draw-tooltip-single\"), this._singleLineLabel = !1) : (L.DomUtil.addClass(this._container, \"leaflet-draw-tooltip-single\"), this._singleLineLabel = !0), this._container.innerHTML = (t.subtext.length > 0 ? '<span class=\"leaflet-draw-tooltip-subtext\">' + t.subtext + \"</span><br />\" : \"\") + \"<span>\" + t.text + \"</span>\", t.text || t.subtext ? (this._visible = !0, this._container.style.visibility = \"inherit\") : (this._visible = !1, this._container.style.visibility = \"hidden\"), this) : this;\n    },\n    updatePosition: function (t) {\n      var e = this._map.latLngToLayerPoint(t),\n        i = this._container;\n      return this._container && (this._visible && (i.style.visibility = \"inherit\"), L.DomUtil.setPosition(i, e)), this;\n    },\n    showAsError: function () {\n      return this._container && L.DomUtil.addClass(this._container, \"leaflet-error-draw-tooltip\"), this;\n    },\n    removeError: function () {\n      return this._container && L.DomUtil.removeClass(this._container, \"leaflet-error-draw-tooltip\"), this;\n    },\n    _onMouseOut: function () {\n      this._container && (this._container.style.visibility = \"hidden\");\n    }\n  }), L.DrawToolbar = L.Toolbar.extend({\n    statics: {\n      TYPE: \"draw\"\n    },\n    options: {\n      polyline: {},\n      polygon: {},\n      rectangle: {},\n      circle: {},\n      marker: {},\n      circlemarker: {}\n    },\n    initialize: function (t) {\n      for (var e in this.options) this.options.hasOwnProperty(e) && t[e] && (t[e] = L.extend({}, this.options[e], t[e]));\n      this._toolbarClass = \"leaflet-draw-draw\", L.Toolbar.prototype.initialize.call(this, t);\n    },\n    getModeHandlers: function (t) {\n      return [{\n        enabled: this.options.polyline,\n        handler: new L.Draw.Polyline(t, this.options.polyline),\n        title: L.drawLocal.draw.toolbar.buttons.polyline\n      }, {\n        enabled: this.options.polygon,\n        handler: new L.Draw.Polygon(t, this.options.polygon),\n        title: L.drawLocal.draw.toolbar.buttons.polygon\n      }, {\n        enabled: this.options.rectangle,\n        handler: new L.Draw.Rectangle(t, this.options.rectangle),\n        title: L.drawLocal.draw.toolbar.buttons.rectangle\n      }, {\n        enabled: this.options.circle,\n        handler: new L.Draw.Circle(t, this.options.circle),\n        title: L.drawLocal.draw.toolbar.buttons.circle\n      }, {\n        enabled: this.options.marker,\n        handler: new L.Draw.Marker(t, this.options.marker),\n        title: L.drawLocal.draw.toolbar.buttons.marker\n      }, {\n        enabled: this.options.circlemarker,\n        handler: new L.Draw.CircleMarker(t, this.options.circlemarker),\n        title: L.drawLocal.draw.toolbar.buttons.circlemarker\n      }];\n    },\n    getActions: function (t) {\n      return [{\n        enabled: t.completeShape,\n        title: L.drawLocal.draw.toolbar.finish.title,\n        text: L.drawLocal.draw.toolbar.finish.text,\n        callback: t.completeShape,\n        context: t\n      }, {\n        enabled: t.deleteLastVertex,\n        title: L.drawLocal.draw.toolbar.undo.title,\n        text: L.drawLocal.draw.toolbar.undo.text,\n        callback: t.deleteLastVertex,\n        context: t\n      }, {\n        title: L.drawLocal.draw.toolbar.actions.title,\n        text: L.drawLocal.draw.toolbar.actions.text,\n        callback: this.disable,\n        context: this\n      }];\n    },\n    setOptions: function (t) {\n      L.setOptions(this, t);\n      for (var e in this._modes) this._modes.hasOwnProperty(e) && t.hasOwnProperty(e) && this._modes[e].handler.setOptions(t[e]);\n    }\n  }), L.EditToolbar = L.Toolbar.extend({\n    statics: {\n      TYPE: \"edit\"\n    },\n    options: {\n      edit: {\n        selectedPathOptions: {\n          dashArray: \"10, 10\",\n          fill: !0,\n          fillColor: \"#fe57a1\",\n          fillOpacity: .1,\n          maintainColor: !1\n        }\n      },\n      remove: {},\n      poly: null,\n      featureGroup: null\n    },\n    initialize: function (t) {\n      t.edit && (void 0 === t.edit.selectedPathOptions && (t.edit.selectedPathOptions = this.options.edit.selectedPathOptions), t.edit.selectedPathOptions = L.extend({}, this.options.edit.selectedPathOptions, t.edit.selectedPathOptions)), t.remove && (t.remove = L.extend({}, this.options.remove, t.remove)), t.poly && (t.poly = L.extend({}, this.options.poly, t.poly)), this._toolbarClass = \"leaflet-draw-edit\", L.Toolbar.prototype.initialize.call(this, t), this._selectedFeatureCount = 0;\n    },\n    getModeHandlers: function (t) {\n      var e = this.options.featureGroup;\n      return [{\n        enabled: this.options.edit,\n        handler: new L.EditToolbar.Edit(t, {\n          featureGroup: e,\n          selectedPathOptions: this.options.edit.selectedPathOptions,\n          poly: this.options.poly\n        }),\n        title: L.drawLocal.edit.toolbar.buttons.edit\n      }, {\n        enabled: this.options.remove,\n        handler: new L.EditToolbar.Delete(t, {\n          featureGroup: e\n        }),\n        title: L.drawLocal.edit.toolbar.buttons.remove\n      }];\n    },\n    getActions: function (t) {\n      var e = [{\n        title: L.drawLocal.edit.toolbar.actions.save.title,\n        text: L.drawLocal.edit.toolbar.actions.save.text,\n        callback: this._save,\n        context: this\n      }, {\n        title: L.drawLocal.edit.toolbar.actions.cancel.title,\n        text: L.drawLocal.edit.toolbar.actions.cancel.text,\n        callback: this.disable,\n        context: this\n      }];\n      return t.removeAllLayers && e.push({\n        title: L.drawLocal.edit.toolbar.actions.clearAll.title,\n        text: L.drawLocal.edit.toolbar.actions.clearAll.text,\n        callback: this._clearAllLayers,\n        context: this\n      }), e;\n    },\n    addToolbar: function (t) {\n      var e = L.Toolbar.prototype.addToolbar.call(this, t);\n      return this._checkDisabled(), this.options.featureGroup.on(\"layeradd layerremove\", this._checkDisabled, this), e;\n    },\n    removeToolbar: function () {\n      this.options.featureGroup.off(\"layeradd layerremove\", this._checkDisabled, this), L.Toolbar.prototype.removeToolbar.call(this);\n    },\n    disable: function () {\n      this.enabled() && (this._activeMode.handler.revertLayers(), L.Toolbar.prototype.disable.call(this));\n    },\n    _save: function () {\n      this._activeMode.handler.save(), this._activeMode && this._activeMode.handler.disable();\n    },\n    _clearAllLayers: function () {\n      this._activeMode.handler.removeAllLayers(), this._activeMode && this._activeMode.handler.disable();\n    },\n    _checkDisabled: function () {\n      var t,\n        e = this.options.featureGroup,\n        i = 0 !== e.getLayers().length;\n      this.options.edit && (t = this._modes[L.EditToolbar.Edit.TYPE].button, i ? L.DomUtil.removeClass(t, \"leaflet-disabled\") : L.DomUtil.addClass(t, \"leaflet-disabled\"), t.setAttribute(\"title\", i ? L.drawLocal.edit.toolbar.buttons.edit : L.drawLocal.edit.toolbar.buttons.editDisabled)), this.options.remove && (t = this._modes[L.EditToolbar.Delete.TYPE].button, i ? L.DomUtil.removeClass(t, \"leaflet-disabled\") : L.DomUtil.addClass(t, \"leaflet-disabled\"), t.setAttribute(\"title\", i ? L.drawLocal.edit.toolbar.buttons.remove : L.drawLocal.edit.toolbar.buttons.removeDisabled));\n    }\n  }), L.EditToolbar.Edit = L.Handler.extend({\n    statics: {\n      TYPE: \"edit\"\n    },\n    initialize: function (t, e) {\n      if (L.Handler.prototype.initialize.call(this, t), L.setOptions(this, e), this._featureGroup = e.featureGroup, !(this._featureGroup instanceof L.FeatureGroup)) throw new Error(\"options.featureGroup must be a L.FeatureGroup\");\n      this._uneditedLayerProps = {}, this.type = L.EditToolbar.Edit.TYPE;\n      var i = L.version.split(\".\");\n      1 === parseInt(i[0], 10) && parseInt(i[1], 10) >= 2 ? L.EditToolbar.Edit.include(L.Evented.prototype) : L.EditToolbar.Edit.include(L.Mixin.Events);\n    },\n    enable: function () {\n      !this._enabled && this._hasAvailableLayers() && (this.fire(\"enabled\", {\n        handler: this.type\n      }), this._map.fire(L.Draw.Event.EDITSTART, {\n        handler: this.type\n      }), L.Handler.prototype.enable.call(this), this._featureGroup.on(\"layeradd\", this._enableLayerEdit, this).on(\"layerremove\", this._disableLayerEdit, this));\n    },\n    disable: function () {\n      this._enabled && (this._featureGroup.off(\"layeradd\", this._enableLayerEdit, this).off(\"layerremove\", this._disableLayerEdit, this), L.Handler.prototype.disable.call(this), this._map.fire(L.Draw.Event.EDITSTOP, {\n        handler: this.type\n      }), this.fire(\"disabled\", {\n        handler: this.type\n      }));\n    },\n    addHooks: function () {\n      var t = this._map;\n      t && (t.getContainer().focus(), this._featureGroup.eachLayer(this._enableLayerEdit, this), this._tooltip = new L.Draw.Tooltip(this._map), this._tooltip.updateContent({\n        text: L.drawLocal.edit.handlers.edit.tooltip.text,\n        subtext: L.drawLocal.edit.handlers.edit.tooltip.subtext\n      }), t._editTooltip = this._tooltip, this._updateTooltip(), this._map.on(\"mousemove\", this._onMouseMove, this).on(\"touchmove\", this._onMouseMove, this).on(\"MSPointerMove\", this._onMouseMove, this).on(L.Draw.Event.EDITVERTEX, this._updateTooltip, this));\n    },\n    removeHooks: function () {\n      this._map && (this._featureGroup.eachLayer(this._disableLayerEdit, this), this._uneditedLayerProps = {}, this._tooltip.dispose(), this._tooltip = null, this._map.off(\"mousemove\", this._onMouseMove, this).off(\"touchmove\", this._onMouseMove, this).off(\"MSPointerMove\", this._onMouseMove, this).off(L.Draw.Event.EDITVERTEX, this._updateTooltip, this));\n    },\n    revertLayers: function () {\n      this._featureGroup.eachLayer(function (t) {\n        this._revertLayer(t);\n      }, this);\n    },\n    save: function () {\n      var t = new L.LayerGroup();\n      this._featureGroup.eachLayer(function (e) {\n        e.edited && (t.addLayer(e), e.edited = !1);\n      }), this._map.fire(L.Draw.Event.EDITED, {\n        layers: t\n      });\n    },\n    _backupLayer: function (t) {\n      var e = L.Util.stamp(t);\n      this._uneditedLayerProps[e] || (t instanceof L.Polyline || t instanceof L.Polygon || t instanceof L.Rectangle ? this._uneditedLayerProps[e] = {\n        latlngs: L.LatLngUtil.cloneLatLngs(t.getLatLngs())\n      } : t instanceof L.Circle ? this._uneditedLayerProps[e] = {\n        latlng: L.LatLngUtil.cloneLatLng(t.getLatLng()),\n        radius: t.getRadius()\n      } : (t instanceof L.Marker || t instanceof L.CircleMarker) && (this._uneditedLayerProps[e] = {\n        latlng: L.LatLngUtil.cloneLatLng(t.getLatLng())\n      }));\n    },\n    _getTooltipText: function () {\n      return {\n        text: L.drawLocal.edit.handlers.edit.tooltip.text,\n        subtext: L.drawLocal.edit.handlers.edit.tooltip.subtext\n      };\n    },\n    _updateTooltip: function () {\n      this._tooltip.updateContent(this._getTooltipText());\n    },\n    _revertLayer: function (t) {\n      var e = L.Util.stamp(t);\n      t.edited = !1, this._uneditedLayerProps.hasOwnProperty(e) && (t instanceof L.Polyline || t instanceof L.Polygon || t instanceof L.Rectangle ? t.setLatLngs(this._uneditedLayerProps[e].latlngs) : t instanceof L.Circle ? (t.setLatLng(this._uneditedLayerProps[e].latlng), t.setRadius(this._uneditedLayerProps[e].radius)) : (t instanceof L.Marker || t instanceof L.CircleMarker) && t.setLatLng(this._uneditedLayerProps[e].latlng), t.fire(\"revert-edited\", {\n        layer: t\n      }));\n    },\n    _enableLayerEdit: function (t) {\n      var e,\n        i,\n        o = t.layer || t.target || t;\n      this._backupLayer(o), this.options.poly && (i = L.Util.extend({}, this.options.poly), o.options.poly = i), this.options.selectedPathOptions && (e = L.Util.extend({}, this.options.selectedPathOptions), e.maintainColor && (e.color = o.options.color, e.fillColor = o.options.fillColor), o.options.original = L.extend({}, o.options), o.options.editing = e), o instanceof L.Marker ? (o.editing && o.editing.enable(), o.dragging.enable(), o.on(\"dragend\", this._onMarkerDragEnd).on(\"touchmove\", this._onTouchMove, this).on(\"MSPointerMove\", this._onTouchMove, this).on(\"touchend\", this._onMarkerDragEnd, this).on(\"MSPointerUp\", this._onMarkerDragEnd, this)) : o.editing.enable();\n    },\n    _disableLayerEdit: function (t) {\n      var e = t.layer || t.target || t;\n      e.edited = !1, e.editing && e.editing.disable(), delete e.options.editing, delete e.options.original, this._selectedPathOptions && (e instanceof L.Marker ? this._toggleMarkerHighlight(e) : (e.setStyle(e.options.previousOptions), delete e.options.previousOptions)), e instanceof L.Marker ? (e.dragging.disable(), e.off(\"dragend\", this._onMarkerDragEnd, this).off(\"touchmove\", this._onTouchMove, this).off(\"MSPointerMove\", this._onTouchMove, this).off(\"touchend\", this._onMarkerDragEnd, this).off(\"MSPointerUp\", this._onMarkerDragEnd, this)) : e.editing.disable();\n    },\n    _onMouseMove: function (t) {\n      this._tooltip.updatePosition(t.latlng);\n    },\n    _onMarkerDragEnd: function (t) {\n      var e = t.target;\n      e.edited = !0, this._map.fire(L.Draw.Event.EDITMOVE, {\n        layer: e\n      });\n    },\n    _onTouchMove: function (t) {\n      var e = t.originalEvent.changedTouches[0],\n        i = this._map.mouseEventToLayerPoint(e),\n        o = this._map.layerPointToLatLng(i);\n      t.target.setLatLng(o);\n    },\n    _hasAvailableLayers: function () {\n      return 0 !== this._featureGroup.getLayers().length;\n    }\n  }), L.EditToolbar.Delete = L.Handler.extend({\n    statics: {\n      TYPE: \"remove\"\n    },\n    initialize: function (t, e) {\n      if (L.Handler.prototype.initialize.call(this, t), L.Util.setOptions(this, e), this._deletableLayers = this.options.featureGroup, !(this._deletableLayers instanceof L.FeatureGroup)) throw new Error(\"options.featureGroup must be a L.FeatureGroup\");\n      this.type = L.EditToolbar.Delete.TYPE;\n      var i = L.version.split(\".\");\n      1 === parseInt(i[0], 10) && parseInt(i[1], 10) >= 2 ? L.EditToolbar.Delete.include(L.Evented.prototype) : L.EditToolbar.Delete.include(L.Mixin.Events);\n    },\n    enable: function () {\n      !this._enabled && this._hasAvailableLayers() && (this.fire(\"enabled\", {\n        handler: this.type\n      }), this._map.fire(L.Draw.Event.DELETESTART, {\n        handler: this.type\n      }), L.Handler.prototype.enable.call(this), this._deletableLayers.on(\"layeradd\", this._enableLayerDelete, this).on(\"layerremove\", this._disableLayerDelete, this));\n    },\n    disable: function () {\n      this._enabled && (this._deletableLayers.off(\"layeradd\", this._enableLayerDelete, this).off(\"layerremove\", this._disableLayerDelete, this), L.Handler.prototype.disable.call(this), this._map.fire(L.Draw.Event.DELETESTOP, {\n        handler: this.type\n      }), this.fire(\"disabled\", {\n        handler: this.type\n      }));\n    },\n    addHooks: function () {\n      var t = this._map;\n      t && (t.getContainer().focus(), this._deletableLayers.eachLayer(this._enableLayerDelete, this), this._deletedLayers = new L.LayerGroup(), this._tooltip = new L.Draw.Tooltip(this._map), this._tooltip.updateContent({\n        text: L.drawLocal.edit.handlers.remove.tooltip.text\n      }), this._map.on(\"mousemove\", this._onMouseMove, this));\n    },\n    removeHooks: function () {\n      this._map && (this._deletableLayers.eachLayer(this._disableLayerDelete, this), this._deletedLayers = null, this._tooltip.dispose(), this._tooltip = null, this._map.off(\"mousemove\", this._onMouseMove, this));\n    },\n    revertLayers: function () {\n      this._deletedLayers.eachLayer(function (t) {\n        this._deletableLayers.addLayer(t), t.fire(\"revert-deleted\", {\n          layer: t\n        });\n      }, this);\n    },\n    save: function () {\n      this._map.fire(L.Draw.Event.DELETED, {\n        layers: this._deletedLayers\n      });\n    },\n    removeAllLayers: function () {\n      this._deletableLayers.eachLayer(function (t) {\n        this._removeLayer({\n          layer: t\n        });\n      }, this), this.save();\n    },\n    _enableLayerDelete: function (t) {\n      (t.layer || t.target || t).on(\"click\", this._removeLayer, this);\n    },\n    _disableLayerDelete: function (t) {\n      var e = t.layer || t.target || t;\n      e.off(\"click\", this._removeLayer, this), this._deletedLayers.removeLayer(e);\n    },\n    _removeLayer: function (t) {\n      var e = t.layer || t.target || t;\n      this._deletableLayers.removeLayer(e), this._deletedLayers.addLayer(e), e.fire(\"deleted\");\n    },\n    _onMouseMove: function (t) {\n      this._tooltip.updatePosition(t.latlng);\n    },\n    _hasAvailableLayers: function () {\n      return 0 !== this._deletableLayers.getLayers().length;\n    }\n  });\n}(window, document);", "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@bluehalo/ngx-leaflet';\nimport { LeafletDirectiveWrapper, LeafletUtil, LeafletModule } from '@bluehalo/ngx-leaflet';\nimport 'leaflet-draw';\nimport { drawLocal, Control, Draw } from 'leaflet';\nclass LeafletDrawUtil {\n  /**\n   * Deep copy the source object into the dest object. Will only copy literal values.\n   * @param dest\n   * @param src\n   */\n  static deepLiteralCopy(dest, src) {\n    const toReturn = dest;\n    if (null != src) {\n      for (const k in src) {\n        if (src.hasOwnProperty(k)) {\n          if (typeof src[k] === 'string' || src[k] instanceof String) {\n            toReturn[k] = src[k];\n          } else {\n            this.deepLiteralCopy(toReturn[k], src[k]);\n          }\n        }\n      }\n    }\n    return toReturn;\n  }\n}\nclass LeafletDrawDirective {\n  constructor(leafletDirective, zone) {\n    this.zone = zone;\n    this.drawOptions = null;\n    // Using 'any' here to avoid duplicating the DrawLocal interface with a bunch of optional properties\n    this.drawLocal = null;\n    // Configure callback function for the map\n    this.drawReady = new EventEmitter();\n    // Draw Events\n    this.onDrawCreated = new EventEmitter();\n    this.onDrawEdited = new EventEmitter();\n    this.onDrawDeleted = new EventEmitter();\n    this.onDrawStart = new EventEmitter();\n    this.onDrawStop = new EventEmitter();\n    this.onDrawVertex = new EventEmitter();\n    this.onDrawEditStart = new EventEmitter();\n    this.onDrawEditMove = new EventEmitter();\n    this.onDrawEditResize = new EventEmitter();\n    this.onDrawEditVertex = new EventEmitter();\n    this.onDrawEditStop = new EventEmitter();\n    this.onDrawDeleteStart = new EventEmitter();\n    this.onDrawDeleteStop = new EventEmitter();\n    this.onDrawToolbarOpened = new EventEmitter();\n    this.onDrawToolbarClosed = new EventEmitter();\n    this.onDrawMarkerContext = new EventEmitter();\n    this.leafletDirective = new LeafletDirectiveWrapper(leafletDirective);\n  }\n  ngOnInit() {\n    this.leafletDirective.init();\n    // Configure localization options\n    if (null != this.drawLocal) {\n      LeafletDrawUtil.deepLiteralCopy(drawLocal, this.drawLocal);\n    }\n    // Create the control\n    this.drawControl = new Control.Draw(this.drawOptions);\n    // Add the control to the map\n    this.leafletDirective.getMap().addControl(this.drawControl);\n    // Register the main handler for events coming from the draw plugin\n    const map = this.leafletDirective.getMap();\n    // add draw event pass throughs\n    map.on(Draw.Event.CREATED, e => LeafletUtil.handleEvent(this.zone, this.onDrawCreated, e));\n    map.on(Draw.Event.EDITED, e => LeafletUtil.handleEvent(this.zone, this.onDrawEdited, e));\n    map.on(Draw.Event.DELETED, e => LeafletUtil.handleEvent(this.zone, this.onDrawDeleted, e));\n    map.on(Draw.Event.DRAWSTART, e => LeafletUtil.handleEvent(this.zone, this.onDrawStart, e));\n    map.on(Draw.Event.DRAWSTOP, e => LeafletUtil.handleEvent(this.zone, this.onDrawStop, e));\n    map.on(Draw.Event.EDITSTART, e => LeafletUtil.handleEvent(this.zone, this.onDrawEditStart, e));\n    map.on(Draw.Event.EDITMOVE, e => LeafletUtil.handleEvent(this.zone, this.onDrawEditMove, e));\n    map.on(Draw.Event.EDITRESIZE, e => LeafletUtil.handleEvent(this.zone, this.onDrawEditResize, e));\n    map.on(Draw.Event.EDITVERTEX, e => LeafletUtil.handleEvent(this.zone, this.onDrawEditVertex, e));\n    map.on(Draw.Event.EDITSTOP, e => LeafletUtil.handleEvent(this.zone, this.onDrawEditStop, e));\n    map.on(Draw.Event.DELETESTART, e => LeafletUtil.handleEvent(this.zone, this.onDrawDeleteStart, e));\n    map.on(Draw.Event.DELETESTOP, e => LeafletUtil.handleEvent(this.zone, this.onDrawDeleteStop, e));\n    map.on(Draw.Event.TOOLBAROPENED, e => LeafletUtil.handleEvent(this.zone, this.onDrawToolbarOpened, e));\n    map.on(Draw.Event.TOOLBARCLOSED, e => LeafletUtil.handleEvent(this.zone, this.onDrawToolbarClosed, e));\n    // Notify others that the draw control has been created\n    this.drawReady.emit(this.drawControl);\n  }\n  ngOnDestroy() {\n    this.leafletDirective.getMap().removeControl(this.drawControl);\n  }\n  getDrawControl() {\n    return this.drawControl;\n  }\n  static {\n    this.ɵfac = function LeafletDrawDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletDrawDirective)(i0.ɵɵdirectiveInject(i1.LeafletDirective), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LeafletDrawDirective,\n      selectors: [[\"\", \"leafletDraw\", \"\"]],\n      inputs: {\n        drawOptions: [0, \"leafletDrawOptions\", \"drawOptions\"],\n        drawLocal: [0, \"leafletDrawLocal\", \"drawLocal\"]\n      },\n      outputs: {\n        drawReady: \"leafletDrawReady\",\n        onDrawCreated: \"leafletDrawCreated\",\n        onDrawEdited: \"leafletDrawEdited\",\n        onDrawDeleted: \"leafletDrawDeleted\",\n        onDrawStart: \"leafletDrawStart\",\n        onDrawStop: \"leafletDrawStop\",\n        onDrawVertex: \"leafletDrawVertex\",\n        onDrawEditStart: \"leafletDrawEditStart\",\n        onDrawEditMove: \"leafletDrawEditMove\",\n        onDrawEditResize: \"leafletDrawEditResize\",\n        onDrawEditVertex: \"leafletDrawEditVertex\",\n        onDrawEditStop: \"leafletDrawEditStop\",\n        onDrawDeleteStart: \"leafletDrawDeleteStart\",\n        onDrawDeleteStop: \"leafletDrawDeleteStop\",\n        onDrawToolbarOpened: \"leafletDrawToolbarOpened\",\n        onDrawToolbarClosed: \"leafletDrawToolbarClosed\",\n        onDrawMarkerContext: \"leafletDrawMarkerContext\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletDrawDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[leafletDraw]',\n      standalone: false\n    }]\n  }], () => [{\n    type: i1.LeafletDirective\n  }, {\n    type: i0.NgZone\n  }], {\n    drawOptions: [{\n      type: Input,\n      args: ['leafletDrawOptions']\n    }],\n    drawLocal: [{\n      type: Input,\n      args: ['leafletDrawLocal']\n    }],\n    drawReady: [{\n      type: Output,\n      args: ['leafletDrawReady']\n    }],\n    onDrawCreated: [{\n      type: Output,\n      args: ['leafletDrawCreated']\n    }],\n    onDrawEdited: [{\n      type: Output,\n      args: ['leafletDrawEdited']\n    }],\n    onDrawDeleted: [{\n      type: Output,\n      args: ['leafletDrawDeleted']\n    }],\n    onDrawStart: [{\n      type: Output,\n      args: ['leafletDrawStart']\n    }],\n    onDrawStop: [{\n      type: Output,\n      args: ['leafletDrawStop']\n    }],\n    onDrawVertex: [{\n      type: Output,\n      args: ['leafletDrawVertex']\n    }],\n    onDrawEditStart: [{\n      type: Output,\n      args: ['leafletDrawEditStart']\n    }],\n    onDrawEditMove: [{\n      type: Output,\n      args: ['leafletDrawEditMove']\n    }],\n    onDrawEditResize: [{\n      type: Output,\n      args: ['leafletDrawEditResize']\n    }],\n    onDrawEditVertex: [{\n      type: Output,\n      args: ['leafletDrawEditVertex']\n    }],\n    onDrawEditStop: [{\n      type: Output,\n      args: ['leafletDrawEditStop']\n    }],\n    onDrawDeleteStart: [{\n      type: Output,\n      args: ['leafletDrawDeleteStart']\n    }],\n    onDrawDeleteStop: [{\n      type: Output,\n      args: ['leafletDrawDeleteStop']\n    }],\n    onDrawToolbarOpened: [{\n      type: Output,\n      args: ['leafletDrawToolbarOpened']\n    }],\n    onDrawToolbarClosed: [{\n      type: Output,\n      args: ['leafletDrawToolbarClosed']\n    }],\n    onDrawMarkerContext: [{\n      type: Output,\n      args: ['leafletDrawMarkerContext']\n    }]\n  });\n})();\nclass LeafletDrawModule {\n  static {\n    this.ɵfac = function LeafletDrawModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletDrawModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LeafletDrawModule,\n      declarations: [LeafletDrawDirective],\n      imports: [LeafletModule],\n      exports: [LeafletDrawDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [LeafletModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletDrawModule, [{\n    type: NgModule,\n    args: [{\n      imports: [LeafletModule],\n      exports: [LeafletDrawDirective],\n      declarations: [LeafletDrawDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LeafletDrawDirective, LeafletDrawModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,CAAC,SAAU,GAAG,GAAG,GAAG;AAClB,WAAS,EAAEA,IAAGC,IAAG;AACf,YAAQD,KAAIA,GAAE,kBAAkB,CAACA,GAAE,UAAU,SAASC,EAAC,IAAG;AAC1D,WAAOD;AAAA,EACT;AACA,IAAE,cAAc,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,YAAY;AAAA,IAClD,MAAM;AAAA,MACJ,SAAS;AAAA,QACP,SAAS;AAAA,UACP,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,MAAM;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,SAAS;AAAA,UACP,UAAU;AAAA,UACV,SAAS;AAAA,UACT,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,UACN,SAAS;AAAA,YACP,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,YACP,OAAO;AAAA,YACP,MAAM;AAAA,YACN,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,YACP,OAAO;AAAA,YACP,MAAM;AAAA,YACN,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,YACP,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,aAAa;AAAA,UACX,SAAS;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,SAAS;AAAA,QACP,SAAS;AAAA,UACP,MAAM;AAAA,YACJ,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,YACN,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,UACA,UAAU;AAAA,YACR,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,UACd,QAAQ;AAAA,UACR,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,MAAM;AAAA,UACJ,SAAS;AAAA,YACP,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,YACP,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,EAAE,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,MAAM,UAAU,gBAAgB,EAAE,KAAK,MAAM,SAAS,eAAe,EAAE,KAAK,MAAM,UAAU,gBAAgB,EAAE,KAAK,MAAM,YAAY,kBAAkB,EAAE,KAAK,MAAM,WAAW,iBAAiB,EAAE,KAAK,MAAM,aAAa,mBAAmB,EAAE,KAAK,MAAM,YAAY,kBAAkB,EAAE,KAAK,MAAM,WAAW,iBAAiB,EAAE,KAAK,MAAM,aAAa,mBAAmB,EAAE,KAAK,MAAM,aAAa,mBAAmB,EAAE,KAAK,MAAM,WAAW,iBAAiB,EAAE,KAAK,MAAM,cAAc,oBAAoB,EAAE,KAAK,MAAM,aAAa,mBAAmB,EAAE,KAAK,MAAM,gBAAgB,sBAAsB,EAAE,KAAK,MAAM,gBAAgB,sBAAsB,EAAE,KAAK,MAAM,gBAAgB,sBAAsB,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE,QAAQ,OAAO;AAAA,IAC7wB,YAAY,SAAUA,IAAGC,IAAG;AAC1B,WAAK,OAAOD,IAAG,KAAK,aAAaA,GAAE,YAAY,KAAK,eAAeA,GAAE,OAAO,aAAa,KAAK,aAAaA,GAAE,OAAO,WAAWC,MAAKA,GAAE,iBAAiBA,GAAE,eAAe,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,QAAQ,cAAcA,GAAE,YAAY,IAAI,EAAE,WAAW,MAAMA,EAAC;AAC3P,UAAIC,KAAI,EAAE,QAAQ,MAAM,GAAG;AAC3B,YAAM,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,QAAQ,QAAQ,EAAE,QAAQ,SAAS,IAAI,EAAE,KAAK,QAAQ,QAAQ,EAAE,MAAM,MAAM;AAAA,IAC3I;AAAA,IACA,QAAQ,WAAY;AAClB,WAAK,aAAa,EAAE,QAAQ,UAAU,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK,WAAW;AAAA,QAC5E,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,WAAW;AAAA,QACzC,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,aAAa,EAAE,QAAQ,UAAU,QAAQ,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,UAAU;AAAA,QAC9F,WAAW,KAAK;AAAA,MAClB,CAAC,GAAG,KAAK,KAAK,YAAY;AAAA,QACxB,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,IACA,UAAU,WAAY;AACpB,UAAIF,KAAI,KAAK;AACb,MAAAA,OAAM,EAAE,QAAQ,qBAAqB,GAAGA,GAAE,aAAa,EAAE,MAAM,GAAG,KAAK,WAAW,IAAI,EAAE,KAAK,QAAQ,KAAK,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,SAAS,KAAK,gBAAgB,IAAI;AAAA,IACpL;AAAA,IACA,aAAa,WAAY;AACvB,WAAK,SAAS,EAAE,QAAQ,oBAAoB,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK,WAAW,MAAM,EAAE,SAAS,IAAI,KAAK,YAAY,SAAS,KAAK,gBAAgB,IAAI;AAAA,IAClK;AAAA,IACA,YAAY,SAAUA,IAAG;AACvB,QAAE,WAAW,MAAMA,EAAC;AAAA,IACtB;AAAA,IACA,mBAAmB,SAAUA,IAAG;AAC9B,WAAK,KAAK,KAAK,EAAE,KAAK,MAAM,SAAS;AAAA,QACnC,OAAOA;AAAA,QACP,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IACA,gBAAgB,SAAUA,IAAG;AAC3B,aAAOA,GAAE,YAAY,KAAK,KAAK,KAAK,iBAAiB;AAAA,QACnD,WAAW,KAAK;AAAA,MAClB,CAAC,GAAG,KAAK,QAAQ;AAAA,IACnB;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,WAAW,EAAE,KAAK,QAAQ,OAAO;AAAA,IAC1C,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,MAAM,EAAE;AAAA,IACR,SAAS;AAAA,MACP,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,WAAW;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,MAAM,IAAI,EAAE,QAAQ;AAAA,QAClB,UAAU,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,QAC1B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,WAAW,IAAI,EAAE,QAAQ;AAAA,QACvB,UAAU,IAAI,EAAE,MAAM,IAAI,EAAE;AAAA,QAC5B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,IACA,YAAY,SAAUA,IAAGC,IAAG;AAC1B,QAAE,QAAQ,UAAU,KAAK,QAAQ,OAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,UAAU,UAAU,EAAE,UAAU,KAAK,SAAS,SAAS,OAAOA,MAAKA,GAAE,cAAcA,GAAE,YAAY,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,QAAQ,WAAWA,GAAE,SAAS,IAAI,KAAK,OAAO,EAAE,KAAK,SAAS,MAAM,EAAE,KAAK,QAAQ,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC;AAAA,IAC/T;AAAA,IACA,UAAU,WAAY;AACpB,QAAE,KAAK,QAAQ,UAAU,SAAS,KAAK,IAAI,GAAG,KAAK,SAAS,KAAK,WAAW,CAAC,GAAG,KAAK,eAAe,IAAI,EAAE,WAAW,GAAG,KAAK,KAAK,SAAS,KAAK,YAAY,GAAG,KAAK,QAAQ,IAAI,EAAE,SAAS,CAAC,GAAG,KAAK,QAAQ,YAAY,GAAG,KAAK,SAAS,cAAc,KAAK,gBAAgB,CAAC,GAAG,KAAK,iBAAiB,KAAK,eAAe,EAAE,OAAO,KAAK,KAAK,UAAU,GAAG;AAAA,QACxV,MAAM,EAAE,QAAQ;AAAA,UACd,WAAW;AAAA,UACX,YAAY,CAAC,IAAI,EAAE;AAAA,UACnB,UAAU,CAAC,IAAI,EAAE;AAAA,QACnB,CAAC;AAAA,QACD,SAAS;AAAA,QACT,cAAc,KAAK,QAAQ;AAAA,MAC7B,CAAC,IAAI,KAAK,aAAa,GAAG,YAAY,KAAK,aAAa,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,WAAW,KAAK,YAAY,IAAI,EAAE,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG,WAAW,KAAK,YAAY,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,oBAAoB,KAAK,YAAY,IAAI,EAAE,GAAG,cAAc,KAAK,UAAU,IAAI,EAAE,GAAG,WAAW,KAAK,YAAY,IAAI;AAAA,IACxZ;AAAA,IACA,aAAa,WAAY;AACvB,QAAE,KAAK,QAAQ,UAAU,YAAY,KAAK,IAAI,GAAG,KAAK,uBAAuB,GAAG,KAAK,cAAc,GAAG,KAAK,KAAK,YAAY,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,OAAO,KAAK,UAAU,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG,OAAO,KAAK,OAAO,KAAK,aAAa,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,YAAY,KAAK,aAAa,IAAI,EAAE,IAAI,WAAW,KAAK,YAAY,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,GAAG,KAAK,KAAK,YAAY,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,KAAK,aAAa,GAAG,KAAK,KAAK,IAAI,WAAW,KAAK,YAAY,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,oBAAoB,KAAK,YAAY,IAAI,EAAE,IAAI,WAAW,KAAK,YAAY,IAAI,EAAE,IAAI,cAAc,KAAK,UAAU,IAAI,EAAE,IAAI,SAAS,KAAK,UAAU,IAAI;AAAA,IAC5vB;AAAA,IACA,kBAAkB,WAAY;AAC5B,UAAI,EAAE,KAAK,SAAS,UAAU,IAAI;AAChC,YAAID,KAAI,KAAK,SAAS,IAAI,GACxBC,KAAI,KAAK,OACTC,KAAID,GAAE,WAAW,GACjBE,KAAID,GAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AACvB,aAAK,MAAM,WAAWA,EAAC,GAAG,KAAK,aAAa,YAAYF,EAAC,GAAGC,GAAE,WAAW,EAAE,SAAS,KAAK,KAAK,KAAK,YAAYA,EAAC,GAAG,KAAK,eAAeE,IAAG,KAAE;AAAA,MAC9I;AAAA,IACF;AAAA,IACA,WAAW,SAAUH,IAAG;AACtB,UAAI,KAAK,SAAS,UAAU,KAAK,CAAC,KAAK,QAAQ,qBAAqB,KAAK,MAAM,oBAAoBA,EAAC,EAAG,QAAO,KAAK,KAAK,kBAAkB;AAC1I,WAAK,eAAe,KAAK,kBAAkB,GAAG,KAAK,SAAS,KAAK,KAAK,cAAcA,EAAC,CAAC,GAAG,KAAK,MAAM,UAAUA,EAAC,GAAG,MAAM,KAAK,MAAM,WAAW,EAAE,UAAU,KAAK,KAAK,SAAS,KAAK,KAAK,GAAG,KAAK,eAAeA,IAAG,IAAE;AAAA,IACrN;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,SAAS,UAAU,MAAM,KAAK,kBAAkB,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,cAAc,KAAK,OAAO;AAAA,IACjH;AAAA,IACA,cAAc,WAAY;AACxB,UAAIA,KAAI,KAAK,MAAM,gBAAgB,KAAK,MAAM,cAAc,IAAI,KAAK,MAAM,WAAW,GACpFC,KAAI,KAAK,MAAM,oBAAoBD,GAAEA,GAAE,SAAS,CAAC,CAAC;AACpD,UAAI,CAAC,KAAK,QAAQ,qBAAqBC,MAAK,CAAC,KAAK,cAAc,EAAG,QAAO,KAAK,KAAK,kBAAkB;AACtG,WAAK,kBAAkB,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,cAAc,KAAK,OAAO;AAAA,IACnF;AAAA,IACA,eAAe,WAAY;AACzB,aAAO;AAAA,IACT;AAAA,IACA,YAAY,WAAY;AACtB,eAAS,KAAK,YAAY,KAAK,aAAa;AAAA,IAC9C;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAIC,KAAI,KAAK,KAAK,uBAAuBD,GAAE,aAAa,GACtDE,KAAI,KAAK,KAAK,mBAAmBD,EAAC;AACpC,WAAK,iBAAiBC,IAAG,KAAK,eAAeA,EAAC,GAAG,KAAK,aAAaD,EAAC,GAAG,KAAK,aAAa,UAAUC,EAAC,GAAG,EAAE,SAAS,eAAeF,GAAE,aAAa;AAAA,IAClJ;AAAA,IACA,gBAAgB,SAAUA,IAAGC,IAAG;AAC9B,WAAK,KAAK,KAAK,EAAE,KAAK,MAAM,YAAY;AAAA,QACtC,QAAQ,KAAK;AAAA,MACf,CAAC,GAAG,KAAK,qBAAqB,GAAG,KAAK,sBAAsBD,IAAGC,EAAC,GAAG,KAAK,aAAa,GAAG,KAAK,eAAe;AAAA,IAC9G;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,iBAAiB,CAAC,KAAK,iBAAiB;AACvE,aAAK,aAAaA,EAAC,GAAG,KAAK,gBAAgB,MAAI,KAAK,mBAAmB;AACvE,YAAIC,KAAID,GAAE,eACRE,KAAID,GAAE,SACNE,KAAIF,GAAE;AACR,aAAK,YAAY,KAAK,MAAMC,IAAGC,EAAC;AAAA,MAClC;AAAA,IACF;AAAA,IACA,aAAa,SAAUH,IAAGC,IAAG;AAC3B,WAAK,mBAAmB,EAAE,MAAMD,IAAGC,EAAC;AAAA,IACtC;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,UAAIC,KAAID,GAAE,eACRE,KAAID,GAAE,SACNE,KAAIF,GAAE;AACR,WAAK,UAAU,KAAK,MAAMC,IAAGC,IAAGH,EAAC,GAAG,KAAK,gBAAgB;AAAA,IAC3D;AAAA,IACA,WAAW,SAAUC,IAAGC,IAAGC,IAAG;AAC5B,UAAI,KAAK,kBAAkB;AACzB,YAAI,IAAI,EAAE,MAAMF,IAAGC,EAAC,EAAE,WAAW,KAAK,gBAAgB,GACpD,IAAI,KAAK,yBAAyBC,GAAE,MAAM;AAC5C,aAAK,QAAQ,YAAY,KAAK,KAAK,QAAQ,aAAa,KAAK,SAAS,SAAS,KAAK,KAAK,UAAUA,GAAE,MAAM,GAAG,KAAK,aAAa,KAAK,IAAI,MAAM,EAAE,QAAQ,QAAQ,KAAK,aAAa,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE,oBAAoB,MAAM,KAAK,UAAUA,GAAE,MAAM,GAAG,KAAK,kBAAkB;AAAA,MACzR;AACA,WAAK,mBAAmB;AAAA,IAC1B;AAAA,IACA,UAAU,SAAUH,IAAG;AACrB,UAAIC,IACFC,IACAC,KAAIH,GAAE;AACR,OAACG,GAAE,WAAW,CAACA,GAAE,QAAQ,CAAC,KAAK,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,oBAAoBF,KAAIE,GAAE,QAAQ,CAAC,EAAE,SAASD,KAAIC,GAAE,QAAQ,CAAC,EAAE,SAAS,KAAK,mBAAmB,GAAG,KAAK,gBAAgB,MAAI,KAAK,YAAY,KAAK,MAAMF,IAAGC,EAAC,GAAG,KAAK,UAAU,KAAK,MAAMD,IAAGC,IAAGF,EAAC,GAAG,KAAK,gBAAgB,OAAO,KAAK,gBAAgB;AAAA,IACtU;AAAA,IACA,aAAa,WAAY;AACvB,WAAK,YAAY,KAAK,SAAS,YAAY,KAAK,KAAK,QAAQ;AAAA,IAC/D;AAAA,IACA,0BAA0B,SAAUA,IAAG;AACrC,UAAIC;AACJ,UAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,YAAIC;AACJ,YAAI,KAAK,SAAS,EAAE,KAAK,SAAS,KAAM,CAAAA,KAAI,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAAA,aAAO;AACvF,cAAI,KAAK,SAAS,EAAE,KAAK,QAAQ,KAAM,QAAO,IAAI;AAClD,UAAAA,KAAI,KAAK,SAAS,CAAC;AAAA,QACrB;AACA,YAAIC,KAAI,KAAK,KAAK,uBAAuBD,GAAE,UAAU,CAAC,GACpD,IAAI,IAAI,EAAE,OAAOF,IAAG;AAAA,UAClB,MAAM,KAAK,QAAQ;AAAA,UACnB,cAAc,IAAI,KAAK,QAAQ;AAAA,QACjC,CAAC,GACD,IAAI,KAAK,KAAK,uBAAuB,EAAE,UAAU,CAAC;AACpD,QAAAC,KAAIE,GAAE,WAAW,CAAC;AAAA,MACpB,MAAO,CAAAF,KAAI,IAAI;AACf,aAAOA;AAAA,IACT;AAAA,IACA,sBAAsB,WAAY;AAChC,UAAID,KAAI,KAAK,SAAS;AACtB,MAAAA,KAAI,KAAK,KAAK,SAASA,KAAI,CAAC,EAAE,GAAG,SAAS,KAAK,cAAc,IAAI,GAAGA,KAAI,KAAK,KAAK,SAASA,KAAI,CAAC,EAAE,IAAI,SAAS,KAAK,cAAc,IAAI;AAAA,IACxI;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,UAAIC,KAAI,IAAI,EAAE,OAAOD,IAAG;AAAA,QACtB,MAAM,KAAK,QAAQ;AAAA,QACnB,cAAc,IAAI,KAAK,QAAQ;AAAA,MACjC,CAAC;AACD,aAAO,KAAK,aAAa,SAASC,EAAC,GAAGA;AAAA,IACxC;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAIC,KAAI,KAAK,WAAW,KAAK,SAAS,SAAS;AAC/C,MAAAA,KAAI,MAAMD,KAAIA,MAAK,KAAK,KAAK,mBAAmB,KAAK,cAAc,GAAG,KAAK,aAAa,GAAG,KAAK,WAAW,KAAK,KAAK,mBAAmB,KAAK,SAASC,KAAI,CAAC,EAAE,UAAU,CAAC,GAAGD,EAAC;AAAA,IAC9K;AAAA,IACA,gBAAgB,SAAUA,IAAG;AAC3B,UAAIC,KAAI,KAAK,gBAAgB;AAC7B,MAAAD,MAAK,KAAK,SAAS,eAAeA,EAAC,GAAG,KAAK,eAAe,KAAK,SAAS,cAAcC,EAAC;AAAA,IACzF;AAAA,IACA,YAAY,SAAUD,IAAGC,IAAG;AAC1B,UAAIC,IACFC,IACA,GACA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,IAAIF,GAAE,IAAID,GAAE,GAAG,CAAC,IAAI,KAAK,IAAIC,GAAE,IAAID,GAAE,GAAG,CAAC,CAAC,CAAC,GACzE,IAAI,KAAK,QAAQ,mBACjB,IAAI,KAAK,QAAQ,oBACjB,IAAI,IAAI,IAAI,IAAI,IAAI;AACtB,WAAK,KAAK,qBAAqB,KAAK,mBAAmB,EAAE,QAAQ,OAAO,OAAO,uBAAuB,KAAK,YAAY,IAAI,IAAI,GAAG,KAAK,KAAK,QAAQ,kBAAmB,CAAAE,KAAI,IAAI,GAAGC,KAAI;AAAA,QACpL,GAAG,KAAK,MAAMH,GAAE,KAAK,IAAIE,MAAKA,KAAID,GAAE,CAAC;AAAA,QACrC,GAAG,KAAK,MAAMD,GAAE,KAAK,IAAIE,MAAKA,KAAID,GAAE,CAAC;AAAA,MACvC,GAAG,IAAI,EAAE,QAAQ,OAAO,OAAO,2BAA2B,KAAK,gBAAgB,GAAG,EAAE,MAAM,kBAAkB,KAAK,cAAc,KAAK,QAAQ,UAAU,QAAQ,KAAK,QAAQ,aAAa,OAAO,EAAE,QAAQ,YAAY,GAAGE,EAAC;AAAA,IAC3N;AAAA,IACA,mBAAmB,SAAUH,IAAG;AAC9B,UAAI,KAAK,iBAAkB,UAASC,KAAI,GAAGC,KAAI,KAAK,iBAAiB,WAAW,QAAQD,KAAIC,IAAGD,KAAK,MAAK,iBAAiB,WAAWA,EAAC,EAAE,MAAM,kBAAkBD;AAAA,IAClK;AAAA,IACA,cAAc,WAAY;AACxB,UAAI,KAAK,iBAAkB,QAAO,KAAK,iBAAiB,aAAa,MAAK,iBAAiB,YAAY,KAAK,iBAAiB,UAAU;AAAA,IACzI;AAAA,IACA,iBAAiB,WAAY;AAC3B,UAAIA,IACFC,IACAC,KAAI,KAAK,QAAQ;AACnB,aAAO,MAAM,KAAK,SAAS,SAASF,KAAI;AAAA,QACtC,MAAM,EAAE,UAAU,KAAK,SAAS,SAAS,QAAQ;AAAA,MACnD,KAAKC,KAAIC,KAAI,KAAK,sBAAsB,IAAI,IAAIF,KAAI,MAAM,KAAK,SAAS,SAAS;AAAA,QAC/E,MAAM,EAAE,UAAU,KAAK,SAAS,SAAS,QAAQ;AAAA,QACjD,SAASC;AAAA,MACX,IAAI;AAAA,QACF,MAAM,EAAE,UAAU,KAAK,SAAS,SAAS,QAAQ;AAAA,QACjD,SAASA;AAAA,MACX,IAAID;AAAA,IACN;AAAA,IACA,uBAAuB,SAAUA,IAAGC,IAAG;AACrC,UAAIC,IACFC,IACA,IAAI,KAAK,SAAS;AACpB,YAAM,KAAK,SAAS,SAAS,KAAK,2BAA2B,KAAKD,KAAI,KAAKD,KAAI,IAAI,IAAIE,KAAI,EAAE,aAAa,aAAa,IAAIH,GAAE,WAAW,KAAK,SAASE,EAAC,EAAE,UAAU,CAAC,KAAK,KAAK,QAAQ,UAAU,KAAK,KAAK,KAAK,SAASF,IAAG,KAAK,SAASE,EAAC,EAAE,UAAU,CAAC,KAAK,KAAK,QAAQ,UAAU,IAAI,KAAK,4BAA4BC,MAAKF,KAAI,IAAI;AAAA,IACvU;AAAA,IACA,uBAAuB,WAAY;AACjC,UAAID,IACFC,KAAI,KAAK,gBACTC,KAAI,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,EAAE,UAAU;AACxD,aAAOF,KAAI,EAAE,aAAa,aAAa,IAAIE,MAAKD,MAAKA,GAAE,aAAa,KAAK,2BAA2BA,GAAE,WAAWC,EAAC,KAAK,KAAK,QAAQ,UAAU,KAAK,KAAK,4BAA4B,IAAIA,MAAKD,KAAI,KAAK,2BAA2B,KAAK,KAAK,SAASA,IAAGC,EAAC,KAAK,KAAK,QAAQ,UAAU,KAAK,KAAK,4BAA4B,GAAG,EAAE,aAAa,iBAAiBF,IAAG,KAAK,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ,QAAQ,KAAK,QAAQ,SAAS;AAAA,IACrb;AAAA,IACA,mBAAmB,WAAY;AAC7B,WAAK,cAAc,MAAI,KAAK,SAAS,YAAY,EAAE,cAAc;AAAA,QAC/D,MAAM,KAAK,QAAQ,UAAU;AAAA,MAC/B,CAAC,GAAG,KAAK,kBAAkB,KAAK,QAAQ,UAAU,KAAK,GAAG,KAAK,MAAM,SAAS;AAAA,QAC5E,OAAO,KAAK,QAAQ,UAAU;AAAA,MAChC,CAAC,GAAG,KAAK,uBAAuB,GAAG,KAAK,oBAAoB,WAAW,EAAE,KAAK,KAAK,KAAK,mBAAmB,IAAI,GAAG,KAAK,QAAQ,UAAU,OAAO;AAAA,IAClJ;AAAA,IACA,mBAAmB,WAAY;AAC7B,WAAK,cAAc,OAAI,KAAK,uBAAuB,GAAG,KAAK,SAAS,YAAY,EAAE,cAAc,KAAK,gBAAgB,CAAC,GAAG,KAAK,kBAAkB,KAAK,QAAQ,aAAa,KAAK,GAAG,KAAK,MAAM,SAAS;AAAA,QACpM,OAAO,KAAK,QAAQ,aAAa;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,IACA,wBAAwB,WAAY;AAClC,WAAK,sBAAsB,aAAa,KAAK,iBAAiB,GAAG,KAAK,oBAAoB;AAAA,IAC5F;AAAA,IACA,oBAAoB,WAAY;AAC9B,WAAK,kBAAkB;AAAA,IACzB;AAAA,IACA,mBAAmB,WAAY;AAC7B,iBAAW,WAAY;AACrB,aAAK,kBAAkB;AAAA,MACzB,EAAE,KAAK,IAAI,GAAG,EAAE;AAAA,IAClB;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,SAAS,SAAS,KAAK,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,EAAE,IAAI,SAAS,KAAK,cAAc,IAAI;AAAA,IAC1G;AAAA,IACA,mBAAmB,WAAY;AAC7B,UAAIA,KAAI,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,QAAQ,YAAY;AACxE,QAAE,KAAK,QAAQ,UAAU,kBAAkB,KAAK,MAAMA,EAAC;AAAA,IACzD;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE,KAAK,SAAS,OAAO;AAAA,IAC1C,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,MAAM,EAAE;AAAA,IACR,SAAS;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW,CAAC;AAAA,IACd;AAAA,IACA,YAAY,SAAUA,IAAGC,IAAG;AAC1B,QAAE,KAAK,SAAS,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC,GAAG,KAAK,OAAO,EAAE,KAAK,QAAQ;AAAA,IACpF;AAAA,IACA,sBAAsB,WAAY;AAChC,UAAID,KAAI,KAAK,SAAS;AACtB,YAAMA,MAAK,KAAK,SAAS,CAAC,EAAE,GAAG,SAAS,KAAK,cAAc,IAAI,GAAGA,KAAI,MAAM,KAAK,SAASA,KAAI,CAAC,EAAE,GAAG,YAAY,KAAK,cAAc,IAAI,GAAGA,KAAI,KAAK,KAAK,SAASA,KAAI,CAAC,EAAE,IAAI,YAAY,KAAK,cAAc,IAAI;AAAA,IACjN;AAAA,IACA,iBAAiB,WAAY;AAC3B,UAAIA,IAAGC;AACP,aAAO,MAAM,KAAK,SAAS,SAASD,KAAI,EAAE,UAAU,KAAK,SAAS,QAAQ,QAAQ,QAAQ,KAAK,SAAS,SAAS,KAAKA,KAAI,EAAE,UAAU,KAAK,SAAS,QAAQ,QAAQ,MAAMC,KAAI,KAAK,sBAAsB,MAAMD,KAAI,EAAE,UAAU,KAAK,SAAS,QAAQ,QAAQ,KAAKC,KAAI,KAAK,sBAAsB,IAAI;AAAA,QACnS,MAAMD;AAAA,QACN,SAASC;AAAA,MACX;AAAA,IACF;AAAA,IACA,uBAAuB,WAAY;AACjC,UAAID,KAAI,KAAK,OACXC,KAAI;AACN,aAAOD,MAAK,KAAK,QAAQ,cAAc,KAAK,QAAQ,eAAeC,KAAI,EAAE,KAAK,SAAS,UAAU,sBAAsB,KAAK,IAAI,IAAID,OAAMC,MAAK,SAAS,EAAE,aAAa,aAAaD,IAAG,KAAK,QAAQ,QAAQ,KAAK,QAAQ,SAAS,IAAIC,MAAK;AAAA,IAC7O;AAAA,IACA,eAAe,WAAY;AACzB,aAAO,KAAK,SAAS,UAAU;AAAA,IACjC;AAAA,IACA,gBAAgB,SAAUD,IAAGC,IAAG;AAC9B,UAAIC;AACJ,OAAC,KAAK,QAAQ,qBAAqB,KAAK,QAAQ,aAAaA,KAAI,KAAK,MAAM,WAAW,GAAG,KAAK,QAAQ,EAAE,aAAa,aAAaA,EAAC,IAAI,EAAE,KAAK,SAAS,UAAU,eAAe,KAAK,MAAMF,IAAGC,EAAC;AAAA,IAClM;AAAA,IACA,eAAe,WAAY;AACzB,UAAID,KAAI,KAAK,SAAS;AACtB,MAAAA,KAAI,MAAM,KAAK,SAAS,CAAC,EAAE,IAAI,SAAS,KAAK,cAAc,IAAI,GAAGA,KAAI,KAAK,KAAK,SAASA,KAAI,CAAC,EAAE,IAAI,YAAY,KAAK,cAAc,IAAI;AAAA,IACzI;AAAA,EACF,CAAC,GAAG,EAAE,cAAc,CAAC,GAAG,EAAE,KAAK,cAAc,EAAE,KAAK,QAAQ,OAAO;AAAA,IACjE,SAAS;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,YAAY,SAAUA,IAAGC,IAAG;AAC1B,WAAK,gBAAgB,EAAE,UAAU,KAAK,SAAS,YAAY,QAAQ,KAAK,EAAE,KAAK,QAAQ,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC;AAAA,IAC7H;AAAA,IACA,UAAU,WAAY;AACpB,QAAE,KAAK,QAAQ,UAAU,SAAS,KAAK,IAAI,GAAG,KAAK,SAAS,KAAK,gBAAgB,KAAK,KAAK,SAAS,QAAQ,GAAG,KAAK,iBAAiB,KAAK,KAAK,SAAS,QAAQ,GAAG,KAAK,WAAW,MAAM,SAAS,aAAa,KAAK,SAAS,cAAc;AAAA,QACzO,MAAM,KAAK;AAAA,MACb,CAAC,GAAG,KAAK,KAAK,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,cAAc,KAAK,cAAc,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,GAAG,EAAE,iBAAiB,cAAc,EAAE,SAAS,gBAAgB;AAAA,QAC9O,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,IACA,aAAa,WAAY;AACvB,QAAE,KAAK,QAAQ,UAAU,YAAY,KAAK,IAAI,GAAG,KAAK,SAAS,KAAK,iBAAiB,KAAK,KAAK,SAAS,OAAO,GAAG,KAAK,WAAW,MAAM,SAAS,IAAI,KAAK,KAAK,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,cAAc,KAAK,cAAc,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,GAAG,EAAE,SAAS,IAAI,GAAG,WAAW,KAAK,YAAY,IAAI,GAAG,EAAE,SAAS,IAAI,GAAG,YAAY,KAAK,YAAY,IAAI,GAAG,EAAE,oBAAoB,cAAc,EAAE,SAAS,cAAc,GAAG,KAAK,WAAW,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,OAAO,KAAK,UAAU,KAAK,aAAa;AAAA,IACllB;AAAA,IACA,iBAAiB,WAAY;AAC3B,aAAO;AAAA,QACL,MAAM,KAAK;AAAA,MACb;AAAA,IACF;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,WAAK,aAAa,MAAI,KAAK,eAAeA,GAAE,QAAQ,EAAE,SAAS,GAAG,GAAG,WAAW,KAAK,YAAY,IAAI,EAAE,GAAG,GAAG,YAAY,KAAK,YAAY,IAAI,EAAE,eAAeA,GAAE,aAAa;AAAA,IAChL;AAAA,IACA,cAAc,SAAUA,IAAG;AACzB,UAAIC,KAAID,GAAE;AACV,WAAK,SAAS,eAAeC,EAAC,GAAG,KAAK,eAAe,KAAK,SAAS,cAAc,KAAK,gBAAgB,CAAC,GAAG,KAAK,WAAWA,EAAC;AAAA,IAC7H;AAAA,IACA,YAAY,WAAY;AACtB,WAAK,UAAU,KAAK,kBAAkB,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,cAAc,KAAK,OAAO;AAAA,IAClG;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,OAAO;AAAA,IAC/C,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW;AAAA,QACX,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,QAAQ;AAAA,IACV;AAAA,IACA,YAAY,SAAUD,IAAGC,IAAG;AAC1B,WAAK,OAAO,EAAE,KAAK,UAAU,MAAM,KAAK,oBAAoB,EAAE,UAAU,KAAK,SAAS,UAAU,QAAQ,OAAO,EAAE,KAAK,YAAY,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC;AAAA,IACxK;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,aAAa,KAAK,8BAA8B,OAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,KAAK,IAAI;AAAA,IACzG;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,4BAA6B,QAAO,MAAM,KAAK,8BAA8B;AACvG,WAAK,+BAA+B,CAAC,EAAEA,GAAE,QAAQ,cAAc,KAAK,EAAE,KAAK,YAAY,UAAU,WAAW,KAAK,IAAI;AAAA,IACvH;AAAA,IACA,YAAY,SAAUA,IAAG;AACvB,WAAK,SAAS,KAAK,OAAO,UAAU,IAAI,EAAE,aAAa,KAAK,cAAcA,EAAC,CAAC,KAAK,KAAK,SAAS,IAAI,EAAE,UAAU,IAAI,EAAE,aAAa,KAAK,cAAcA,EAAC,GAAG,KAAK,QAAQ,YAAY,GAAG,KAAK,KAAK,SAAS,KAAK,MAAM;AAAA,IACrN;AAAA,IACA,mBAAmB,WAAY;AAC7B,UAAIA,KAAI,IAAI,EAAE,UAAU,KAAK,OAAO,UAAU,GAAG,KAAK,QAAQ,YAAY;AAC1E,QAAE,KAAK,YAAY,UAAU,kBAAkB,KAAK,MAAMA,EAAC;AAAA,IAC7D;AAAA,IACA,iBAAiB,WAAY;AAC3B,UAAIA,IACFC,IACAC,IACAC,KAAI,EAAE,KAAK,YAAY,UAAU,gBAAgB,KAAK,IAAI,GAC1D,IAAI,KAAK,QACT,IAAI,KAAK,QAAQ;AACnB,aAAO,MAAMH,KAAI,KAAK,OAAO,gBAAgB,KAAK,OAAO,cAAc,IAAI,KAAK,OAAO,WAAW,GAAGC,KAAI,EAAE,aAAa,aAAaD,EAAC,GAAGE,KAAI,IAAI,EAAE,aAAa,aAAaD,IAAG,KAAK,QAAQ,MAAM,IAAI,KAAK;AAAA,QAC1M,MAAME,GAAE;AAAA,QACR,SAASD;AAAA,MACX;AAAA,IACF;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,QAAQ,OAAO;AAAA,IACxC,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,MAAM,IAAI,EAAE,KAAK,QAAQ;AAAA,MACzB,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB;AAAA,IACA,YAAY,SAAUF,IAAGC,IAAG;AAC1B,WAAK,OAAO,EAAE,KAAK,OAAO,MAAM,KAAK,oBAAoB,EAAE,UAAU,KAAK,SAAS,OAAO,QAAQ,OAAO,EAAE,KAAK,QAAQ,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC;AAAA,IAC9J;AAAA,IACA,UAAU,WAAY;AACpB,QAAE,KAAK,QAAQ,UAAU,SAAS,KAAK,IAAI,GAAG,KAAK,SAAS,KAAK,SAAS,cAAc;AAAA,QACtF,MAAM,KAAK;AAAA,MACb,CAAC,GAAG,KAAK,iBAAiB,KAAK,eAAe,EAAE,OAAO,KAAK,KAAK,UAAU,GAAG;AAAA,QAC5E,MAAM,EAAE,QAAQ;AAAA,UACd,WAAW;AAAA,UACX,YAAY,CAAC,IAAI,EAAE;AAAA,UACnB,UAAU,CAAC,IAAI,EAAE;AAAA,QACnB,CAAC;AAAA,QACD,SAAS;AAAA,QACT,cAAc,KAAK,QAAQ;AAAA,MAC7B,CAAC,IAAI,KAAK,aAAa,GAAG,SAAS,KAAK,UAAU,IAAI,EAAE,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG,aAAa,KAAK,cAAc,IAAI,GAAG,KAAK,KAAK,GAAG,SAAS,KAAK,UAAU,IAAI;AAAA,IACzK;AAAA,IACA,aAAa,WAAY;AACvB,QAAE,KAAK,QAAQ,UAAU,YAAY,KAAK,IAAI,GAAG,KAAK,SAAS,KAAK,KAAK,IAAI,SAAS,KAAK,UAAU,IAAI,EAAE,IAAI,SAAS,KAAK,UAAU,IAAI,GAAG,KAAK,YAAY,KAAK,QAAQ,IAAI,SAAS,KAAK,UAAU,IAAI,GAAG,KAAK,KAAK,YAAY,KAAK,OAAO,GAAG,OAAO,KAAK,UAAU,KAAK,aAAa,IAAI,SAAS,KAAK,UAAU,IAAI,GAAG,KAAK,KAAK,YAAY,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,KAAK,KAAK,IAAI,aAAa,KAAK,cAAc,IAAI;AAAA,IACvb;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAIC,KAAID,GAAE;AACV,WAAK,SAAS,eAAeC,EAAC,GAAG,KAAK,aAAa,UAAUA,EAAC,GAAG,KAAK,WAAWA,KAAI,KAAK,aAAa,UAAU,GAAG,KAAK,QAAQ,UAAUA,EAAC,MAAM,KAAK,UAAU,KAAK,cAAcA,EAAC,GAAG,KAAK,QAAQ,GAAG,SAAS,KAAK,UAAU,IAAI,GAAG,KAAK,KAAK,GAAG,SAAS,KAAK,UAAU,IAAI,EAAE,SAAS,KAAK,OAAO;AAAA,IACzS;AAAA,IACA,eAAe,SAAUD,IAAG;AAC1B,aAAO,IAAI,EAAE,OAAOA,IAAG;AAAA,QACrB,MAAM,KAAK,QAAQ;AAAA,QACnB,cAAc,KAAK,QAAQ;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,IACA,UAAU,WAAY;AACpB,WAAK,kBAAkB,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,cAAc,KAAK,OAAO;AAAA,IACnF;AAAA,IACA,UAAU,SAAUA,IAAG;AACrB,WAAK,aAAaA,EAAC,GAAG,KAAK,SAAS;AAAA,IACtC;AAAA,IACA,mBAAmB,WAAY;AAC7B,UAAIA,KAAI,IAAI,EAAE,OAAO,MAAM,KAAK,QAAQ,UAAU,GAAG;AAAA,QACnD,MAAM,KAAK,QAAQ;AAAA,MACrB,CAAC;AACD,QAAE,KAAK,QAAQ,UAAU,kBAAkB,KAAK,MAAMA,EAAC;AAAA,IACzD;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,eAAe,EAAE,KAAK,OAAO,OAAO;AAAA,IAC7C,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,MACX,cAAc;AAAA,IAChB;AAAA,IACA,YAAY,SAAUA,IAAGC,IAAG;AAC1B,WAAK,OAAO,EAAE,KAAK,aAAa,MAAM,KAAK,oBAAoB,EAAE,UAAU,KAAK,SAAS,aAAa,QAAQ,OAAO,EAAE,KAAK,QAAQ,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC;AAAA,IAC1K;AAAA,IACA,mBAAmB,WAAY;AAC7B,UAAID,KAAI,IAAI,EAAE,aAAa,KAAK,QAAQ,UAAU,GAAG,KAAK,OAAO;AACjE,QAAE,KAAK,QAAQ,UAAU,kBAAkB,KAAK,MAAMA,EAAC;AAAA,IACzD;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,aAAO,IAAI,EAAE,aAAaA,IAAG,KAAK,OAAO;AAAA,IAC3C;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,YAAY,OAAO;AAAA,IAC5C,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,IACA,YAAY,SAAUA,IAAGC,IAAG;AAC1B,WAAK,OAAO,EAAE,KAAK,OAAO,MAAM,KAAK,oBAAoB,EAAE,UAAU,KAAK,SAAS,OAAO,QAAQ,OAAO,EAAE,KAAK,YAAY,UAAU,WAAW,KAAK,MAAMD,IAAGC,EAAC;AAAA,IAClK;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,UAAI,EAAE,aAAa,aAAa,EAAG,KAAIC,KAAI,KAAK,aAAa,WAAWD,EAAC;AAAA,UAAO,KAAIC,KAAI,KAAK,KAAK,SAAS,KAAK,cAAcD,EAAC;AAC/H,WAAK,SAAS,KAAK,OAAO,UAAUC,EAAC,KAAK,KAAK,SAAS,IAAI,EAAE,OAAO,KAAK,cAAcA,IAAG,KAAK,QAAQ,YAAY,GAAG,KAAK,KAAK,SAAS,KAAK,MAAM;AAAA,IACvJ;AAAA,IACA,mBAAmB,WAAY;AAC7B,UAAID,KAAI,IAAI,EAAE,OAAO,KAAK,cAAc,KAAK,OAAO,UAAU,GAAG,KAAK,QAAQ,YAAY;AAC1F,QAAE,KAAK,YAAY,UAAU,kBAAkB,KAAK,MAAMA,EAAC;AAAA,IAC7D;AAAA,IACA,cAAc,SAAUA,IAAG;AACzB,UAAIC,IACFC,KAAIF,GAAE,QACNG,KAAI,KAAK,QAAQ,YACjB,IAAI,KAAK,QAAQ;AACnB,UAAI,KAAK,SAAS,eAAeD,EAAC,GAAG,KAAK,YAAY;AACpD,aAAK,WAAWA,EAAC,GAAGD,KAAI,KAAK,OAAO,UAAU,EAAE,QAAQ,CAAC;AACzD,YAAI,IAAI;AACR,QAAAE,OAAM,IAAI,EAAE,UAAU,KAAK,SAAS,OAAO,SAAS,OAAO,EAAE,aAAa,iBAAiBF,IAAG,GAAG,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,IAAI,KAAK,SAAS,cAAc;AAAA,UACrK,MAAM,KAAK;AAAA,UACX,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE,QAAQ,OAAO;AAAA,IAC1D,YAAY,SAAUD,IAAGC,IAAG;AAC1B,WAAK,UAAUD,IAAG,EAAE,WAAW,MAAMC,EAAC;AAAA,IACxC;AAAA,IACA,UAAU,WAAY;AACpB,UAAID,KAAI,KAAK;AACb,MAAAA,GAAE,SAAS,OAAO,GAAGA,GAAE,GAAG,WAAW,KAAK,YAAYA,EAAC,GAAG,KAAK,uBAAuB;AAAA,IACxF;AAAA,IACA,aAAa,WAAY;AACvB,UAAIA,KAAI,KAAK;AACb,MAAAA,GAAE,SAAS,QAAQ,GAAGA,GAAE,IAAI,WAAW,KAAK,YAAYA,EAAC,GAAG,KAAK,uBAAuB;AAAA,IAC1F;AAAA,IACA,YAAY,SAAUA,IAAG;AACvB,UAAIC,KAAID,GAAE;AACV,MAAAC,GAAE,SAAS,MAAI,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,UAAU;AAAA,QACnD,OAAOA;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,wBAAwB,WAAY;AAClC,UAAID,KAAI,KAAK,QAAQ;AACrB,MAAAA,OAAMA,GAAE,MAAM,UAAU,QAAQ,EAAE,QAAQ,SAASA,IAAG,8BAA8B,KAAK,EAAE,QAAQ,YAAYA,IAAG,8BAA8B,GAAG,KAAK,cAAcA,IAAG,EAAE,MAAM,EAAE,QAAQ,SAASA,IAAG,8BAA8B,GAAG,KAAK,cAAcA,IAAG,CAAC,IAAIA,GAAE,MAAM,UAAU;AAAA,IACvR;AAAA,IACA,eAAe,SAAUA,IAAGC,IAAG;AAC7B,UAAIC,KAAI,SAASF,GAAE,MAAM,WAAW,EAAE,IAAIC,IACxCE,KAAI,SAASH,GAAE,MAAM,YAAY,EAAE,IAAIC;AACzC,MAAAD,GAAE,MAAM,YAAYE,KAAI,MAAMF,GAAE,MAAM,aAAaG,KAAI;AAAA,IACzD;AAAA,EACF,CAAC,GAAG,EAAE,OAAO,YAAY,WAAY;AACnC,MAAE,KAAK,WAAW,KAAK,UAAU,IAAI,EAAE,KAAK,OAAO,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK,QAAQ,OAAO;AAAA,EACzG,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,OAAO,EAAE,QAAQ,OAAO;AAAA,IACxD,YAAY,SAAUH,IAAG;AACvB,WAAK,UAAU,CAACA,GAAE,QAAQ,GAAGA,GAAE,WAAW,KAAK,UAAU,KAAK,QAAQ,OAAOA,GAAE,MAAM,IAAI,KAAK,QAAQA,IAAG,KAAK,MAAM,GAAG,iBAAiB,KAAK,gBAAgB,IAAI;AAAA,IACnK;AAAA,IACA,eAAe,WAAY;AACzB,aAAO,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM,KAAK,MAAM,QAAQ,IAAI,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,MAAM;AAAA,IAC9H;AAAA,IACA,oBAAoB,SAAUA,IAAG;AAC/B,eAASC,KAAI,GAAGA,KAAI,KAAK,kBAAkB,QAAQA,KAAK,CAAAD,GAAE,KAAK,kBAAkBC,EAAC,CAAC;AAAA,IACrF;AAAA,IACA,UAAU,WAAY;AACpB,WAAK,cAAc,GAAG,KAAK,mBAAmB,SAAUD,IAAG;AACzD,QAAAA,GAAE,SAAS;AAAA,MACb,CAAC;AAAA,IACH;AAAA,IACA,aAAa,WAAY;AACvB,WAAK,mBAAmB,SAAUA,IAAG;AACnC,QAAAA,GAAE,YAAY;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,mBAAmB,SAAUA,IAAG;AACnC,QAAAA,GAAE,cAAc;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,oBAAoB,CAAC;AAC1B,eAASA,KAAI,GAAGA,KAAI,KAAK,QAAQ,QAAQA,KAAK,MAAK,kBAAkB,KAAK,IAAI,EAAE,KAAK,iBAAiB,KAAK,OAAO,KAAK,QAAQA,EAAC,GAAG,KAAK,MAAM,QAAQ,IAAI,CAAC;AAAA,IAC7J;AAAA,IACA,gBAAgB,SAAUA,IAAG;AAC3B,WAAK,UAAU,CAACA,GAAE,MAAM,QAAQ,GAAGA,GAAE,MAAM,WAAW,KAAK,UAAU,KAAK,QAAQ,OAAOA,GAAE,MAAM,MAAM;AAAA,IACzG;AAAA,EACF,CAAC,GAAG,EAAE,KAAK,mBAAmB,EAAE,QAAQ,OAAO;AAAA,IAC7C,SAAS;AAAA,MACP,MAAM,IAAI,EAAE,QAAQ;AAAA,QAClB,UAAU,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,QAC1B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,WAAW,IAAI,EAAE,QAAQ;AAAA,QACvB,UAAU,IAAI,EAAE,MAAM,IAAI,EAAE;AAAA,QAC5B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,WAAW;AAAA,QACT,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA,YAAY,SAAUA,IAAGC,IAAGC,IAAG;AAC7B,QAAE,QAAQ,UAAU,KAAK,QAAQ,OAAO,KAAK,QAAQ,YAAY,KAAK,QAAQF,IAAGE,MAAKA,GAAE,cAAcA,GAAE,YAAY,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,QAAQ,WAAWA,GAAE,SAAS,IAAI,KAAK,WAAWD,IAAG,EAAE,WAAW,MAAMC,EAAC;AAAA,IACtN;AAAA,IACA,eAAe,WAAY;AACzB,aAAO,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM,KAAK,QAAQ,IAAI,KAAK,WAAW,KAAK,SAAS,CAAC,IAAI,KAAK;AAAA,IACtG;AAAA,IACA,UAAU,WAAY;AACpB,UAAIF,KAAI,KAAK,OACXC,KAAID,GAAE;AACR,MAAAA,cAAa,EAAE,YAAYA,GAAE,QAAQ,OAAO,OAAIA,GAAE,QAAQ,YAAYA,GAAE,QAAQ,QAAQ,OAAO,SAAMC,MAAKD,GAAE,QAAQ,QAAQ,cAAcA,GAAE,QAAQ,SAAS,aAAaA,GAAE,QAAQ,SAAS,UAAU,MAAM,GAAG,EAAE,QAAQ,SAAUA,IAAG;AACrO,UAAE,QAAQ,YAAYC,IAAGD,EAAC;AAAA,MAC5B,CAAC,GAAGA,GAAE,QAAQ,QAAQ,UAAU,MAAM,GAAG,EAAE,QAAQ,SAAUA,IAAG;AAC9D,UAAE,QAAQ,SAASC,IAAGD,EAAC;AAAA,MACzB,CAAC,IAAIA,GAAE,SAASA,GAAE,QAAQ,OAAO,GAAG,KAAK,MAAM,SAAS,KAAK,OAAO,KAAK,MAAM,MAAM,KAAK,gBAAgB,KAAK,aAAa,GAAG,KAAK,MAAM,KAAK,SAAS,KAAK,YAAY;AAAA,IAC3K;AAAA,IACA,aAAa,WAAY;AACvB,UAAIA,KAAI,KAAK,OACXC,KAAID,GAAE;AACR,MAAAC,MAAKD,GAAE,QAAQ,QAAQ,cAAcA,GAAE,QAAQ,QAAQ,UAAU,MAAM,GAAG,EAAE,QAAQ,SAAUA,IAAG;AAC/F,UAAE,QAAQ,YAAYC,IAAGD,EAAC;AAAA,MAC5B,CAAC,GAAGA,GAAE,QAAQ,SAAS,aAAaA,GAAE,QAAQ,SAAS,UAAU,MAAM,GAAG,EAAE,QAAQ,SAAUA,IAAG;AAC/F,UAAE,QAAQ,SAASC,IAAGD,EAAC;AAAA,MACzB,CAAC,IAAIA,GAAE,SAASA,GAAE,QAAQ,QAAQ,GAAGA,GAAE,SAASA,GAAE,KAAK,YAAY,KAAK,YAAY,GAAG,OAAO,KAAK,cAAc,OAAO,KAAK;AAAA,IAC/H;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,aAAa,YAAY,GAAG,KAAK,aAAa;AAAA,IACrD;AAAA,IACA,cAAc,WAAY;AACxB,WAAK,iBAAiB,KAAK,eAAe,IAAI,EAAE,WAAW,IAAI,KAAK,WAAW,CAAC;AAChF,UAAIA,IACFC,IACAC,IACAC,IACA,IAAI,KAAK,cAAc;AACzB,WAAKH,KAAI,GAAGE,KAAI,EAAE,QAAQF,KAAIE,IAAGF,KAAK,CAAAG,KAAI,KAAK,cAAc,EAAEH,EAAC,GAAGA,EAAC,GAAGG,GAAE,GAAG,SAAS,KAAK,gBAAgB,IAAI,GAAGA,GAAE,GAAG,eAAe,KAAK,gBAAgB,IAAI,GAAG,KAAK,SAAS,KAAKA,EAAC;AACrL,UAAI,GAAG;AACP,WAAKH,KAAI,GAAGC,KAAIC,KAAI,GAAGF,KAAIE,IAAGD,KAAID,KAAK,EAAC,MAAMA,MAAK,EAAE,WAAW,KAAK,iBAAiB,EAAE,aAAa,IAAI,KAAK,SAASC,EAAC,GAAG,IAAI,KAAK,SAASD,EAAC,GAAG,KAAK,oBAAoB,GAAG,CAAC,GAAG,KAAK,gBAAgB,GAAG,CAAC;AAAA,IAC5M;AAAA,IACA,eAAe,SAAUA,IAAGC,IAAG;AAC7B,UAAIC,KAAI,IAAI,EAAE,OAAO,MAAMF,IAAG;AAAA,QAC5B,WAAW;AAAA,QACX,MAAM,KAAK,QAAQ;AAAA,MACrB,CAAC;AACD,aAAOE,GAAE,cAAcF,IAAGE,GAAE,SAASD,IAAGC,GAAE,GAAG,aAAa,KAAK,oBAAoB,IAAI,EAAE,GAAG,QAAQ,KAAK,eAAe,IAAI,EAAE,GAAG,WAAW,KAAK,WAAW,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,YAAY,KAAK,WAAW,IAAI,EAAE,GAAG,iBAAiB,KAAK,cAAc,IAAI,EAAE,GAAG,eAAe,KAAK,WAAW,IAAI,GAAG,KAAK,aAAa,SAASA,EAAC,GAAGA;AAAA,IACvW;AAAA,IACA,oBAAoB,WAAY;AAC9B,WAAK,MAAM,KAAK,WAAW;AAAA,IAC7B;AAAA,IACA,gBAAgB,WAAY;AAC1B,UAAIF,KAAI,KAAK,cAAc,GACzBC,KAAI,CAAC,EAAE,OAAO,MAAMD,IAAG,SAAS;AAClC,aAAO,KAAK,MAAM,gBAAgBA,IAAG,IAAE,GAAG,KAAK,MAAM,OAAO,GAAGC;AAAA,IACjE;AAAA,IACA,eAAe,SAAUD,IAAG;AAC1B,UAAIC,KAAID,GAAE;AACV,WAAK,aAAa,YAAYA,EAAC,GAAG,KAAK,SAAS,OAAOC,IAAG,CAAC,GAAG,KAAK,eAAeA,IAAG,CAAC,GAAG,KAAK,eAAeA,IAAG,EAAE,GAAGD,GAAE,IAAI,aAAa,KAAK,oBAAoB,IAAI,EAAE,IAAI,QAAQ,KAAK,eAAe,IAAI,EAAE,IAAI,WAAW,KAAK,WAAW,IAAI,EAAE,IAAI,aAAa,KAAK,eAAe,IAAI,EAAE,IAAI,YAAY,KAAK,WAAW,IAAI,EAAE,IAAI,SAAS,KAAK,gBAAgB,IAAI,EAAE,IAAI,iBAAiB,KAAK,cAAc,IAAI,EAAE,IAAI,eAAe,KAAK,WAAW,IAAI;AAAA,IAClc;AAAA,IACA,WAAW,WAAY;AACrB,WAAK,MAAM,SAAS,MAAI,KAAK,MAAM,KAAK,MAAM,GAAG,KAAK,MAAM,KAAK,KAAK,EAAE,KAAK,MAAM,YAAY;AAAA,QAC7F,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,UAAIC,KAAID,GAAE,QACRE,KAAI,KAAK;AACX,UAAI,EAAE,OAAOD,GAAE,aAAaA,GAAE,OAAO,GAAGA,GAAE,eAAeA,GAAE,YAAY,UAAU,KAAK,iBAAiBA,GAAE,OAAOA,EAAC,CAAC,GAAGA,GAAE,gBAAgBA,GAAE,aAAa,UAAU,KAAK,iBAAiBA,IAAGA,GAAE,KAAK,CAAC,GAAGC,GAAE,QAAQ,MAAM;AAClN,YAAIC,KAAID,GAAE,KAAK;AACf,YAAI,CAACA,GAAE,QAAQ,KAAK,qBAAqBA,GAAE,WAAW,GAAG;AACvD,cAAI,IAAIA,GAAE,QAAQ;AAClB,UAAAA,GAAE,SAAS;AAAA,YACT,OAAO,KAAK,QAAQ,UAAU;AAAA,UAChC,CAAC,GAAG,MAAM,EAAE,QAAQ,QAAQ,KAAK,KAAKD,GAAE,SAAS,WAAW,MAAMD,EAAC,GAAG,KAAK,eAAeA,EAAC,GAAGG,MAAKA,GAAE,cAAc;AAAA,YACjH,MAAM,EAAE,UAAU,KAAK,SAAS,SAAS;AAAA,UAC3C,CAAC,GAAG,WAAW,WAAY;AACzB,YAAAD,GAAE,SAAS;AAAA,cACT,OAAO;AAAA,YACT,CAAC,GAAGC,MAAKA,GAAE,cAAc;AAAA,cACvB,MAAM,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,cAC7C,SAAS,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,YAClD,CAAC;AAAA,UACH,GAAG,GAAG;AAAA,QACR;AAAA,MACF;AACA,WAAK,MAAM,QAAQ,aAAa,EAAE,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,QAAQ,aAAa,EAAE,OAAO,KAAK,GAAG,KAAK,CAAC;AAC/G,UAAI,IAAI,KAAK,MAAM,WAAW;AAC9B,WAAK,MAAM,gBAAgB,GAAG,IAAE,GAAG,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,KAAK,UAAU;AAAA,IACpF;AAAA,IACA,gBAAgB,SAAUH,IAAG;AAC3B,UAAIC,KAAI,EAAE,WAAW,KAAK,iBAAiB,EAAE,UAAU,IAAI,GACzDC,KAAIF,GAAE;AACR,WAAK,cAAc,EAAE,SAASC,OAAM,KAAK,cAAcC,EAAC,GAAG,KAAK,gBAAgBA,GAAE,OAAOA,GAAE,KAAK,GAAGA,GAAE,eAAe,KAAK,aAAa,YAAYA,GAAE,WAAW,GAAGA,GAAE,gBAAgB,KAAK,aAAa,YAAYA,GAAE,YAAY,GAAGA,GAAE,SAASA,GAAE,QAAQ,KAAK,oBAAoBA,GAAE,OAAOA,GAAE,KAAK,IAAIA,GAAE,QAAQA,GAAE,UAAUA,GAAE,MAAM,eAAe,QAAQA,GAAE,MAAM,cAAc,MAAM,KAAK,UAAU;AAAA,IACvY;AAAA,IACA,gBAAgB,SAAUF,IAAG;AAC3B,UAAIC,KAAID,GAAE;AACV,WAAK;AACL,WAAK,MAAM,KAAK,KAAK,EAAE,KAAK,MAAM,eAAe;AAAA,QAC/C,QAAQC;AAAA,QACR,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK;AAAA,MACb,CAAC,GAAG,EAAE,SAAS;AAAA,IACjB;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAIC,KAAI,KAAK,KAAK,uBAAuBD,GAAE,cAAc,QAAQ,CAAC,CAAC,GACjEE,KAAI,KAAK,KAAK,mBAAmBD,EAAC,GAClCE,KAAIH,GAAE;AACR,QAAE,OAAOG,GAAE,aAAaD,EAAC,GAAGC,GAAE,eAAeA,GAAE,YAAY,UAAU,KAAK,iBAAiBA,GAAE,OAAOA,EAAC,CAAC,GAAGA,GAAE,gBAAgBA,GAAE,aAAa,UAAU,KAAK,iBAAiBA,IAAGA,GAAE,KAAK,CAAC,GAAG,KAAK,MAAM,OAAO,GAAG,KAAK,cAAc;AAAA,IAClO;AAAA,IACA,gBAAgB,SAAUH,IAAGC,IAAG;AAC9B,WAAK,aAAa,UAAU,SAAUC,IAAG;AACvC,QAAAA,GAAE,SAASF,OAAME,GAAE,UAAUD;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,SAAUD,IAAGC,IAAG;AACnC,UAAIC,IACFC,IACA,GACA,IAAI,KAAK,iBAAiBH,IAAGC,EAAC,GAC9B,IAAI,KAAK,cAAc,CAAC;AAC1B,QAAE,WAAW,GAAE,GAAGD,GAAE,eAAeC,GAAE,cAAc,GAAGE,KAAI,WAAY;AACpE,UAAE,IAAI,aAAaA,IAAG,IAAI;AAC1B,YAAIC,KAAIH,GAAE;AACV,UAAE,SAASG,IAAG,EAAE,IAAI,SAASF,IAAG,IAAI,EAAE,GAAG,SAAS,KAAK,gBAAgB,IAAI,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,KAAK,eAAeE,IAAG,GAAG,CAAC,GAAG,KAAK,SAAS,OAAOA,IAAG,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,KAAK,eAAeA,IAAG,CAAC,GAAGH,GAAE,UAAU,KAAK,gBAAgBD,IAAG,CAAC,GAAG,KAAK,gBAAgB,GAAGC,EAAC,GAAG,KAAK,MAAM,KAAK,WAAW;AAAA,MAC9U,GAAG,IAAI,WAAY;AACjB,UAAE,IAAI,aAAaE,IAAG,IAAI,GAAG,EAAE,IAAI,WAAW,GAAG,IAAI,GAAG,EAAE,IAAI,aAAaA,IAAG,IAAI,GAAG,KAAK,oBAAoBH,IAAG,CAAC,GAAG,KAAK,oBAAoB,GAAGC,EAAC;AAAA,MACpJ,GAAGC,KAAI,WAAY;AACjB,QAAAC,GAAE,KAAK,IAAI,GAAG,EAAE,KAAK,IAAI,GAAG,KAAK,UAAU;AAAA,MAC7C,GAAG,EAAE,GAAG,SAASD,IAAG,IAAI,EAAE,GAAG,aAAaC,IAAG,IAAI,EAAE,GAAG,WAAW,GAAG,IAAI,EAAE,GAAG,aAAaA,IAAG,IAAI,GAAG,KAAK,aAAa,SAAS,CAAC;AAAA,IAClI;AAAA,IACA,iBAAiB,SAAUH,IAAGC,IAAG;AAC/B,MAAAD,OAAMA,GAAE,QAAQC,KAAIA,OAAMA,GAAE,QAAQD;AAAA,IACtC;AAAA,IACA,kBAAkB,SAAUA,IAAGC,IAAG;AAChC,UAAIC,KAAI,KAAK,MAAM,MACjBC,KAAID,GAAE,QAAQF,GAAE,UAAU,CAAC,GAC3B,IAAIE,GAAE,QAAQD,GAAE,UAAU,CAAC;AAC7B,aAAOC,GAAE,UAAUC,GAAE,KAAK,CAAC,EAAE,UAAU,CAAC,CAAC;AAAA,IAC3C;AAAA,EACF,CAAC,GAAG,EAAE,SAAS,YAAY,WAAY;AACrC,SAAK,YAAY,EAAE,KAAK,SAAS,KAAK,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK,QAAQ,OAAO,IAAI,KAAK,GAAG,OAAO,WAAY;AACjJ,WAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,SAAS;AAAA,IAClE,CAAC,GAAG,KAAK,GAAG,UAAU,WAAY;AAChC,WAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,YAAY;AAAA,IACrE,CAAC;AAAA,EACH,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,cAAc,EAAE,QAAQ,OAAO;AAAA,IAC/D,SAAS;AAAA,MACP,UAAU,IAAI,EAAE,QAAQ;AAAA,QACtB,UAAU,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,QAC1B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,YAAY,IAAI,EAAE,QAAQ;AAAA,QACxB,UAAU,IAAI,EAAE,MAAM,GAAG,CAAC;AAAA,QAC1B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,eAAe,IAAI,EAAE,QAAQ;AAAA,QAC3B,UAAU,IAAI,EAAE,MAAM,IAAI,EAAE;AAAA,QAC5B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,iBAAiB,IAAI,EAAE,QAAQ;AAAA,QAC7B,UAAU,IAAI,EAAE,MAAM,IAAI,EAAE;AAAA,QAC5B,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAUH,IAAGC,IAAG;AAC1B,QAAE,QAAQ,UAAU,KAAK,QAAQ,WAAW,KAAK,QAAQ,eAAe,KAAK,QAAQ,aAAa,KAAK,QAAQ,kBAAkB,KAAK,SAASD,IAAG,EAAE,KAAK,WAAW,MAAMC,EAAC;AAAA,IAC7K;AAAA,IACA,UAAU,WAAY;AACpB,UAAID,KAAI,KAAK;AACb,WAAK,OAAO,SAAS,KAAK,OAAO,KAAK,OAAO,MAAMA,GAAE,SAASA,GAAE,QAAQ,OAAO,GAAGA,GAAE,SAAS,KAAK,OAAOA,GAAE,MAAM,KAAK,gBAAgB,KAAK,aAAa,GAAG,KAAK,KAAK,SAAS,KAAK,YAAY;AAAA,IACjM;AAAA,IACA,aAAa,WAAY;AACvB,UAAIA,KAAI,KAAK;AACb,UAAIA,GAAE,SAASA,GAAE,QAAQ,QAAQ,GAAGA,GAAE,MAAM;AAC1C,aAAK,cAAc,KAAK,WAAW;AACnC,iBAASC,KAAI,GAAGC,KAAI,KAAK,eAAe,QAAQD,KAAIC,IAAGD,KAAK,MAAK,cAAc,KAAK,eAAeA,EAAC,CAAC;AACrG,aAAK,iBAAiB,MAAM,KAAK,KAAK,YAAY,KAAK,YAAY,GAAG,OAAO,KAAK;AAAA,MACpF;AACA,WAAK,OAAO;AAAA,IACd;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,aAAa,YAAY,GAAG,KAAK,aAAa;AAAA,IACrD;AAAA,IACA,cAAc,WAAY;AACxB,WAAK,iBAAiB,KAAK,eAAe,IAAI,EAAE,WAAW,IAAI,KAAK,kBAAkB,GAAG,KAAK,oBAAoB;AAAA,IACpH;AAAA,IACA,mBAAmB,WAAY;AAAA,IAAC;AAAA,IAChC,qBAAqB,WAAY;AAAA,IAAC;AAAA,IAClC,eAAe,SAAUD,IAAGC,IAAG;AAC7B,UAAIC,KAAI,IAAI,EAAE,OAAO,MAAMF,IAAG;AAAA,QAC5B,WAAW;AAAA,QACX,MAAMC;AAAA,QACN,cAAc;AAAA,MAChB,CAAC;AACD,aAAO,KAAK,YAAYC,EAAC,GAAG,KAAK,aAAa,SAASA,EAAC,GAAGA;AAAA,IAC7D;AAAA,IACA,aAAa,SAAUF,IAAG;AACxB,MAAAA,GAAE,GAAG,aAAa,KAAK,oBAAoB,IAAI,EAAE,GAAG,QAAQ,KAAK,eAAe,IAAI,EAAE,GAAG,WAAW,KAAK,kBAAkB,IAAI,EAAE,GAAG,cAAc,KAAK,eAAe,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,iBAAiB,KAAK,cAAc,IAAI,EAAE,GAAG,YAAY,KAAK,aAAa,IAAI,EAAE,GAAG,eAAe,KAAK,aAAa,IAAI;AAAA,IACnV;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,MAAAA,GAAE,IAAI,aAAa,KAAK,oBAAoB,IAAI,EAAE,IAAI,QAAQ,KAAK,eAAe,IAAI,EAAE,IAAI,WAAW,KAAK,kBAAkB,IAAI,EAAE,IAAI,cAAc,KAAK,eAAe,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,iBAAiB,KAAK,cAAc,IAAI,EAAE,IAAI,YAAY,KAAK,aAAa,IAAI,EAAE,IAAI,eAAe,KAAK,aAAa,IAAI;AAAA,IAC3V;AAAA,IACA,oBAAoB,SAAUA,IAAG;AAC/B,MAAAA,GAAE,OAAO,WAAW,CAAC,GAAG,KAAK,OAAO,KAAK,WAAW;AAAA,IACtD;AAAA,IACA,WAAW,WAAY;AACrB,WAAK,OAAO,SAAS,MAAI,KAAK,OAAO,KAAK,MAAM;AAAA,IAClD;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,UAAIC,KAAID,GAAE,QACRE,KAAID,GAAE,UAAU;AAClB,MAAAA,OAAM,KAAK,cAAc,KAAK,MAAMC,EAAC,IAAI,KAAK,QAAQA,EAAC,GAAG,KAAK,OAAO,OAAO,GAAG,KAAK,OAAO,KAAK,UAAU;AAAA,IAC7G;AAAA,IACA,kBAAkB,SAAUF,IAAG;AAC7B,MAAAA,GAAE,OAAO,WAAW,CAAC,GAAG,KAAK,UAAU;AAAA,IACzC;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,UAAI,EAAE,KAAK,YAAY,UAAU,mBAAmB,KAAK,MAAMA,EAAC,GAAG,cAAc,OAAO,KAAK,aAAa;AACxG,YAAIC,KAAI,KAAK,YAAY,GACvBC,KAAIF,GAAE,QACNG,KAAID,GAAE;AACR,QAAAA,GAAE,WAAW,CAAC,GAAG,KAAK,kBAAkBD,IAAGE,KAAI,KAAK,CAAC,GAAG,KAAK,qBAAqB,GAAGA,EAAC;AAAA,MACxF;AACA,WAAK,OAAO,KAAK,WAAW;AAAA,IAC9B;AAAA,IACA,cAAc,SAAUH,IAAG;AACzB,UAAIC,KAAI,KAAK,KAAK,uBAAuBD,GAAE,cAAc,QAAQ,CAAC,CAAC,GACjEE,KAAI,KAAK,KAAK,mBAAmBD,EAAC;AACpC,aAAOD,GAAE,WAAW,KAAK,cAAc,KAAK,MAAME,EAAC,IAAI,KAAK,QAAQA,EAAC,GAAG,KAAK,OAAO,OAAO,GAAG;AAAA,IAChG;AAAA,IACA,aAAa,SAAUF,IAAG;AACxB,MAAAA,GAAE,OAAO,WAAW,CAAC,GAAG,KAAK,cAAc,GAAG,KAAK,UAAU;AAAA,IAC/D;AAAA,IACA,OAAO,WAAY;AAAA,IAAC;AAAA,IACpB,SAAS,WAAY;AAAA,IAAC;AAAA,EACxB,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,YAAY,EAAE,KAAK,YAAY,OAAO;AAAA,IACtE,mBAAmB,WAAY;AAC7B,UAAIA,KAAI,KAAK,OAAO,UAAU,GAC5BC,KAAID,GAAE,UAAU;AAClB,WAAK,cAAc,KAAK,cAAcC,IAAG,KAAK,QAAQ,QAAQ;AAAA,IAChE;AAAA,IACA,qBAAqB,WAAY;AAC/B,UAAID,KAAI,KAAK,YAAY;AACzB,WAAK,iBAAiB,CAAC;AACvB,eAASC,KAAI,GAAGC,KAAIF,GAAE,QAAQC,KAAIC,IAAGD,KAAK,MAAK,eAAe,KAAK,KAAK,cAAcD,GAAEC,EAAC,GAAG,KAAK,QAAQ,UAAU,CAAC,GAAG,KAAK,eAAeA,EAAC,EAAE,eAAeA;AAAA,IAC/J;AAAA,IACA,oBAAoB,SAAUD,IAAG;AAC/B,QAAE,KAAK,YAAY,UAAU,mBAAmB,KAAK,MAAMA,EAAC;AAC5D,UAAIC,KAAI,KAAK,YAAY,GACvBC,KAAIF,GAAE,QACNG,KAAID,GAAE;AACR,WAAK,kBAAkBD,IAAGE,KAAI,KAAK,CAAC,GAAG,KAAK,qBAAqB,GAAGA,EAAC;AAAA,IACvE;AAAA,IACA,kBAAkB,SAAUH,IAAG;AAC7B,UAAIC,IACFC,IACAC,KAAIH,GAAE;AACR,MAAAG,OAAM,KAAK,gBAAgBF,KAAI,KAAK,OAAO,UAAU,GAAGC,KAAID,GAAE,UAAU,GAAGE,GAAE,UAAUD,EAAC,IAAI,KAAK,qBAAqB,CAAC,GAAG,KAAK,yBAAyB,GAAG,EAAE,KAAK,YAAY,UAAU,iBAAiB,KAAK,MAAMF,EAAC;AAAA,IACvN;AAAA,IACA,OAAO,SAAUA,IAAG;AAClB,eAASC,IAAGC,KAAI,KAAK,OAAO,gBAAgB,KAAK,OAAO,cAAc,IAAI,KAAK,OAAO,WAAW,GAAGC,KAAI,KAAK,OAAO,UAAU,GAAG,IAAIA,GAAE,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAID,GAAE,QAAQ,IAAI,GAAG,IAAK,CAAAD,KAAI,CAACC,GAAE,CAAC,EAAE,MAAM,EAAE,KAAKA,GAAE,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,EAAE,KAAK,CAACF,GAAE,MAAMC,GAAE,CAAC,GAAGD,GAAE,MAAMC,GAAE,CAAC,CAAC,CAAC;AAC1Q,WAAK,OAAO,WAAW,CAAC,GAAG,KAAK,yBAAyB,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,UAAU;AAAA,QAChG,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,SAAS,SAAUD,IAAG;AACpB,UAAIC;AACJ,WAAK,OAAO,UAAU,EAAE,aAAaD,IAAG,KAAK,eAAe,CAAC,GAAGC,KAAI,KAAK,OAAO,UAAU,GAAG,KAAK,YAAY,UAAUA,GAAE,UAAU,CAAC,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,YAAY;AAAA,QAC9K,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,IACA,aAAa,WAAY;AACvB,UAAID,KAAI,KAAK,OAAO,UAAU;AAC9B,aAAO,CAACA,GAAE,aAAa,GAAGA,GAAE,aAAa,GAAGA,GAAE,aAAa,GAAGA,GAAE,aAAa,CAAC;AAAA,IAChF;AAAA,IACA,sBAAsB,SAAUA,IAAG;AACjC,eAASC,KAAI,GAAGC,KAAI,KAAK,eAAe,QAAQD,KAAIC,IAAGD,KAAK,MAAK,eAAeA,EAAC,EAAE,WAAWD,EAAC;AAAA,IACjG;AAAA,IACA,0BAA0B,WAAY;AACpC,eAASA,KAAI,KAAK,YAAY,GAAGC,KAAI,GAAGC,KAAI,KAAK,eAAe,QAAQD,KAAIC,IAAGD,KAAK,MAAK,eAAeA,EAAC,EAAE,UAAUD,GAAEC,EAAC,CAAC;AAAA,IAC3H;AAAA,EACF,CAAC,GAAG,EAAE,UAAU,YAAY,WAAY;AACtC,MAAE,KAAK,cAAc,KAAK,UAAU,IAAI,EAAE,KAAK,UAAU,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK,QAAQ,OAAO;AAAA,EAC/G,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,eAAe,EAAE,KAAK,YAAY,OAAO;AAAA,IACzE,mBAAmB,WAAY;AAC7B,UAAID,KAAI,KAAK,OAAO,UAAU;AAC9B,WAAK,cAAc,KAAK,cAAcA,IAAG,KAAK,QAAQ,QAAQ;AAAA,IAChE;AAAA,IACA,qBAAqB,WAAY;AAC/B,WAAK,iBAAiB,CAAC;AAAA,IACzB;AAAA,IACA,OAAO,SAAUA,IAAG;AAClB,UAAI,KAAK,eAAe,QAAQ;AAC9B,YAAIC,KAAI,KAAK,sBAAsBD,EAAC;AACpC,aAAK,eAAe,CAAC,EAAE,UAAUC,EAAC;AAAA,MACpC;AACA,WAAK,OAAO,UAAUD,EAAC,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,UAAU;AAAA,QAC9D,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF,CAAC,GAAG,EAAE,aAAa,YAAY,WAAY;AACzC,MAAE,KAAK,iBAAiB,KAAK,UAAU,IAAI,EAAE,KAAK,aAAa,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK,QAAQ,OAAO,IAAI,KAAK,GAAG,OAAO,WAAY;AAChJ,WAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,SAAS;AAAA,IAClE,CAAC,GAAG,KAAK,GAAG,UAAU,WAAY;AAChC,WAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,YAAY;AAAA,IACrE,CAAC;AAAA,EACH,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,aAAa,OAAO;AAAA,IACpE,qBAAqB,WAAY;AAC/B,UAAIA,KAAI,KAAK,OAAO,UAAU,GAC5BC,KAAI,KAAK,sBAAsBD,EAAC;AAClC,WAAK,iBAAiB,CAAC,GAAG,KAAK,eAAe,KAAK,KAAK,cAAcC,IAAG,KAAK,QAAQ,UAAU,CAAC;AAAA,IACnG;AAAA,IACA,uBAAuB,SAAUD,IAAG;AAClC,UAAIC,KAAI,KAAK,OAAO,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC,GAChDC,KAAI,KAAK,KAAK,QAAQF,EAAC;AACzB,aAAO,KAAK,KAAK,UAAU,CAACE,GAAE,IAAID,IAAGC,GAAE,IAAID,EAAC,CAAC;AAAA,IAC/C;AAAA,IACA,SAAS,SAAUD,IAAG;AACpB,UAAIC,KAAI,KAAK,YAAY,UAAU;AACnC,QAAE,aAAa,aAAa,IAAI,SAASA,GAAE,WAAWD,EAAC,IAAI,SAAS,KAAK,KAAK,SAASC,IAAGD,EAAC,GAAG,KAAK,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,eAAe,KAAK,KAAK,aAAa,cAAc;AAAA,QACzL,MAAM,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ,UAAU,WAAW,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,QACzG,SAAS,EAAE,UAAU,KAAK,SAAS,OAAO,SAAS,OAAO,EAAE,aAAa,iBAAiB,QAAQ,MAAI,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM;AAAA,MAC9I,CAAC,GAAG,KAAK,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,YAAY;AAAA,QACzE,OAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF,CAAC,GAAG,EAAE,OAAO,YAAY,WAAY;AACnC,MAAE,KAAK,WAAW,KAAK,UAAU,IAAI,EAAE,KAAK,OAAO,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK,QAAQ,OAAO,IAAI,KAAK,GAAG,OAAO,WAAY;AACpI,WAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,SAAS;AAAA,IAClE,CAAC,GAAG,KAAK,GAAG,UAAU,WAAY;AAChC,WAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,YAAY;AAAA,IACrE,CAAC;AAAA,EACH,CAAC,GAAG,EAAE,IAAI,aAAa;AAAA,IACrB,aAAa;AAAA,EACf,CAAC,GAAG,EAAE,IAAI,cAAc,EAAE,QAAQ,OAAO;AAAA,IACvC,YAAY,SAAUA,IAAG;AACvB,WAAK,OAAOA,IAAG,KAAK,aAAaA,GAAE,YAAY,KAAK,QAAQA,GAAE,OAAO;AAAA,IACvE;AAAA,IACA,UAAU,WAAY;AACpB,QAAE,SAAS,GAAG,KAAK,YAAY,cAAc,KAAK,eAAe,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,YAAY,KAAK,aAAa,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,aAAa,KAAK,cAAc,IAAI,GAAG,KAAK,UAAU,KAAK,EAAE,SAAS,GAAG,KAAK,YAAY,iBAAiB,KAAK,eAAe,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,eAAe,KAAK,aAAa,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,iBAAiB,KAAK,cAAc,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,mBAAmB,KAAK,gBAAgB,IAAI,MAAM,EAAE,SAAS,GAAG,KAAK,YAAY,eAAe,KAAK,gBAAgB,IAAI,GAAG,EAAE,SAAS,GAAG,KAAK,YAAY,cAAc,KAAK,eAAe,IAAI;AAAA,IACpqB;AAAA,IACA,aAAa,WAAY;AACvB,QAAE,SAAS,IAAI,KAAK,YAAY,cAAc,KAAK,aAAa,GAAG,EAAE,SAAS,IAAI,KAAK,YAAY,YAAY,KAAK,WAAW,GAAG,EAAE,SAAS,IAAI,KAAK,YAAY,aAAa,KAAK,YAAY,GAAG,KAAK,UAAU,KAAK,EAAE,SAAS,IAAI,KAAK,YAAY,iBAAiB,KAAK,aAAa,GAAG,EAAE,SAAS,IAAI,KAAK,YAAY,eAAe,KAAK,WAAW,GAAG,EAAE,SAAS,IAAI,KAAK,YAAY,iBAAiB,KAAK,YAAY,GAAG,EAAE,SAAS,IAAI,KAAK,YAAY,mBAAmB,KAAK,cAAc,MAAM,EAAE,SAAS,IAAI,KAAK,YAAY,eAAe,KAAK,cAAc,GAAG,EAAE,SAAS,IAAI,KAAK,YAAY,cAAc,KAAK,aAAa;AAAA,IACvnB;AAAA,IACA,aAAa,SAAUA,IAAGC,IAAG;AAC3B,UAAIC,KAAI,CAAC;AACT,UAAI,WAAWF,GAAE,SAAS;AACxB,YAAI,CAACA,GAAE,QAAQ,OAAQ;AACvB,QAAAE,KAAIF,GAAE,QAAQ,CAAC;AAAA,MACjB,OAAO;AACL,YAAI,YAAYA,GAAE,YAAa;AAC/B,YAAIE,KAAIF,IAAG,CAAC,KAAK,aAAaA,EAAC,EAAG;AAAA,MACpC;AACA,UAAIG,KAAI,KAAK,KAAK,2BAA2BD,EAAC,GAC5C,IAAI,KAAK,KAAK,uBAAuBA,EAAC,GACtC,IAAI,KAAK,KAAK,mBAAmB,CAAC;AACpC,WAAK,KAAK,KAAKD,IAAG;AAAA,QAChB,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,gBAAgBE;AAAA,QAChB,OAAOD,GAAE;AAAA,QACT,OAAOA,GAAE;AAAA,QACT,eAAeF;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,cAAc,SAAUA,IAAG;AACzB,UAAIC,KAAID,GAAE,aAAaA,GAAE,cAAc,WACrCE,KAAI,EAAE,SAAS,cAAcD,KAAI,EAAE,SAAS;AAC9C,aAAOC,MAAKA,KAAI,OAAOA,KAAI,OAAOF,GAAE,OAAO,mBAAmB,CAACA,GAAE,cAAc,EAAE,SAAS,KAAKA,EAAC,GAAG,UAAO,EAAE,SAAS,aAAaC,IAAG;AAAA,IACvI;AAAA,IACA,eAAe,SAAUD,IAAG;AAC1B,UAAI,KAAK,KAAK,SAAS;AACrB,aAAK,YAAYA,IAAG,YAAY;AAAA,MAClC;AAAA,IACF;AAAA,IACA,aAAa,SAAUA,IAAG;AACxB,UAAI,KAAK,KAAK,SAAS;AACrB,aAAK,YAAYA,IAAG,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,IACA,gBAAgB,SAAUA,IAAG;AAC3B,UAAI,KAAK,KAAK,SAAS;AACrB,YAAIC,KAAI;AACR,aAAK,UAAU,MAAMA,KAAI,kBAAkB,KAAK,YAAYD,IAAGC,EAAC;AAAA,MAClE;AAAA,IACF;AAAA,IACA,eAAe,SAAUD,IAAG;AAC1B,UAAI,KAAK,KAAK,SAAS;AACrB,aAAK,YAAYA,IAAG,YAAY;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,SAAUA,IAAG;AACzB,UAAI,KAAK,KAAK,SAAS;AACrB,aAAK,YAAYA,IAAG,WAAW;AAAA,MACjC;AAAA,IACF;AAAA,IACA,WAAW,WAAY;AACrB,UAAIC,KAAI,EAAE,UAAU,WAClBC,KAAID,GAAE,QAAQ,OAAO;AACvB,UAAIC,KAAI,EAAG,QAAO,SAASD,GAAE,UAAUC,KAAI,GAAGD,GAAE,QAAQ,KAAKC,EAAC,CAAC,GAAG,EAAE;AACpE,UAAID,GAAE,QAAQ,UAAU,IAAI,GAAG;AAC7B,YAAIE,KAAIF,GAAE,QAAQ,KAAK;AACvB,eAAO,SAASA,GAAE,UAAUE,KAAI,GAAGF,GAAE,QAAQ,KAAKE,EAAC,CAAC,GAAG,EAAE;AAAA,MAC3D;AACA,UAAI,IAAIF,GAAE,QAAQ,OAAO;AACzB,aAAO,IAAI,KAAK,SAASA,GAAE,UAAU,IAAI,GAAGA,GAAE,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE;AAAA,IACpE;AAAA,EACF,CAAC,GAAG,EAAE,IAAI,YAAY,cAAc,eAAe,EAAE,IAAI,WAAW,GAAG,EAAE,OAAO,QAAQ,EAAE,OAAO,OAAO;AAAA,IACtG,kBAAkB,WAAY;AAC5B,aAAO,KAAK,uBAAuB,EAAE,OAAO,UAAU,iBAAiB,MAAM,IAAI,IAAI,KAAK,uBAAuB;AAAA,IACnH;AAAA,IACA,wBAAwB,WAAY;AAClC,UAAI,KAAK,QAAQ,WAAW;AAC1B,YAAID,KAAI,KAAK,OACXC,KAAI,CAAC,YAAY,aAAa,aAAa,YAAY,eAAe,cAAc,YAAY,WAAW;AAC7G,aAAK,YAAYA,GAAE,OAAO,CAAC,iBAAiB,eAAe,iBAAiB,iBAAiB,CAAC,IAAIA,GAAE,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,SAASD,IAAG,mBAAmB,GAAG,EAAE,SAAS,GAAGA,IAAG,SAAS,KAAK,eAAe,IAAI,GAAG,EAAE,SAAS,GAAGA,IAAG,YAAY,KAAK,aAAa,IAAI;AACjR,iBAASE,KAAI,GAAGA,KAAID,GAAE,QAAQC,KAAK,GAAE,SAAS,GAAGF,IAAGC,GAAEC,EAAC,GAAG,KAAK,iBAAiB,IAAI;AACpF,UAAE,QAAQ,eAAe,KAAK,WAAW,IAAI,EAAE,QAAQ,WAAW,IAAI,GAAG,KAAK,QAAQ,aAAa,KAAK,SAAS,OAAO;AAAA,MAC1H;AAAA,IACF;AAAA,IACA,WAAW,WAAY;AACrB,UAAID,KAAI,EAAE,UAAU,WAClBC,KAAID,GAAE,QAAQ,OAAO;AACvB,UAAIC,KAAI,EAAG,QAAO,SAASD,GAAE,UAAUC,KAAI,GAAGD,GAAE,QAAQ,KAAKC,EAAC,CAAC,GAAG,EAAE;AACpE,UAAID,GAAE,QAAQ,UAAU,IAAI,GAAG;AAC7B,YAAIE,KAAIF,GAAE,QAAQ,KAAK;AACvB,eAAO,SAASA,GAAE,UAAUE,KAAI,GAAGF,GAAE,QAAQ,KAAKE,EAAC,CAAC,GAAG,EAAE;AAAA,MAC3D;AACA,UAAI,IAAIF,GAAE,QAAQ,OAAO;AACzB,aAAO,IAAI,KAAK,SAASA,GAAE,UAAU,IAAI,GAAGA,GAAE,QAAQ,KAAK,CAAC,CAAC,GAAG,EAAE;AAAA,IACpE;AAAA,EACF,CAAC,GAAG,EAAE,aAAa;AAAA,IACjB,cAAc,SAAUD,IAAG;AACzB,eAASC,KAAI,CAAC,GAAGC,KAAI,GAAGC,KAAIH,GAAE,QAAQE,KAAIC,IAAGD,KAAK,OAAM,QAAQF,GAAEE,EAAC,CAAC,IAAID,GAAE,KAAK,EAAE,WAAW,aAAaD,GAAEE,EAAC,CAAC,CAAC,IAAID,GAAE,KAAK,KAAK,YAAYD,GAAEE,EAAC,CAAC,CAAC;AAC/I,aAAOD;AAAA,IACT;AAAA,IACA,aAAa,SAAUD,IAAG;AACxB,aAAO,EAAE,OAAOA,GAAE,KAAKA,GAAE,GAAG;AAAA,IAC9B;AAAA,EACF,GAAG,WAAY;AACb,QAAIA,KAAI;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN;AACA,MAAE,eAAe,EAAE,OAAO,EAAE,gBAAgB,CAAC,GAAG;AAAA,MAC9C,cAAc,SAAUA,IAAG;AACzB,YAAIC,IACFC,IACAC,KAAIH,GAAE,QACN,IAAI,GACJ,IAAI,KAAK,KAAK;AAChB,YAAIG,KAAI,GAAG;AACT,mBAAS,IAAI,GAAG,IAAIA,IAAG,IAAK,CAAAF,KAAID,GAAE,CAAC,GAAGE,KAAIF,IAAG,IAAI,KAAKG,EAAC,GAAG,MAAMD,GAAE,MAAMD,GAAE,OAAO,KAAK,IAAI,KAAK,IAAIA,GAAE,MAAM,CAAC,IAAI,KAAK,IAAIC,GAAE,MAAM,CAAC;AAClI,cAAI,UAAU,IAAI,UAAU;AAAA,QAC9B;AACA,eAAO,KAAK,IAAI,CAAC;AAAA,MACnB;AAAA,MACA,iBAAiB,SAAUF,IAAGC,IAAG;AAC/B,YAAIC,KAAI,WAAWF,EAAC,EAAE,QAAQC,EAAC,GAC7BE,KAAI,EAAE,UAAU,UAAU,EAAE,UAAU,OAAO,SAC7C,IAAIA,MAAKA,GAAE,YACX,IAAI,KAAK,EAAE,WACX,IAAI,KAAK,EAAE;AACb,YAAI,KAAK,GAAG;AACV,cAAI,IAAID,GAAE,MAAM,GAAG;AACnB,UAAAA,KAAI,IAAI,EAAE,CAAC,EAAE,QAAQ,2BAA2B,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,KAAK,KAAK,EAAE,SAAS,MAAMA,KAAIA,KAAI,IAAI,EAAE,CAAC;AAAA,QAClH;AACA,eAAOA;AAAA,MACT;AAAA,MACA,cAAc,SAAUD,IAAGC,IAAGC,IAAG;AAC/B,YAAI,GACF,GACAA,KAAI,EAAE,KAAK,OAAO,CAAC,GAAGH,IAAGG,EAAC;AAC5B,eAAOD,MAAK,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,OAAOA,IAAG,aAAa,OAAO,IAAI,CAACA,EAAC,IAAI,cAAc,SAAS,IAAIA,KAAI,IAAID,MAAK,OAAO,OAAO,EAAE,QAAQ,IAAI,IAAI,EAAE,aAAa,gBAAgB,OAAOA,IAAGE,GAAE,EAAE,IAAI,SAASF,MAAK,OAAO,OAAO,EAAE,QAAQ,IAAI,IAAI,EAAE,aAAa,gBAAgB,OAAOA,IAAGE,GAAE,EAAE,IAAI,QAAQ,EAAE,aAAa,gBAAgBF,IAAGE,GAAE,CAAC,IAAI,UAAUF,MAAK,UAAS,IAAIA,MAAK,UAAU,EAAE,aAAa,gBAAgBA,KAAI,SAASE,GAAE,EAAE,IAAI,SAASF,MAAK,OAAO,EAAE,aAAa,gBAAgBA,KAAI,MAAME,GAAE,EAAE,IAAI,WAAW,EAAE,aAAa,gBAAgBF,IAAGE,GAAE,EAAE,IAAI,SAAS;AAAA,MAC1jB;AAAA,MACA,kBAAkB,SAAUF,IAAGC,IAAGC,IAAG,GAAG,GAAG;AACzC,YAAI,GACF,IAAI,EAAE,KAAK,OAAO,CAAC,GAAGH,IAAG,CAAC;AAC5B,gBAAQE,KAAI,YAAY,OAAOA,KAAIA,KAAI,WAAWC,KAAI,SAAS,IAAI,iBAAiB,SAAS;AAAA,UAC3F,KAAK;AACH,gBAAIF,KAAI,MAAM,EAAE,aAAa,gBAAgBA,KAAI,KAAK,EAAE,EAAE,IAAI,QAAQ,EAAE,aAAa,gBAAgBA,IAAG,EAAE,CAAC,IAAI;AAC/G;AAAA,UACF,KAAK;AACH,YAAAA,MAAK,SAAS,IAAI,EAAE,aAAa,gBAAgBA,IAAG,EAAE,EAAE,IAAI;AAC5D;AAAA,UACF,KAAK;AACH,YAAAA,MAAK,SAAQ,IAAI,EAAE,aAAa,gBAAgBA,KAAI,KAAK,EAAE,EAAE,IAAI;AACjE;AAAA,UACF,KAAK;AAAA,UACL;AACE,YAAAA,MAAK,SAAS,IAAIA,KAAI,OAAO,EAAE,aAAa,gBAAgBA,KAAI,MAAM,EAAE,EAAE,IAAI,WAAW,EAAE,aAAa,gBAAgBA,IAAG,EAAE,EAAE,IAAI;AAAA,QACvI;AACA,eAAO;AAAA,MACT;AAAA,MACA,cAAc,WAAY;AACxB,YAAID,KAAI,EAAE,QAAQ,MAAM,GAAG;AAC3B,eAAO,MAAM,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,MAAM,SAASA,GAAE,CAAC,GAAG,EAAE;AAAA,MAC5D;AAAA,IACF,CAAC;AAAA,EACH,EAAE,GAAG,EAAE,KAAK,OAAO,EAAE,UAAU;AAAA,IAC7B,mBAAmB,SAAUA,IAAGC,IAAGC,IAAGC,IAAG;AACvC,aAAO,KAAK,uBAAuBH,IAAGE,IAAGC,EAAC,MAAM,KAAK,uBAAuBF,IAAGC,IAAGC,EAAC,KAAK,KAAK,uBAAuBH,IAAGC,IAAGC,EAAC,MAAM,KAAK,uBAAuBF,IAAGC,IAAGE,EAAC;AAAA,IACtK;AAAA,IACA,wBAAwB,SAAUH,IAAGC,IAAGC,IAAG;AACzC,cAAQA,GAAE,IAAIF,GAAE,MAAMC,GAAE,IAAID,GAAE,MAAMC,GAAE,IAAID,GAAE,MAAME,GAAE,IAAIF,GAAE;AAAA,IAC5D;AAAA,EACF,CAAC,GAAG,EAAE,SAAS,QAAQ;AAAA,IACrB,YAAY,WAAY;AACtB,UAAIA,IACFC,IACAC,IACAC,KAAI,KAAK,oBAAoB,GAC7B,IAAIA,KAAIA,GAAE,SAAS;AACrB,UAAI,KAAK,6BAA6B,EAAG,QAAO;AAChD,WAAKH,KAAI,IAAI,GAAGA,MAAK,GAAGA,KAAK,KAAIC,KAAIE,GAAEH,KAAI,CAAC,GAAGE,KAAIC,GAAEH,EAAC,GAAG,KAAK,6BAA6BC,IAAGC,IAAGF,KAAI,CAAC,EAAG,QAAO;AAChH,aAAO;AAAA,IACT;AAAA,IACA,qBAAqB,SAAUA,IAAGC,IAAG;AACnC,aAAO,CAAC,CAAC,KAAK,QAAQ,KAAK,mBAAmB,KAAK,KAAK,mBAAmBD,EAAC,GAAGC,EAAC;AAAA,IAClF;AAAA,IACA,oBAAoB,SAAUD,IAAGC,IAAG;AAClC,UAAIC,KAAI,KAAK,oBAAoB,GAC/BC,KAAID,KAAIA,GAAE,SAAS,GACnB,IAAIA,KAAIA,GAAEC,KAAI,CAAC,IAAI,MACnB,IAAIA,KAAI;AACV,aAAO,CAAC,KAAK,6BAA6B,CAAC,KAAK,KAAK,6BAA6B,GAAGH,IAAG,GAAGC,KAAI,IAAI,CAAC;AAAA,IACtG;AAAA,IACA,8BAA8B,SAAUD,IAAG;AACzC,UAAIC,KAAI,KAAK,oBAAoB,GAC/BC,KAAID,KAAIA,GAAE,SAAS;AACrB,aAAOC,MAAKF,MAAK,GAAG,CAACC,MAAKC,MAAK;AAAA,IACjC;AAAA,IACA,8BAA8B,SAAUF,IAAGC,IAAGC,IAAGC,IAAG;AAClD,UAAI,GACF,GACA,IAAI,KAAK,oBAAoB;AAC/B,MAAAA,KAAIA,MAAK;AACT,eAAS,IAAID,IAAG,IAAIC,IAAG,IAAK,KAAI,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,SAAS,kBAAkBH,IAAGC,IAAG,GAAG,CAAC,EAAG,QAAO;AACzG,aAAO;AAAA,IACT;AAAA,IACA,qBAAqB,WAAY;AAC/B,UAAI,CAAC,KAAK,cAAe,QAAO,KAAK;AACrC,eAASD,KAAI,CAAC,GAAGC,KAAI,KAAK,cAAc,GAAGC,KAAI,GAAGA,KAAID,GAAE,QAAQC,KAAK,CAAAF,GAAE,KAAK,KAAK,KAAK,mBAAmBC,GAAEC,EAAC,CAAC,CAAC;AAC9G,aAAOF;AAAA,IACT;AAAA,EACF,CAAC,GAAG,EAAE,QAAQ,QAAQ;AAAA,IACpB,YAAY,WAAY;AACtB,UAAIA,IACFC,IACAC,IACAC,IACA,IAAI,KAAK,oBAAoB;AAC/B,aAAO,CAAC,KAAK,6BAA6B,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,WAAW,KAAK,IAAI,MAAMH,KAAI,EAAE,QAAQC,KAAI,EAAE,CAAC,GAAGC,KAAI,EAAEF,KAAI,CAAC,GAAGG,KAAIH,KAAI,GAAG,KAAK,6BAA6BE,IAAGD,IAAGE,IAAG,CAAC;AAAA,IAChM;AAAA,EACF,CAAC,GAAG,EAAE,QAAQ,OAAO,EAAE,QAAQ,OAAO;AAAA,IACpC,SAAS;AAAA,MACP,UAAU;AAAA,MACV,MAAM,CAAC;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,YAAY,SAAUH,IAAG;AACvB,UAAI,EAAE,UAAU,MAAO,OAAM,IAAI,MAAM,uGAAuG;AAC9I,QAAE,QAAQ,UAAU,WAAW,KAAK,MAAMA,EAAC;AAC3C,UAAIC;AACJ,WAAK,YAAY,CAAC,GAAG,EAAE,eAAe,KAAK,QAAQ,SAASA,KAAI,IAAI,EAAE,YAAY,KAAK,QAAQ,IAAI,GAAG,KAAK,UAAU,EAAE,YAAY,IAAI,IAAIA,IAAG,KAAK,UAAU,EAAE,YAAY,IAAI,EAAE,GAAG,UAAU,KAAK,iBAAiB,IAAI,IAAI,EAAE,eAAe,KAAK,QAAQ,SAASA,KAAI,IAAI,EAAE,YAAY,KAAK,QAAQ,IAAI,GAAG,KAAK,UAAU,EAAE,YAAY,IAAI,IAAIA,IAAG,KAAK,UAAU,EAAE,YAAY,IAAI,EAAE,GAAG,UAAU,KAAK,iBAAiB,IAAI,IAAI,EAAE,UAAU;AAAA,IACjb;AAAA,IACA,OAAO,SAAUD,IAAG;AAClB,UAAIC,IACFC,KAAI,EAAE,QAAQ,OAAO,OAAO,cAAc,GAC1CC,KAAI;AACN,eAAS,KAAK,KAAK,UAAW,MAAK,UAAU,eAAe,CAAC,MAAMF,KAAI,KAAK,UAAU,CAAC,EAAE,WAAWD,EAAC,OAAOG,OAAM,EAAE,QAAQ,SAASF,IAAG,0BAA0B,KAAK,EAAE,QAAQ,SAASA,GAAE,WAAW,CAAC,GAAG,0BAA0B,GAAGE,KAAI,OAAKD,GAAE,YAAYD,EAAC;AAChQ,aAAOC;AAAA,IACT;AAAA,IACA,UAAU,WAAY;AACpB,eAASF,MAAK,KAAK,UAAW,MAAK,UAAU,eAAeA,EAAC,KAAK,KAAK,UAAUA,EAAC,EAAE,cAAc;AAAA,IACpG;AAAA,IACA,mBAAmB,SAAUA,IAAG;AAC9B,eAASC,MAAK,KAAK,UAAW,MAAK,UAAUA,EAAC,aAAa,EAAE,eAAe,KAAK,UAAUA,EAAC,EAAE,WAAWD,EAAC;AAAA,IAC5G;AAAA,IACA,iBAAiB,SAAUA,IAAG;AAC5B,UAAIC,KAAID,GAAE;AACV,eAASE,MAAK,KAAK,UAAW,MAAK,UAAUA,EAAC,MAAMD,MAAK,KAAK,UAAUC,EAAC,EAAE,QAAQ;AAAA,IACrF;AAAA,EACF,CAAC,GAAG,EAAE,IAAI,aAAa;AAAA,IACrB,qBAAqB;AAAA,IACrB,aAAa;AAAA,EACf,CAAC,GAAG,EAAE,IAAI,YAAY,WAAY;AAChC,SAAK,QAAQ,gBAAgB,KAAK,cAAc,IAAI,EAAE,QAAQ,KAAK,GAAG,KAAK,WAAW,KAAK,WAAW;AAAA,EACxG,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,OAAO;AAAA,IAC7B,YAAY,SAAUF,IAAG;AACvB,QAAE,WAAW,MAAMA,EAAC,GAAG,KAAK,SAAS,CAAC,GAAG,KAAK,iBAAiB,CAAC,GAAG,KAAK,cAAc;AACtF,UAAIC,KAAI,EAAE,QAAQ,MAAM,GAAG;AAC3B,YAAM,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE,QAAQ,QAAQ,EAAE,QAAQ,SAAS,IAAI,EAAE,QAAQ,QAAQ,EAAE,MAAM,MAAM;AAAA,IACjI;AAAA,IACA,SAAS,WAAY;AACnB,aAAO,SAAS,KAAK;AAAA,IACvB;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,QAAQ,KAAK,KAAK,YAAY,QAAQ,QAAQ;AAAA,IACrD;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,UAAIC,IACFC,KAAI,EAAE,QAAQ,OAAO,OAAO,sBAAsB,GAClDC,KAAI,GACJ,IAAI,KAAK,iBAAiB,IAC1B,IAAI,KAAK,gBAAgBH,EAAC;AAC5B,WAAK,KAAK,oBAAoB,EAAE,QAAQ,OAAO,OAAO,kCAAkC,GAAG,KAAK,OAAOA,IAAGC,KAAI,GAAGA,KAAI,EAAE,QAAQA,KAAK,GAAEA,EAAC,EAAE,WAAW,KAAK,iBAAiB,EAAEA,EAAC,EAAE,SAAS,KAAK,mBAAmBE,MAAK,GAAG,EAAEF,EAAC,EAAE,KAAK;AAClO,UAAIE,GAAG,QAAO,KAAK,mBAAmB,EAAEA,IAAG,KAAK,oBAAoB,EAAE,QAAQ,OAAO,MAAM,sBAAsB,GAAGD,GAAE,YAAY,KAAK,iBAAiB,GAAGA,GAAE,YAAY,KAAK,iBAAiB,GAAGA;AAAA,IACpM;AAAA,IACA,eAAe,WAAY;AACzB,eAASF,MAAK,KAAK,OAAQ,MAAK,OAAO,eAAeA,EAAC,MAAM,KAAK,eAAe,KAAK,OAAOA,EAAC,EAAE,QAAQ,KAAK,OAAOA,EAAC,EAAE,QAAQ,QAAQ,KAAK,OAAOA,EAAC,EAAE,OAAO,GAAG,KAAK,OAAOA,EAAC,EAAE,QAAQ,QAAQ,GAAG,KAAK,OAAOA,EAAC,EAAE,QAAQ,IAAI,WAAW,KAAK,mBAAmB,IAAI,EAAE,IAAI,YAAY,KAAK,qBAAqB,IAAI;AACpT,WAAK,SAAS,CAAC;AACf,eAASC,KAAI,GAAGC,KAAI,KAAK,eAAe,QAAQD,KAAIC,IAAGD,KAAK,MAAK,eAAe,KAAK,eAAeA,EAAC,EAAE,QAAQ,KAAK,eAAeA,EAAC,EAAE,UAAU,IAAI;AACpJ,WAAK,iBAAiB,CAAC,GAAG,KAAK,oBAAoB;AAAA,IACrD;AAAA,IACA,kBAAkB,SAAUD,IAAGC,IAAGC,IAAGC,IAAG,GAAG;AACzC,UAAI,IAAIH,GAAE;AACV,WAAK,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE,UAAUA,IAAG,KAAK,OAAO,CAAC,EAAE,SAAS,KAAK,cAAc;AAAA,QAC1F,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAWG,KAAI,MAAM;AAAA,QACrB,WAAWF;AAAA,QACX,UAAU,KAAK,OAAO,CAAC,EAAE,QAAQ;AAAA,QACjC,SAAS,KAAK,OAAO,CAAC,EAAE;AAAA,MAC1B,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE,cAAcC,IAAG,KAAK,OAAO,CAAC,EAAE,QAAQ,GAAG,WAAW,KAAK,mBAAmB,IAAI,EAAE,GAAG,YAAY,KAAK,qBAAqB,IAAI;AAAA,IACtJ;AAAA,IACA,YAAY,WAAY;AACtB,aAAO,mBAAmB,KAAK,UAAU,SAAS,KAAK,CAAC,EAAE;AAAA,IAC5D;AAAA,IACA,eAAe,SAAUF,IAAG;AAC1B,UAAIC,KAAI,EAAE,QAAQ,OAAO,KAAKD,GAAE,aAAa,IAAIA,GAAE,SAAS,GAC1DE,KAAI,EAAE,QAAQ,OAAO,QAAQ,WAAWF,GAAE,SAAS;AACrD,MAAAC,GAAE,OAAO,KAAKA,GAAE,YAAYC,EAAC,GAAGF,GAAE,UAAUC,GAAE,QAAQD,GAAE,OAAOE,GAAE,YAAYF,GAAE,QAAQA,GAAE,SAASC,GAAE,YAAYD,GAAE,MAAME,GAAE,YAAYF,GAAE;AACxI,UAAIG,KAAI,KAAK,WAAW,IAAI,eAAe;AAC3C,aAAO,EAAE,SAAS,GAAGF,IAAG,SAAS,EAAE,SAAS,eAAe,EAAE,GAAGA,IAAG,aAAa,EAAE,SAAS,eAAe,EAAE,GAAGA,IAAG,YAAY,EAAE,SAAS,eAAe,EAAE,GAAGA,IAAG,cAAc,EAAE,SAAS,eAAe,EAAE,GAAGA,IAAG,SAAS,EAAE,SAAS,cAAc,EAAE,GAAGA,IAAGE,IAAGH,GAAE,UAAUA,GAAE,OAAO,GAAGC;AAAA,IACvR;AAAA,IACA,gBAAgB,SAAUD,IAAGC,IAAG;AAC9B,UAAIC,KAAI,KAAK,WAAW,IAAI,eAAe;AAC3C,QAAE,SAAS,IAAIF,IAAG,SAAS,EAAE,SAAS,eAAe,EAAE,IAAIA,IAAG,aAAa,EAAE,SAAS,eAAe,EAAE,IAAIA,IAAG,YAAY,EAAE,SAAS,eAAe,EAAE,IAAIA,IAAG,cAAc,EAAE,SAAS,eAAe,EAAE,IAAIA,IAAG,SAAS,EAAE,SAAS,cAAc,EAAE,IAAIA,IAAGE,IAAGD,EAAC;AAAA,IAC/P;AAAA,IACA,mBAAmB,SAAUD,IAAG;AAC9B,WAAK,QAAQ,GAAG,KAAK,cAAc,KAAK,OAAOA,GAAE,OAAO,GAAG,EAAE,QAAQ,SAAS,KAAK,YAAY,QAAQ,qCAAqC,GAAG,KAAK,oBAAoB,GAAG,KAAK,KAAK,QAAQ;AAAA,IAC/L;AAAA,IACA,qBAAqB,WAAY;AAC/B,WAAK,oBAAoB,GAAG,EAAE,QAAQ,YAAY,KAAK,YAAY,QAAQ,qCAAqC,GAAG,KAAK,cAAc,MAAM,KAAK,KAAK,SAAS;AAAA,IACjK;AAAA,IACA,gBAAgB,SAAUA,IAAG;AAC3B,UAAIC,IACFC,IACAC,IACA,GACA,IAAI,KAAK,mBACT,IAAI,KAAK,WAAWH,EAAC,GACrB,IAAI,EAAE;AACR,WAAKE,KAAI,GAAGC,KAAI,KAAK,eAAe,QAAQD,KAAIC,IAAGD,KAAK,MAAK,eAAe,KAAK,eAAeA,EAAC,EAAE,QAAQ,KAAK,eAAeA,EAAC,EAAE,QAAQ;AAC1I,WAAK,KAAK,iBAAiB,CAAC,GAAG,EAAE,aAAa,GAAE,YAAY,EAAE,UAAU;AACxE,eAAS,IAAI,GAAG,IAAI,GAAG,IAAK,cAAa,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,YAAYD,KAAI,EAAE,QAAQ,OAAO,MAAM,IAAI,CAAC,GAAG,IAAI,KAAK,cAAc;AAAA,QAC3H,OAAO,EAAE,CAAC,EAAE;AAAA,QACZ,MAAM,EAAE,CAAC,EAAE;AAAA,QACX,WAAWA;AAAA,QACX,UAAU,EAAE,CAAC,EAAE;AAAA,QACf,SAAS,EAAE,CAAC,EAAE;AAAA,MAChB,CAAC,GAAG,KAAK,eAAe,KAAK;AAAA,QAC3B,QAAQ;AAAA,QACR,UAAU,EAAE,CAAC,EAAE;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB,WAAY;AAC/B,UAAID,KAAI,KAAK,YAAY,aACvBC,KAAI,KAAK,kBACTC,KAAI,KAAK,YAAY,OAAO,YAAY;AAC1C,WAAK,eAAe,KAAK,YAAY,OAAO,GAAG,KAAK,kBAAkB,MAAM,MAAMA,KAAI,MAAM,MAAMF,OAAM,EAAE,QAAQ,SAAS,KAAK,mBAAmB,4BAA4B,GAAG,EAAE,QAAQ,SAAS,KAAK,mBAAmB,0BAA0B,IAAIA,OAAMC,OAAM,EAAE,QAAQ,SAAS,KAAK,mBAAmB,+BAA+B,GAAG,EAAE,QAAQ,SAAS,KAAK,mBAAmB,6BAA6B,IAAI,KAAK,kBAAkB,MAAM,UAAU,SAAS,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,aAAa;AAAA,IAC3f;AAAA,IACA,qBAAqB,WAAY;AAC/B,WAAK,kBAAkB,MAAM,UAAU,QAAQ,EAAE,QAAQ,YAAY,KAAK,mBAAmB,4BAA4B,GAAG,EAAE,QAAQ,YAAY,KAAK,mBAAmB,+BAA+B,GAAG,EAAE,QAAQ,YAAY,KAAK,mBAAmB,0BAA0B,GAAG,EAAE,QAAQ,YAAY,KAAK,mBAAmB,6BAA6B,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,aAAa;AAAA,IAChZ;AAAA,EACF,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,UAAU,EAAE,MAAM,OAAO;AAAA,IACzD,YAAY,SAAUD,IAAG;AACvB,WAAK,OAAOA,IAAG,KAAK,aAAaA,GAAE,OAAO,WAAW,KAAK,WAAW,OAAI,KAAK,aAAaA,GAAE,QAAQ,sBAAsB,EAAE,QAAQ,OAAO,OAAO,wBAAwB,KAAK,UAAU,IAAI,MAAM,KAAK,mBAAmB,OAAI,KAAK,KAAK,GAAG,YAAY,KAAK,aAAa,IAAI;AAAA,IACjR;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,KAAK,IAAI,YAAY,KAAK,aAAa,IAAI,GAAG,KAAK,eAAe,KAAK,WAAW,YAAY,KAAK,UAAU,GAAG,KAAK,aAAa;AAAA,IACzI;AAAA,IACA,eAAe,SAAUA,IAAG;AAC1B,aAAO,KAAK,cAAcA,GAAE,UAAUA,GAAE,WAAW,IAAI,MAAMA,GAAE,QAAQ,UAAU,KAAK,mBAAmBA,GAAE,QAAQ,SAAS,KAAK,KAAK,qBAAqB,EAAE,QAAQ,YAAY,KAAK,YAAY,6BAA6B,GAAG,KAAK,mBAAmB,UAAO,EAAE,QAAQ,SAAS,KAAK,YAAY,6BAA6B,GAAG,KAAK,mBAAmB,OAAK,KAAK,WAAW,aAAaA,GAAE,QAAQ,SAAS,IAAI,gDAAgDA,GAAE,UAAU,kBAAkB,MAAM,WAAWA,GAAE,OAAO,WAAWA,GAAE,QAAQA,GAAE,WAAW,KAAK,WAAW,MAAI,KAAK,WAAW,MAAM,aAAa,cAAc,KAAK,WAAW,OAAI,KAAK,WAAW,MAAM,aAAa,WAAW,QAAQ;AAAA,IACjrB;AAAA,IACA,gBAAgB,SAAUA,IAAG;AAC3B,UAAIC,KAAI,KAAK,KAAK,mBAAmBD,EAAC,GACpCE,KAAI,KAAK;AACX,aAAO,KAAK,eAAe,KAAK,aAAaA,GAAE,MAAM,aAAa,YAAY,EAAE,QAAQ,YAAYA,IAAGD,EAAC,IAAI;AAAA,IAC9G;AAAA,IACA,aAAa,WAAY;AACvB,aAAO,KAAK,cAAc,EAAE,QAAQ,SAAS,KAAK,YAAY,4BAA4B,GAAG;AAAA,IAC/F;AAAA,IACA,aAAa,WAAY;AACvB,aAAO,KAAK,cAAc,EAAE,QAAQ,YAAY,KAAK,YAAY,4BAA4B,GAAG;AAAA,IAClG;AAAA,IACA,aAAa,WAAY;AACvB,WAAK,eAAe,KAAK,WAAW,MAAM,aAAa;AAAA,IACzD;AAAA,EACF,CAAC,GAAG,EAAE,cAAc,EAAE,QAAQ,OAAO;AAAA,IACnC,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,UAAU,CAAC;AAAA,MACX,SAAS,CAAC;AAAA,MACV,WAAW,CAAC;AAAA,MACZ,QAAQ,CAAC;AAAA,MACT,QAAQ,CAAC;AAAA,MACT,cAAc,CAAC;AAAA,IACjB;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,eAASC,MAAK,KAAK,QAAS,MAAK,QAAQ,eAAeA,EAAC,KAAKD,GAAEC,EAAC,MAAMD,GAAEC,EAAC,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK,QAAQA,EAAC,GAAGD,GAAEC,EAAC,CAAC;AAChH,WAAK,gBAAgB,qBAAqB,EAAE,QAAQ,UAAU,WAAW,KAAK,MAAMD,EAAC;AAAA,IACvF;AAAA,IACA,iBAAiB,SAAUA,IAAG;AAC5B,aAAO,CAAC;AAAA,QACN,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,KAAK,SAASA,IAAG,KAAK,QAAQ,QAAQ;AAAA,QACrD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,GAAG;AAAA,QACD,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,KAAK,QAAQA,IAAG,KAAK,QAAQ,OAAO;AAAA,QACnD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,GAAG;AAAA,QACD,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,KAAK,UAAUA,IAAG,KAAK,QAAQ,SAAS;AAAA,QACvD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,GAAG;AAAA,QACD,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,KAAK,OAAOA,IAAG,KAAK,QAAQ,MAAM;AAAA,QACjD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,GAAG;AAAA,QACD,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,KAAK,OAAOA,IAAG,KAAK,QAAQ,MAAM;AAAA,QACjD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,GAAG;AAAA,QACD,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,KAAK,aAAaA,IAAG,KAAK,QAAQ,YAAY;AAAA,QAC7D,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAUA,IAAG;AACvB,aAAO,CAAC;AAAA,QACN,SAASA,GAAE;AAAA,QACX,OAAO,EAAE,UAAU,KAAK,QAAQ,OAAO;AAAA,QACvC,MAAM,EAAE,UAAU,KAAK,QAAQ,OAAO;AAAA,QACtC,UAAUA,GAAE;AAAA,QACZ,SAASA;AAAA,MACX,GAAG;AAAA,QACD,SAASA,GAAE;AAAA,QACX,OAAO,EAAE,UAAU,KAAK,QAAQ,KAAK;AAAA,QACrC,MAAM,EAAE,UAAU,KAAK,QAAQ,KAAK;AAAA,QACpC,UAAUA,GAAE;AAAA,QACZ,SAASA;AAAA,MACX,GAAG;AAAA,QACD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,QACxC,MAAM,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,QACvC,UAAU,KAAK;AAAA,QACf,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAUA,IAAG;AACvB,QAAE,WAAW,MAAMA,EAAC;AACpB,eAASC,MAAK,KAAK,OAAQ,MAAK,OAAO,eAAeA,EAAC,KAAKD,GAAE,eAAeC,EAAC,KAAK,KAAK,OAAOA,EAAC,EAAE,QAAQ,WAAWD,GAAEC,EAAC,CAAC;AAAA,IAC3H;AAAA,EACF,CAAC,GAAG,EAAE,cAAc,EAAE,QAAQ,OAAO;AAAA,IACnC,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,QACJ,qBAAqB;AAAA,UACnB,WAAW;AAAA,UACX,MAAM;AAAA,UACN,WAAW;AAAA,UACX,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,MAAM;AAAA,MACN,cAAc;AAAA,IAChB;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,MAAAA,GAAE,SAAS,WAAWA,GAAE,KAAK,wBAAwBA,GAAE,KAAK,sBAAsB,KAAK,QAAQ,KAAK,sBAAsBA,GAAE,KAAK,sBAAsB,EAAE,OAAO,CAAC,GAAG,KAAK,QAAQ,KAAK,qBAAqBA,GAAE,KAAK,mBAAmB,IAAIA,GAAE,WAAWA,GAAE,SAAS,EAAE,OAAO,CAAC,GAAG,KAAK,QAAQ,QAAQA,GAAE,MAAM,IAAIA,GAAE,SAASA,GAAE,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,QAAQ,MAAMA,GAAE,IAAI,IAAI,KAAK,gBAAgB,qBAAqB,EAAE,QAAQ,UAAU,WAAW,KAAK,MAAMA,EAAC,GAAG,KAAK,wBAAwB;AAAA,IACpe;AAAA,IACA,iBAAiB,SAAUA,IAAG;AAC5B,UAAIC,KAAI,KAAK,QAAQ;AACrB,aAAO,CAAC;AAAA,QACN,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,YAAY,KAAKD,IAAG;AAAA,UACjC,cAAcC;AAAA,UACd,qBAAqB,KAAK,QAAQ,KAAK;AAAA,UACvC,MAAM,KAAK,QAAQ;AAAA,QACrB,CAAC;AAAA,QACD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,GAAG;AAAA,QACD,SAAS,KAAK,QAAQ;AAAA,QACtB,SAAS,IAAI,EAAE,YAAY,OAAOD,IAAG;AAAA,UACnC,cAAcC;AAAA,QAChB,CAAC;AAAA,QACD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,UAAIC,KAAI,CAAC;AAAA,QACP,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ,KAAK;AAAA,QAC7C,MAAM,EAAE,UAAU,KAAK,QAAQ,QAAQ,KAAK;AAAA,QAC5C,UAAU,KAAK;AAAA,QACf,SAAS;AAAA,MACX,GAAG;AAAA,QACD,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ,OAAO;AAAA,QAC/C,MAAM,EAAE,UAAU,KAAK,QAAQ,QAAQ,OAAO;AAAA,QAC9C,UAAU,KAAK;AAAA,QACf,SAAS;AAAA,MACX,CAAC;AACD,aAAOD,GAAE,mBAAmBC,GAAE,KAAK;AAAA,QACjC,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ,SAAS;AAAA,QACjD,MAAM,EAAE,UAAU,KAAK,QAAQ,QAAQ,SAAS;AAAA,QAChD,UAAU,KAAK;AAAA,QACf,SAAS;AAAA,MACX,CAAC,GAAGA;AAAA,IACN;AAAA,IACA,YAAY,SAAUD,IAAG;AACvB,UAAIC,KAAI,EAAE,QAAQ,UAAU,WAAW,KAAK,MAAMD,EAAC;AACnD,aAAO,KAAK,eAAe,GAAG,KAAK,QAAQ,aAAa,GAAG,wBAAwB,KAAK,gBAAgB,IAAI,GAAGC;AAAA,IACjH;AAAA,IACA,eAAe,WAAY;AACzB,WAAK,QAAQ,aAAa,IAAI,wBAAwB,KAAK,gBAAgB,IAAI,GAAG,EAAE,QAAQ,UAAU,cAAc,KAAK,IAAI;AAAA,IAC/H;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,QAAQ,MAAM,KAAK,YAAY,QAAQ,aAAa,GAAG,EAAE,QAAQ,UAAU,QAAQ,KAAK,IAAI;AAAA,IACnG;AAAA,IACA,OAAO,WAAY;AACjB,WAAK,YAAY,QAAQ,KAAK,GAAG,KAAK,eAAe,KAAK,YAAY,QAAQ,QAAQ;AAAA,IACxF;AAAA,IACA,iBAAiB,WAAY;AAC3B,WAAK,YAAY,QAAQ,gBAAgB,GAAG,KAAK,eAAe,KAAK,YAAY,QAAQ,QAAQ;AAAA,IACnG;AAAA,IACA,gBAAgB,WAAY;AAC1B,UAAID,IACFC,KAAI,KAAK,QAAQ,cACjBC,KAAI,MAAMD,GAAE,UAAU,EAAE;AAC1B,WAAK,QAAQ,SAASD,KAAI,KAAK,OAAO,EAAE,YAAY,KAAK,IAAI,EAAE,QAAQE,KAAI,EAAE,QAAQ,YAAYF,IAAG,kBAAkB,IAAI,EAAE,QAAQ,SAASA,IAAG,kBAAkB,GAAGA,GAAE,aAAa,SAASE,KAAI,EAAE,UAAU,KAAK,QAAQ,QAAQ,OAAO,EAAE,UAAU,KAAK,QAAQ,QAAQ,YAAY,IAAI,KAAK,QAAQ,WAAWF,KAAI,KAAK,OAAO,EAAE,YAAY,OAAO,IAAI,EAAE,QAAQE,KAAI,EAAE,QAAQ,YAAYF,IAAG,kBAAkB,IAAI,EAAE,QAAQ,SAASA,IAAG,kBAAkB,GAAGA,GAAE,aAAa,SAASE,KAAI,EAAE,UAAU,KAAK,QAAQ,QAAQ,SAAS,EAAE,UAAU,KAAK,QAAQ,QAAQ,cAAc;AAAA,IAC1jB;AAAA,EACF,CAAC,GAAG,EAAE,YAAY,OAAO,EAAE,QAAQ,OAAO;AAAA,IACxC,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,YAAY,SAAUF,IAAGC,IAAG;AAC1B,UAAI,EAAE,QAAQ,UAAU,WAAW,KAAK,MAAMD,EAAC,GAAG,EAAE,WAAW,MAAMC,EAAC,GAAG,KAAK,gBAAgBA,GAAE,cAAc,EAAE,KAAK,yBAAyB,EAAE,cAAe,OAAM,IAAI,MAAM,+CAA+C;AAC9N,WAAK,sBAAsB,CAAC,GAAG,KAAK,OAAO,EAAE,YAAY,KAAK;AAC9D,UAAIC,KAAI,EAAE,QAAQ,MAAM,GAAG;AAC3B,YAAM,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE,YAAY,KAAK,QAAQ,EAAE,QAAQ,SAAS,IAAI,EAAE,YAAY,KAAK,QAAQ,EAAE,MAAM,MAAM;AAAA,IACnJ;AAAA,IACA,QAAQ,WAAY;AAClB,OAAC,KAAK,YAAY,KAAK,oBAAoB,MAAM,KAAK,KAAK,WAAW;AAAA,QACpE,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,WAAW;AAAA,QACzC,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,EAAE,QAAQ,UAAU,OAAO,KAAK,IAAI,GAAG,KAAK,cAAc,GAAG,YAAY,KAAK,kBAAkB,IAAI,EAAE,GAAG,eAAe,KAAK,mBAAmB,IAAI;AAAA,IAC1J;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,aAAa,KAAK,cAAc,IAAI,YAAY,KAAK,kBAAkB,IAAI,EAAE,IAAI,eAAe,KAAK,mBAAmB,IAAI,GAAG,EAAE,QAAQ,UAAU,QAAQ,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,UAAU;AAAA,QAChN,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,KAAK,KAAK,YAAY;AAAA,QACxB,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,IACA,UAAU,WAAY;AACpB,UAAIF,KAAI,KAAK;AACb,MAAAA,OAAMA,GAAE,aAAa,EAAE,MAAM,GAAG,KAAK,cAAc,UAAU,KAAK,kBAAkB,IAAI,GAAG,KAAK,WAAW,IAAI,EAAE,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,SAAS,cAAc;AAAA,QACpK,MAAM,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,QAC7C,SAAS,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,MAClD,CAAC,GAAGA,GAAE,eAAe,KAAK,UAAU,KAAK,eAAe,GAAG,KAAK,KAAK,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,iBAAiB,KAAK,cAAc,IAAI,EAAE,GAAG,EAAE,KAAK,MAAM,YAAY,KAAK,gBAAgB,IAAI;AAAA,IAC3P;AAAA,IACA,aAAa,WAAY;AACvB,WAAK,SAAS,KAAK,cAAc,UAAU,KAAK,mBAAmB,IAAI,GAAG,KAAK,sBAAsB,CAAC,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK,WAAW,MAAM,KAAK,KAAK,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,iBAAiB,KAAK,cAAc,IAAI,EAAE,IAAI,EAAE,KAAK,MAAM,YAAY,KAAK,gBAAgB,IAAI;AAAA,IAC5V;AAAA,IACA,cAAc,WAAY;AACxB,WAAK,cAAc,UAAU,SAAUA,IAAG;AACxC,aAAK,aAAaA,EAAC;AAAA,MACrB,GAAG,IAAI;AAAA,IACT;AAAA,IACA,MAAM,WAAY;AAChB,UAAIA,KAAI,IAAI,EAAE,WAAW;AACzB,WAAK,cAAc,UAAU,SAAUC,IAAG;AACxC,QAAAA,GAAE,WAAWD,GAAE,SAASC,EAAC,GAAGA,GAAE,SAAS;AAAA,MACzC,CAAC,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,QAAQ;AAAA,QACtC,QAAQD;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,cAAc,SAAUA,IAAG;AACzB,UAAIC,KAAI,EAAE,KAAK,MAAMD,EAAC;AACtB,WAAK,oBAAoBC,EAAC,MAAMD,cAAa,EAAE,YAAYA,cAAa,EAAE,WAAWA,cAAa,EAAE,YAAY,KAAK,oBAAoBC,EAAC,IAAI;AAAA,QAC5I,SAAS,EAAE,WAAW,aAAaD,GAAE,WAAW,CAAC;AAAA,MACnD,IAAIA,cAAa,EAAE,SAAS,KAAK,oBAAoBC,EAAC,IAAI;AAAA,QACxD,QAAQ,EAAE,WAAW,YAAYD,GAAE,UAAU,CAAC;AAAA,QAC9C,QAAQA,GAAE,UAAU;AAAA,MACtB,KAAKA,cAAa,EAAE,UAAUA,cAAa,EAAE,kBAAkB,KAAK,oBAAoBC,EAAC,IAAI;AAAA,QAC3F,QAAQ,EAAE,WAAW,YAAYD,GAAE,UAAU,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,IACA,iBAAiB,WAAY;AAC3B,aAAO;AAAA,QACL,MAAM,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,QAC7C,SAAS,EAAE,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,MAClD;AAAA,IACF;AAAA,IACA,gBAAgB,WAAY;AAC1B,WAAK,SAAS,cAAc,KAAK,gBAAgB,CAAC;AAAA,IACpD;AAAA,IACA,cAAc,SAAUA,IAAG;AACzB,UAAIC,KAAI,EAAE,KAAK,MAAMD,EAAC;AACtB,MAAAA,GAAE,SAAS,OAAI,KAAK,oBAAoB,eAAeC,EAAC,MAAMD,cAAa,EAAE,YAAYA,cAAa,EAAE,WAAWA,cAAa,EAAE,YAAYA,GAAE,WAAW,KAAK,oBAAoBC,EAAC,EAAE,OAAO,IAAID,cAAa,EAAE,UAAUA,GAAE,UAAU,KAAK,oBAAoBC,EAAC,EAAE,MAAM,GAAGD,GAAE,UAAU,KAAK,oBAAoBC,EAAC,EAAE,MAAM,MAAMD,cAAa,EAAE,UAAUA,cAAa,EAAE,iBAAiBA,GAAE,UAAU,KAAK,oBAAoBC,EAAC,EAAE,MAAM,GAAGD,GAAE,KAAK,iBAAiB;AAAA,QAChc,OAAOA;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,kBAAkB,SAAUA,IAAG;AAC7B,UAAIC,IACFC,IACAC,KAAIH,GAAE,SAASA,GAAE,UAAUA;AAC7B,WAAK,aAAaG,EAAC,GAAG,KAAK,QAAQ,SAASD,KAAI,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,QAAQ,IAAI,GAAGC,GAAE,QAAQ,OAAOD,KAAI,KAAK,QAAQ,wBAAwBD,KAAI,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,QAAQ,mBAAmB,GAAGA,GAAE,kBAAkBA,GAAE,QAAQE,GAAE,QAAQ,OAAOF,GAAE,YAAYE,GAAE,QAAQ,YAAYA,GAAE,QAAQ,WAAW,EAAE,OAAO,CAAC,GAAGA,GAAE,OAAO,GAAGA,GAAE,QAAQ,UAAUF,KAAIE,cAAa,EAAE,UAAUA,GAAE,WAAWA,GAAE,QAAQ,OAAO,GAAGA,GAAE,SAAS,OAAO,GAAGA,GAAE,GAAG,WAAW,KAAK,gBAAgB,EAAE,GAAG,aAAa,KAAK,cAAc,IAAI,EAAE,GAAG,iBAAiB,KAAK,cAAc,IAAI,EAAE,GAAG,YAAY,KAAK,kBAAkB,IAAI,EAAE,GAAG,eAAe,KAAK,kBAAkB,IAAI,KAAKA,GAAE,QAAQ,OAAO;AAAA,IAC/pB;AAAA,IACA,mBAAmB,SAAUH,IAAG;AAC9B,UAAIC,KAAID,GAAE,SAASA,GAAE,UAAUA;AAC/B,MAAAC,GAAE,SAAS,OAAIA,GAAE,WAAWA,GAAE,QAAQ,QAAQ,GAAG,OAAOA,GAAE,QAAQ,SAAS,OAAOA,GAAE,QAAQ,UAAU,KAAK,yBAAyBA,cAAa,EAAE,SAAS,KAAK,uBAAuBA,EAAC,KAAKA,GAAE,SAASA,GAAE,QAAQ,eAAe,GAAG,OAAOA,GAAE,QAAQ,mBAAmBA,cAAa,EAAE,UAAUA,GAAE,SAAS,QAAQ,GAAGA,GAAE,IAAI,WAAW,KAAK,kBAAkB,IAAI,EAAE,IAAI,aAAa,KAAK,cAAc,IAAI,EAAE,IAAI,iBAAiB,KAAK,cAAc,IAAI,EAAE,IAAI,YAAY,KAAK,kBAAkB,IAAI,EAAE,IAAI,eAAe,KAAK,kBAAkB,IAAI,KAAKA,GAAE,QAAQ,QAAQ;AAAA,IACljB;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,WAAK,SAAS,eAAeA,GAAE,MAAM;AAAA,IACvC;AAAA,IACA,kBAAkB,SAAUA,IAAG;AAC7B,UAAIC,KAAID,GAAE;AACV,MAAAC,GAAE,SAAS,MAAI,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,UAAU;AAAA,QACnD,OAAOA;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAIC,KAAID,GAAE,cAAc,eAAe,CAAC,GACtCE,KAAI,KAAK,KAAK,uBAAuBD,EAAC,GACtCE,KAAI,KAAK,KAAK,mBAAmBD,EAAC;AACpC,MAAAF,GAAE,OAAO,UAAUG,EAAC;AAAA,IACtB;AAAA,IACA,qBAAqB,WAAY;AAC/B,aAAO,MAAM,KAAK,cAAc,UAAU,EAAE;AAAA,IAC9C;AAAA,EACF,CAAC,GAAG,EAAE,YAAY,SAAS,EAAE,QAAQ,OAAO;AAAA,IAC1C,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,YAAY,SAAUH,IAAGC,IAAG;AAC1B,UAAI,EAAE,QAAQ,UAAU,WAAW,KAAK,MAAMD,EAAC,GAAG,EAAE,KAAK,WAAW,MAAMC,EAAC,GAAG,KAAK,mBAAmB,KAAK,QAAQ,cAAc,EAAE,KAAK,4BAA4B,EAAE,cAAe,OAAM,IAAI,MAAM,+CAA+C;AACpP,WAAK,OAAO,EAAE,YAAY,OAAO;AACjC,UAAIC,KAAI,EAAE,QAAQ,MAAM,GAAG;AAC3B,YAAM,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,SAASA,GAAE,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE,YAAY,OAAO,QAAQ,EAAE,QAAQ,SAAS,IAAI,EAAE,YAAY,OAAO,QAAQ,EAAE,MAAM,MAAM;AAAA,IACvJ;AAAA,IACA,QAAQ,WAAY;AAClB,OAAC,KAAK,YAAY,KAAK,oBAAoB,MAAM,KAAK,KAAK,WAAW;AAAA,QACpE,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,aAAa;AAAA,QAC3C,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,EAAE,QAAQ,UAAU,OAAO,KAAK,IAAI,GAAG,KAAK,iBAAiB,GAAG,YAAY,KAAK,oBAAoB,IAAI,EAAE,GAAG,eAAe,KAAK,qBAAqB,IAAI;AAAA,IACjK;AAAA,IACA,SAAS,WAAY;AACnB,WAAK,aAAa,KAAK,iBAAiB,IAAI,YAAY,KAAK,oBAAoB,IAAI,EAAE,IAAI,eAAe,KAAK,qBAAqB,IAAI,GAAG,EAAE,QAAQ,UAAU,QAAQ,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,EAAE,KAAK,MAAM,YAAY;AAAA,QACzN,SAAS,KAAK;AAAA,MAChB,CAAC,GAAG,KAAK,KAAK,YAAY;AAAA,QACxB,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AAAA,IACA,UAAU,WAAY;AACpB,UAAIF,KAAI,KAAK;AACb,MAAAA,OAAMA,GAAE,aAAa,EAAE,MAAM,GAAG,KAAK,iBAAiB,UAAU,KAAK,oBAAoB,IAAI,GAAG,KAAK,iBAAiB,IAAI,EAAE,WAAW,GAAG,KAAK,WAAW,IAAI,EAAE,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,SAAS,cAAc;AAAA,QACnN,MAAM,EAAE,UAAU,KAAK,SAAS,OAAO,QAAQ;AAAA,MACjD,CAAC,GAAG,KAAK,KAAK,GAAG,aAAa,KAAK,cAAc,IAAI;AAAA,IACvD;AAAA,IACA,aAAa,WAAY;AACvB,WAAK,SAAS,KAAK,iBAAiB,UAAU,KAAK,qBAAqB,IAAI,GAAG,KAAK,iBAAiB,MAAM,KAAK,SAAS,QAAQ,GAAG,KAAK,WAAW,MAAM,KAAK,KAAK,IAAI,aAAa,KAAK,cAAc,IAAI;AAAA,IAC9M;AAAA,IACA,cAAc,WAAY;AACxB,WAAK,eAAe,UAAU,SAAUA,IAAG;AACzC,aAAK,iBAAiB,SAASA,EAAC,GAAGA,GAAE,KAAK,kBAAkB;AAAA,UAC1D,OAAOA;AAAA,QACT,CAAC;AAAA,MACH,GAAG,IAAI;AAAA,IACT;AAAA,IACA,MAAM,WAAY;AAChB,WAAK,KAAK,KAAK,EAAE,KAAK,MAAM,SAAS;AAAA,QACnC,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AAAA,IACA,iBAAiB,WAAY;AAC3B,WAAK,iBAAiB,UAAU,SAAUA,IAAG;AAC3C,aAAK,aAAa;AAAA,UAChB,OAAOA;AAAA,QACT,CAAC;AAAA,MACH,GAAG,IAAI,GAAG,KAAK,KAAK;AAAA,IACtB;AAAA,IACA,oBAAoB,SAAUA,IAAG;AAC/B,OAACA,GAAE,SAASA,GAAE,UAAUA,IAAG,GAAG,SAAS,KAAK,cAAc,IAAI;AAAA,IAChE;AAAA,IACA,qBAAqB,SAAUA,IAAG;AAChC,UAAIC,KAAID,GAAE,SAASA,GAAE,UAAUA;AAC/B,MAAAC,GAAE,IAAI,SAAS,KAAK,cAAc,IAAI,GAAG,KAAK,eAAe,YAAYA,EAAC;AAAA,IAC5E;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,UAAIC,KAAID,GAAE,SAASA,GAAE,UAAUA;AAC/B,WAAK,iBAAiB,YAAYC,EAAC,GAAG,KAAK,eAAe,SAASA,EAAC,GAAGA,GAAE,KAAK,SAAS;AAAA,IACzF;AAAA,IACA,cAAc,SAAUD,IAAG;AACzB,WAAK,SAAS,eAAeA,GAAE,MAAM;AAAA,IACvC;AAAA,IACA,qBAAqB,WAAY;AAC/B,aAAO,MAAM,KAAK,iBAAiB,UAAU,EAAE;AAAA,IACjD;AAAA,EACF,CAAC;AACH,EAAE,QAAQ,QAAQ;;;AChtDlB,qBAAyC;AACzC,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,OAAO,gBAAgB,MAAM,KAAK;AAChC,UAAM,WAAW;AACjB,QAAI,QAAQ,KAAK;AACf,iBAAW,KAAK,KAAK;AACnB,YAAI,IAAI,eAAe,CAAC,GAAG;AACzB,cAAI,OAAO,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,aAAa,QAAQ;AAC1D,qBAAS,CAAC,IAAI,IAAI,CAAC;AAAA,UACrB,OAAO;AACL,iBAAK,gBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,kBAAkB,MAAM;AAClC,SAAK,OAAO;AACZ,SAAK,cAAc;AAEnB,SAAK,YAAY;AAEjB,SAAK,YAAY,IAAI,aAAa;AAElC,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,sBAAsB,IAAI,aAAa;AAC5C,SAAK,mBAAmB,IAAI,wBAAwB,gBAAgB;AAAA,EACtE;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB,KAAK;AAE3B,QAAI,QAAQ,KAAK,WAAW;AAC1B,sBAAgB,gBAAgB,0BAAW,KAAK,SAAS;AAAA,IAC3D;AAEA,SAAK,cAAc,IAAI,uBAAQ,KAAK,KAAK,WAAW;AAEpD,SAAK,iBAAiB,OAAO,EAAE,WAAW,KAAK,WAAW;AAE1D,UAAM,MAAM,KAAK,iBAAiB,OAAO;AAEzC,QAAI,GAAG,oBAAK,MAAM,SAAS,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,eAAe,CAAC,CAAC;AACzF,QAAI,GAAG,oBAAK,MAAM,QAAQ,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,cAAc,CAAC,CAAC;AACvF,QAAI,GAAG,oBAAK,MAAM,SAAS,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,eAAe,CAAC,CAAC;AACzF,QAAI,GAAG,oBAAK,MAAM,WAAW,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,aAAa,CAAC,CAAC;AACzF,QAAI,GAAG,oBAAK,MAAM,UAAU,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,YAAY,CAAC,CAAC;AACvF,QAAI,GAAG,oBAAK,MAAM,WAAW,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,iBAAiB,CAAC,CAAC;AAC7F,QAAI,GAAG,oBAAK,MAAM,UAAU,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,gBAAgB,CAAC,CAAC;AAC3F,QAAI,GAAG,oBAAK,MAAM,YAAY,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,kBAAkB,CAAC,CAAC;AAC/F,QAAI,GAAG,oBAAK,MAAM,YAAY,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,kBAAkB,CAAC,CAAC;AAC/F,QAAI,GAAG,oBAAK,MAAM,UAAU,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,gBAAgB,CAAC,CAAC;AAC3F,QAAI,GAAG,oBAAK,MAAM,aAAa,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,mBAAmB,CAAC,CAAC;AACjG,QAAI,GAAG,oBAAK,MAAM,YAAY,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,kBAAkB,CAAC,CAAC;AAC/F,QAAI,GAAG,oBAAK,MAAM,eAAe,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,qBAAqB,CAAC,CAAC;AACrG,QAAI,GAAG,oBAAK,MAAM,eAAe,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,qBAAqB,CAAC,CAAC;AAErG,SAAK,UAAU,KAAK,KAAK,WAAW;AAAA,EACtC;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,OAAO,EAAE,cAAc,KAAK,WAAW;AAAA,EAC/D;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,gBAAgB,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACnI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,aAAa,CAAC,GAAG,sBAAsB,aAAa;AAAA,QACpD,WAAW,CAAC,GAAG,oBAAoB,WAAW;AAAA,MAChD;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,eAAe;AAAA,QACf,cAAc;AAAA,QACd,eAAe;AAAA,QACf,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,QAClB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,oBAAoB;AAAA,MACnC,SAAS,CAAC,aAAa;AAAA,MACvB,SAAS,CAAC,oBAAoB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,aAAa;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,SAAS,CAAC,oBAAoB;AAAA,MAC9B,cAAc,CAAC,oBAAoB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["t", "e", "i", "o", "a"]}