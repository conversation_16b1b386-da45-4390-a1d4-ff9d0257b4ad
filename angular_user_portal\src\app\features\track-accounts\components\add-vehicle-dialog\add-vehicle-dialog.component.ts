import { NgStyle } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { vehicleLicensePlateSubClassOptions } from '@proxy/vehicles/license-plates';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-add-vehicle-dialog',
  standalone: true,
  templateUrl: `./add-vehicle-dialog.component.html`,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatButton,
    LanguagePipe,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
    MatRadioGroup,
    MatRadioButton,
  ],
})
export class AddVehicleDialogComponent {
  data = inject<FormGroup>(MAT_DIALOG_DATA);

  colors = signal(colors);
  public dialogRef = inject(MatDialogRef<AddVehicleDialogComponent>);
  private _formBuilder = inject(FormBuilder);

  VehicleLicensePlateSubClass = vehicleLicensePlateSubClassOptions;
  form = this._formBuilder.group({
    licensePlateSubClass: this._formBuilder.control('', Validators.required),
    licensePlateSerial: this._formBuilder.control('', Validators.required),
    consumptionRate: this._formBuilder.control(1, Validators.required),
    color: this._formBuilder.control('#000000', Validators.required),
    needsTrackingDevice: this._formBuilder.control(false),
  });

  ngOnInit(): void {
    if (this.data) {
      this.form.setValue(this.data.getRawValue());
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }
  save() {
    this.dialogRef.close(this.form);
  }
}
