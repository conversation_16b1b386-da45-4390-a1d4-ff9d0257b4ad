import { CommonModule, NgStyle } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import { changeNodeDto, MapComponent } from '@shared/components/map/map.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { colorToHex } from '@shared/functions/hex-to-color';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-route-create',
  standalone: true,
  templateUrl: './route-create.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    MapComponent,
    LanguagePipe,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
  ],
})
export class RouteCreateComponent implements OnInit {
  draw = signal<any>({ polyline: true });
  shawDraw = signal(false);

  private fb = inject(FormBuilder);
  private routeService = inject(RouteService);
  private router = inject(Router);

  colors = signal(colors);
  form: FormGroup = this.fb.group({
    name: this.fb.control('', Validators.required),
    hexColor: this.fb.control('', Validators.required),
    line: this.fb.control('', Validators.required),
    stopPoints: [[]],
  });
  nodes$ = signal(null);

  ngOnInit(): void {}

  onNodeChange(event: changeNodeDto) {
    const temp = event.event.layer._latlngs.map(latLng => {
      const { lat, lng } = latLng;
      return { longitudeX: lng, latitudeY: lat };
    });
    this.form.controls['line'].setValue(temp);

    this.draw.set({ polyline: true });
  }

  save() {
    const obj = { ...this.form.value };
    obj.hexColor = colorToHex(obj.hexColor);
    this.routeService.create(obj).subscribe(res => {
      this.router.navigate(['/main', 'routes']);
    });
  }

  close() {
    this.router.navigate(['/main', 'routes']);
  }

  showLine() {
    this.draw.set({ polyline: true });
    this.shawDraw.set(true);
  }
}
