import type { CreateExitingZoneAlertDefinitionDto, ExitingZoneAlertDefinitionDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { GeoZoneDetailsDto } from '../../../geo-zones/models';

@Injectable({
  providedIn: 'root',
})
export class ExitingZoneAlertDefinitionService {
  apiName = 'Default';
  

  create = (input: CreateExitingZoneAlertDefinitionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/exitingZoneAlertDefinition',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ExitingZoneAlertDefinitionDto>({
      method: 'GET',
      url: `/api/app/exitingZoneAlertDefinition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getGeoZones = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<GeoZoneDetailsDto>>({
      method: 'GET',
      url: `/api/app/exitingZoneAlertDefinition/${id}/geoZones`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ExitingZoneAlertDefinitionDto>>({
      method: 'GET',
      url: '/api/app/exitingZoneAlertDefinition',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
