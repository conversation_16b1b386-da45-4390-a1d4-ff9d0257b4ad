<div class="flex justify-between items-center mt-4">
  <h3 class="text-lg font-semibold">{{ 'UserPortal:geoZone' | i18n }}</h3>
  <button mat-button color="warn" [routerLink]="['/main/alerts/add-geo-Zone']">
    {{ 'UserPortal:addGeoZone' | i18n }}
  </button>
</div>

<mat-accordion class="mt-4 scale-90">
  @for (geoZone of geoZones$(); track geoZone; let i = $index) {
  <app-geozone-accordion
    [geoZone]="geoZone"
    [opendgeoZones$]="opendgeoZones$"
    (getData)="this.getGeoZones()"
  />
  }
</mat-accordion>
