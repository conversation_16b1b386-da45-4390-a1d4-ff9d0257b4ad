<div class="p-6">
  <h2 class="mb-4 text-xl font-semibold text-center text-main_dark_blue">
    {{ 'UserPortal:Requests' | i18n }}
  </h2>
  <hr class="my-2" />

  <div class="space-y-4">
    @if (ides().size>0) {
    <button mat-flat-button color="primary" aria-label="Pay" (click)="paySelected()">
      {{ 'UserPortal:makePaymentSelected' | i18n }}
    </button>
    } @for (order of request(); track $index) {
    <mat-card class="p-4">
      <div class="flex justify-between gap-4 items-center">
        <div>
          @if (order.status=='Pending') {
          <mat-checkbox [checked]="ides().has(order.id)" (change)="toggle(order)"></mat-checkbox>
          }
          <img class="size-10" [src]="'/assets/images/sms/' + icons[order.type]" alt="" />
        </div>
        <div class="grid grid-cols-2 flex-grow">
          <div class="text-sm text-main_gray">{{ 'UserPortal:RequestType' | i18n }}</div>
          <div class="">{{ order.type | i18n }}</div>
          <div class="text-sm text-main_gray">{{ 'UserPortal:status' | i18n }}</div>
          <div class="">{{ order.status | i18n }}</div>
        </div>
      </div>
      @if (order.status=='Pending') {
      <div class="flex justify-between mt-2 space-x-2">
        <button mat-flat-button color="primary" aria-label="Pay" (click)="pay(order)">
          {{ 'UserPortal:makePayment' | i18n }}
        </button>
        <button mat-mini-fab color="warn" aria-label="Delete" (click)="remove(order)">
          <mat-icon>delete</mat-icon>
        </button>
      </div>
      }
    </mat-card>
    }
  </div>
</div>
