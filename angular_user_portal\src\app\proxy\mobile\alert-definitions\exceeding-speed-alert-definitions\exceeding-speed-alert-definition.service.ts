import type { CreateExceedingSpeedAlertDefinitionDto, ExceedingSpeedAlertDefinitionDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ExceedingSpeedAlertDefinitionService {
  apiName = 'Default';
  

  create = (input: CreateExceedingSpeedAlertDefinitionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/exceedingSpeedAlertDefinition',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ExceedingSpeedAlertDefinitionDto>({
      method: 'GET',
      url: `/api/app/exceedingSpeedAlertDefinition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ExceedingSpeedAlertDefinitionDto>>({
      method: 'GET',
      url: '/api/app/exceedingSpeedAlertDefinition',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
