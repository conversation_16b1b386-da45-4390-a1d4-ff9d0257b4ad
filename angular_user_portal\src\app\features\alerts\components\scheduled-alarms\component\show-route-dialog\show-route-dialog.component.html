<div class="p-6 text-sm bg-white rounded-lg shadow-lg flex flex-col h-full">
  <h2 class="font-semibold text-indigo-600 flex-grow-0">{{ 'UserPortal:lines' | i18n }}</h2>
  <div class="flex-grow">
    <hr class="my-3" />
    <div class="space-y-2">
      <mat-accordion class="mt-4">
        @for (route of routes$(); track route; let i = $index) {
        <app-route-accordion
          [route]="route"
          [opendroutes$]="opendroutes$"
          [openedNodes$]="openedNodes$"
          (getData)="this.GetRoutes()"
        />
        }
      </mat-accordion>
    </div>
  </div>
  <div class="flex justify-center gap-4 mt-4 text-sm flex-grow-0">
    <button mat-button mat-flat-button class="cancleButton" (click)="dialogRef.close()">
      {{ 'UserPortal:ok' | i18n }}
    </button>
  </div>
</div>
