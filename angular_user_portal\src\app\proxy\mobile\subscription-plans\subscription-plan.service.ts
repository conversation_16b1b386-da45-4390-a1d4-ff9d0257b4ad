import type { SubscriptionPlanDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { FeatureDto } from '../../volo/abp/feature-management/models';

@Injectable({
  providedIn: 'root',
})
export class SubscriptionPlanService {
  apiName = 'Default';
  

  getFeatures = (subscriptionPlanKey: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, FeatureDto[]>({
      method: 'GET',
      url: '/api/app/subscriptionPlan/features',
      params: { subscriptionPlanKey },
    },
    { apiName: this.apiName,...config });
  

  getList = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, SubscriptionPlanDto[]>({
      method: 'GET',
      url: '/api/app/subscriptionPlan',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
