import { NgClass } from '@angular/common';
import { Component, input, LOCALE_ID, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>abel } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import {
  MatNativeDateTimeModule,
  MatTimepickerModule,
  provideNativeDateTimeAdapter,
} from '@dhutaryan/ngx-mat-timepicker';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

const lang = 'en-US';

@Component({
  selector: 'alert-time-alert-type',
  standalone: true,
  templateUrl: `./time-alert-type.component.html`,
  imports: [
    MatLabel,
    MatFormField,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatChipsModule,
    MatTimepickerModule,
    MatNativeDateTimeModule,
    MatInputModule,
    NgClass,
  ],

  providers: [provideNativeDateTimeAdapter(), { provide: LOCALE_ID, useValue: lang }],
})
export class TimeAlertTypeComponent {
  alertForm = input.required<FormGroup>();

  weekDays = signal(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']);

  onTimeChange(controlName: string, event: any) {
    this.alertForm().get(controlName)?.setValue(event);
  }
  isDayActive(day: string): boolean {
    return this.alertForm().get('daysOfWeek')?.value.includes(day);
  }

  toggleDay(day: string): void {
    const daysOfWeekControl = this.alertForm().get('daysOfWeek');
    const selectedDays = daysOfWeekControl.value;
    const dayIndex = selectedDays.indexOf(day);
    if (dayIndex === -1) {
      daysOfWeekControl.setValue([...selectedDays, day]);
    } else {
      daysOfWeekControl.setValue(selectedDays.filter((_, i) => i !== dayIndex));
    }
  }
}
