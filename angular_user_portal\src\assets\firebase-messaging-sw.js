// Import and initialize the Firebase SDK
import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
import { getMessaging } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-sw.js';
import { firebaseConfig } from '../../firebase';

const firebaseApp = initializeApp(firebaseConfig);

const messaging = getMessaging(firebaseApp);

// Optional: Add event listeners for background messages
self.addEventListener('push', event => {
  const payload = event.data.json();
  const options = {
    body: payload.notification.body,
    icon: '/assets/icons/icon-72x72.png',
    badge: '/assets/icons/icon-72x72.png',
    data: payload.data,
  };

  event.waitUntil(self.registration.showNotification(payload.notification.title, options));
});

self.addEventListener('notificationclick', event => {
  event.notification.close();

  // This looks to see if the current is already open and focuses if it is
  event.waitUntil(
    clients.matchAll({ type: 'window' }).then(clientList => {
      for (const client of clientList) {
        if (client.url === '/' && 'focus' in client) {
          return client.focus();
        }
      }
      if (clients.openWindow) {
        return clients.openWindow('/');
      }
    })
  );
});
