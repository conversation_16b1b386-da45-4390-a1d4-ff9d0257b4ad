import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { IFormFile } from '../../microsoft/asp-net-core/http/models';
import type { IActionResult } from '../../microsoft/asp-net-core/mvc/models';
import type { IMobileIdentityUserAppService } from '../mobile-identity-users/models';

@Injectable({
  providedIn: 'root',
})
export class MobileIdentityUserService {
  apiName = 'Default';
  

  setProfilePicture = (userAppService: IMobileIdentityUserAppService, profilePictureFile: IFormFile, config?: Partial<Rest.Config>) =>
    this.restService.request<any, IActionResult>({
      method: 'POST',
      url: '/api/app/MobileIdentityUser/SetProfilePicture',
      body: profilePictureFile,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
