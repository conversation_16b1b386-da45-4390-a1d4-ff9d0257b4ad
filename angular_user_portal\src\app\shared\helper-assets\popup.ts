import { LanguageService } from '@ttwr-framework/ngx-main-visuals';

export const popup = (obj: {
  status: string;
  number: string;
  avg_speed: number | string;
  feul: number;
  ls: LanguageService;
}) => {
  return `
   <div class="text-start">
     <div>${obj.ls.translate('status')} : ${obj.status}</div> 
     <div>${obj.ls.translate('licensePlateSerial')} : ${obj.number}</div>
     <div>${obj.ls.translate('averageSpeed')} : ${obj.avg_speed} ${obj.ls.translate('Km/h')}</div>
     <div>${obj.ls.translate('consumptionRate')} : ${obj.feul} ${obj.ls.translate('L/h')}</div>
   </div>
   `;
};
