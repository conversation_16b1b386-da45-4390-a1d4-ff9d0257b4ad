import type { FullAuditedEntityDto } from '@abp/ng.core';
import type { RouteViewModelDto } from '../../routes/dtos/models';
import type { VehicleDto } from '../../vehicles/dtos/models';
import type { VehicleGroupDto } from '../../vehicle-groups/models';

export interface AddVehicleGroupsToTripTemplateDto {
  tripTemplateId?: string;
  vehicleGroupIds: string[];
}

export interface AddVehiclesToTripTemplateDto {
  tripTemplateId?: string;
  vehicleIds: string[];
}

export interface CreateTripTemplateDto {
  name?: string;
  routeIds: string[];
}

export interface TripTemplateDto extends FullAuditedEntityDto<string> {
  name?: string;
  trackAccountId?: string;
}

export interface TripTemplateViewModelDto extends FullAuditedEntityDto<string> {
  name?: string;
  trackAccountId?: string;
  routes: RouteViewModelDto[];
  vehicles: VehicleDto[];
  vehicleGroups: VehicleGroupDto[];
}
