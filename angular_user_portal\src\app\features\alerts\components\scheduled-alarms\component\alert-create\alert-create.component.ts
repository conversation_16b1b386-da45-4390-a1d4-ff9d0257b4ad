import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { GeoZoneDto, GeoZoneService } from '@proxy/mobile/geo-zones';
import { VehicleGroupDto, VehicleGroupService } from '@proxy/mobile/vehicle-groups';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';
import { AlertCreateService } from './alert-create.service';
import { AlertType } from './alert-type.enum';
import { SelectNotificationComponent } from './components/select-notification/select-notification.component';
import { SpeedAlertTypeComponent } from './components/speed-alert-type/speed-alert-type.component';
import { TimeAlertTypeComponent } from './components/time-alert-type/time-alert-type.component';
import { ZonesAlertTypeComponent } from './components/zones-alert-type/zones-alert-type.component';
import { FEATURES } from '@shared/constants/features-token';
import { PathAlertTypeComponent } from './components/path-alert-type/path-alert-type.component';
import { RouteService } from '@proxy/mobile/routes';
import { RouteDto } from '@proxy/mobile/routes/dtos';

@Component({
  selector: 'app-alert-create',
  standalone: true,
  templateUrl: './alert-create.component.html',
  styleUrls: ['./alert-create.scss'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRadioModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatChipsModule,

    LanguagePipe,
    SelectNotificationComponent,
    ZonesAlertTypeComponent,
    SpeedAlertTypeComponent,
    TimeAlertTypeComponent,
    PathAlertTypeComponent,
  ],
})
export class AlertCreateComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private alertCreateService = inject(AlertCreateService);
  public AlertType = AlertType;

  private vehicleService = inject(VehicleService);
  private vehicleGroupService = inject(VehicleGroupService);
  private geoZoneService = inject(GeoZoneService);
  private routeService = inject(RouteService);
  features = inject(FEATURES);

  withGroups = signal(false);
  alertForm = this.fb.group({
    alertType: [AlertType.EnterZone, Validators.required],
    vehicleIds: this.fb.control([]),
    vehicleGroupIds: this.fb.control([]),
    maxSpeed: [0],
    geoZoneIds: this.fb.control([]),
    routeIds: this.fb.control([]),
    name: [''],
    startTime: [''],
    endTime: [''],
    daysOfWeek: this.fb.control([]),
    notificationMethods: this.fb.control([]),
  });
  step = signal(1);
  availableVehicles$ = signal(new Map());
  availableGroups$ = signal(new Map());
  geoZones$ = signal<Map<string, GeoZoneDto>>(new Map());
  paths$ = signal<Map<string, RouteDto>>(new Map());

  alertTypes = [
    {
      key: AlertType.EnterZone,
      value: 'UserPortal:EnterZone',
      feature: 'GoTrack.Alerts.EnteringZoneAlert',
    },
    {
      key: AlertType.ExitZone,
      value: 'UserPortal:ExitZone',
      feature: 'GoTrack.Alerts.ExitingZoneAlert',
    },
    {
      key: AlertType.SpeedViolation,
      value: 'UserPortal:SpeedViolation',
      feature: 'GoTrack.Alerts.ExceedingSpeedAlert',
    },
    {
      key: AlertType.WorkTime,
      value: 'UserPortal:WorkTime',
      feature: 'GoTrack.Alerts.JobTimeAlert',
    },
    {
      key: AlertType.ExitingRoute,
      value: 'UserPortal:ExitingRouteAlert',
      feature: 'GoTrack.Alerts.ExitingRouteAlert',
    },
  ];

  ngOnInit(): void {
    this.getGeoZones();
    this.getVehicles();
    this.getGroups();
    this.getPaths();
  }

  getGeoZones() {
    return this.geoZoneService
      .getList({ skipCount: 0, maxResultCount: 50 })
      .pipe(
        map(val => {
          const m = new Map<string, GeoZoneDto>();
          val.items.map(v => {
            m.set(v.id, v);
          });
          this.geoZones$.set(m);
        })
      )
      .subscribe();
  }
  getPaths() {
    return this.routeService
      .getList({ skipCount: 0, maxResultCount: 50 })
      .pipe(
        map(val => {
          const m = new Map<string, RouteDto>();
          val.items.map(v => {
            m.set(v.id, v);
          });
          this.paths$.set(m);
        })
      )
      .subscribe();
  }
  getVehicles() {
    this.vehicleService
      .getList({ skipCount: 0, maxResultCount: 50 })
      .pipe(
        map(response => {
          const map = new Map<string, VehicleDto>();
          response.items.map((vehicle: VehicleDto & { name: string }) => {
            vehicle.name = `${vehicle.licensePlateSerial} - ${vehicle.licensePlateSubClass}`;
            map.set(vehicle.id, vehicle);
          });
          this.availableVehicles$.set(map);
        })
      )
      .subscribe();
  }
  getGroups() {
    this.vehicleGroupService
      .getList({ skipCount: 0, maxResultCount: 50 })
      .pipe(
        map(response => {
          const map = new Map<string, VehicleGroupDto>();
          response.items.map((Group: VehicleGroupDto) => {
            map.set(Group.id, Group);
          });
          this.availableGroups$.set(map);
        })
      )
      .subscribe();
  }

  onAlertTypeChange(): void {
    const alertTypeFields: { [key in AlertType]?: string[] } = {
      [AlertType.WorkTime]: ['name', 'startTime', 'endTime', 'daysOfWeek'],
      [AlertType.EnterZone]: ['geoZoneIds'],
      [AlertType.ExitZone]: ['geoZoneIds'],
      [AlertType.SpeedViolation]: ['maxSpeed'],
      [AlertType.ExitingRoute]: ['routeIds'],
    };
    const selectedType = this.alertForm.get('alertType')?.value as AlertType;
    Object.keys(alertTypeFields).forEach(type => {
      alertTypeFields[type].forEach(field => {
        if (selectedType === type) {
          if (!this.alertForm.contains(field)) {
            this.alertForm.addControl(field, new FormControl(''));
          }
        } else {
          if (this.alertForm.contains(field)) {
            this.alertForm.removeControl(field);
          }
        }
      });
    });
  }

  nextStep(): void {
    if (this.step() === 1) {
      this.step.set(2);
    } else {
      this.saveAlert();
    }
  }

  prevStep(): void {
    if (this.step() === 2) {
      this.step.set(1);
    } else {
      this.router.navigate(['/main', 'alerts']);
    }
  }

  saveAlert(): void {
    const formData = this.alertForm.value;
    this.alertCreateService.createAlert(formData as any).subscribe(() => {
      this.router.navigate(['/main/alerts']);
    });
  }

  removeChips(id: string, control: string): void {
    const selected = this.alertForm.get(control)?.value.filter((id_: string) => id_ !== id);
    this.alertForm.get(control)?.setValue(selected);
  }

  toggleSelectAll(control: string, map: Map<string, any>): void {
    const con = this.alertForm.get(control).value.length < map.size;
    const value = con ? Array.from(map.keys()) : [];
    this.alertForm.get(control)?.setValue(value);
  }
}
