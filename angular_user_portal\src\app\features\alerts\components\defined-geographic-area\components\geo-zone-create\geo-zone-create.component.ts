import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router, RouterLink } from '@angular/router';
import { GeoZoneService } from '@proxy/mobile/geo-zones';
import { changeNodeDto, MapComponent } from '@shared/components/map/map.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import * as polyline from '@mapbox/polyline';
import { LatLng, latLng, LatLngExpression, LatLngTuple } from 'leaflet';

@Component({
  selector: 'app-geo-zone-create',
  standalone: true,
  templateUrl: './geo-zone-create.component.html',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    RouterLink,
    MapComponent,
    LanguagePipe,
  ],
})
export class GeoZoneCreateComponent implements OnInit {
  draw = signal(true);
  private fb = inject(FormBuilder);
  private geoZoneService = inject(GeoZoneService);
  private router = inject(Router);

  form: FormGroup = this.fb.group({
    name: [''],
    polyLine: [[]],
  });
  nodes$ = signal(null);

  ngOnInit(): void {}

  onNodeChange(event: changeNodeDto) {
    const layer = event.event.layer;

    if (event.event.layerType == 'circle') {
      const center = (layer as any)._latlng;
      const r = (layer as any)._mRadius;
      const circle = this.circleToPolygon(center, r, 60);
      this.nodes$.set([...circle, circle[0]]);
    } else if (event.event.layerType == 'polygon') {
      const r = (layer as any)._latlngs;
      const temp = r[0].map(latLng => {
        const { lat, lng } = latLng;
        return [lat, lng];
      });

      this.nodes$.set([...temp, temp[0]]);
    }
    this.form.controls['polyLine'].setValue(this.nodes$());
  }

  private circleToPolygon(center: LatLngExpression, radius: number, sides: number): LatLngTuple[] {
    const latlng = latLng(center);
    const points: LatLngTuple[] = [];
    for (let i = 0; i < sides; i++) {
      const angle = (i * 2 * Math.PI) / sides; // Calculate the angle for each point
      const point = this.calculateOffset(latlng.lat, latlng.lng, radius, angle);
      points.push([point.lat, point.lng]);
    }
    return points;
  }
  private calculateOffset(lat: number, lng: number, radius: number, angle: number): LatLng {
    const earthRadius = 6378137; // Earth's radius in meters
    const deltaLat = (radius / earthRadius) * Math.cos(angle);
    const deltaLng = (radius / (earthRadius * Math.cos((lat * Math.PI) / 180))) * Math.sin(angle);
    return latLng(lat + (deltaLat * 180) / Math.PI, lng + (deltaLng * 180) / Math.PI);
  }

  save() {
    const obj = { ...this.form.value };
    obj.polyLine = { line: polyline.encode(this.nodes$()) };
    this.geoZoneService.create(obj).subscribe(res => {
      this.router.navigate(['/main/alerts']);
    });
  }
}
