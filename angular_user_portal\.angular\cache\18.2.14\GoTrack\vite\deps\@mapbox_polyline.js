import {
  __commonJS
} from "./chunk-ZTELYOIP.js";

// node_modules/@mapbox/polyline/src/polyline.js
var require_polyline = __commonJS({
  "node_modules/@mapbox/polyline/src/polyline.js"(exports, module) {
    var polyline = {};
    function py2_round(value) {
      return Math.floor(Math.abs(value) + 0.5) * (value >= 0 ? 1 : -1);
    }
    function encode(current, previous, factor) {
      current = py2_round(current * factor);
      previous = py2_round(previous * factor);
      var coordinate = (current - previous) * 2;
      if (coordinate < 0) {
        coordinate = -coordinate - 1;
      }
      var output = "";
      while (coordinate >= 32) {
        output += String.fromCharCode((32 | coordinate & 31) + 63);
        coordinate /= 32;
      }
      output += String.fromCharCode((coordinate | 0) + 63);
      return output;
    }
    polyline.decode = function(str, precision) {
      var index = 0, lat = 0, lng = 0, coordinates = [], shift = 0, result = 0, byte = null, latitude_change, longitude_change, factor = Math.pow(10, Number.isInteger(precision) ? precision : 5);
      while (index < str.length) {
        byte = null;
        shift = 1;
        result = 0;
        do {
          byte = str.charCodeAt(index++) - 63;
          result += (byte & 31) * shift;
          shift *= 32;
        } while (byte >= 32);
        latitude_change = result & 1 ? (-result - 1) / 2 : result / 2;
        shift = 1;
        result = 0;
        do {
          byte = str.charCodeAt(index++) - 63;
          result += (byte & 31) * shift;
          shift *= 32;
        } while (byte >= 32);
        longitude_change = result & 1 ? (-result - 1) / 2 : result / 2;
        lat += latitude_change;
        lng += longitude_change;
        coordinates.push([lat / factor, lng / factor]);
      }
      return coordinates;
    };
    polyline.encode = function(coordinates, precision) {
      if (!coordinates.length) {
        return "";
      }
      var factor = Math.pow(10, Number.isInteger(precision) ? precision : 5), output = encode(coordinates[0][0], 0, factor) + encode(coordinates[0][1], 0, factor);
      for (var i = 1; i < coordinates.length; i++) {
        var a = coordinates[i], b = coordinates[i - 1];
        output += encode(a[0], b[0], factor);
        output += encode(a[1], b[1], factor);
      }
      return output;
    };
    function flipped(coords) {
      var flipped2 = [];
      for (var i = 0; i < coords.length; i++) {
        var coord = coords[i].slice();
        flipped2.push([coord[1], coord[0]]);
      }
      return flipped2;
    }
    polyline.fromGeoJSON = function(geojson, precision) {
      if (geojson && geojson.type === "Feature") {
        geojson = geojson.geometry;
      }
      if (!geojson || geojson.type !== "LineString") {
        throw new Error("Input must be a GeoJSON LineString");
      }
      return polyline.encode(flipped(geojson.coordinates), precision);
    };
    polyline.toGeoJSON = function(str, precision) {
      var coords = polyline.decode(str, precision);
      return {
        type: "LineString",
        coordinates: flipped(coords)
      };
    };
    if (typeof module === "object" && module.exports) {
      module.exports = polyline;
    }
  }
});
export default require_polyline();
//# sourceMappingURL=@mapbox_polyline.js.map
