import { LocalizationService } from '@abp/ng.core';
import { IdentityUserService } from '@abp/ng.identity/proxy';
import { Component, DestroyRef, inject, OnDestroy } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { gridOptionToAbpOptions } from '../../abp-overrides.utils';
import {
  PermissionsDialogComponent,
  PermissionsDialogData,
} from '../permissions-dialog/permissions-dialog.component';
import { UsersCreateDialogComponent } from './users-create-dialog/users-create-dialog.component';
import { UsersUpdateDialogComponent } from './users-update-dialog/users-update-dialog.component';
import { user } from './users.model';

@Component({
  selector: 'app-user',
  standalone: true,
  imports: [TtwrGridComponent],
  templateUrl: './users.component.html',
  styleUrl: './users.component.scss',
})
export class UsersComponent implements OnDestroy {
  private identityUser = inject(IdentityUserService);
  private localization = inject(LocalizationService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = user()
    .select({
      id: true,
      userName: true,
      email: true,
      phoneNumber: true,
    })
    .grid({
      title: this.localization.instant('AbpIdentity::Users'),
      refreshSubject: this.refreshSubject,
      defaultActions: {
        sort: false,
        filters: false,
        pdf: false,
        print: false,
        excel: false,
        toggleAll: false,
      },
      dataFunc: (...args) =>
        this.identityUser.getList(gridOptionToAbpOptions(args)).pipe(map(res => res as any)),
      actions: [
        {
          label: this.localization.instant('AbpIdentity::NewUser'),
          delegateFunc: () => {
            const ref = this.dialog.open(UsersCreateDialogComponent, {
              width: '600px',
            });

            ref
              .afterClosed()
              .pipe(takeUntilDestroyed(this.destroyRef))
              .subscribe(res => {
                if (res) this.refreshSubject.next();
              });
          },
        },
      ],
      fieldActions: [
        {
          color: 'primary',
          label: 'Edit',
          showFunc: obj => obj.userName !== 'admin',
          delegateFunc: obj => {
            const ref = this.dialog.open(UsersUpdateDialogComponent, {
              width: '500px',
              data: obj,
            });

            ref
              .afterClosed()
              .pipe(takeUntilDestroyed(this.destroyRef))
              .subscribe(res => {
                if (res) this.refreshSubject.next();
              });
          },
        },
        {
          color: 'accent',
          label: this.localization.instant('AbpIdentity::Permissions'),
          showFunc: obj => obj.userName !== 'admin',
          delegateFunc: user =>
            this.dialog.open<PermissionsDialogComponent, PermissionsDialogData>(
              PermissionsDialogComponent,
              {
                maxWidth: '800px',
                width: '100%',
                data: {
                  providerName: 'U',
                  providerKey: user.userName,
                  displayName: user.userName,
                },
              }
            ),
        },
        {
          color: 'warn',
          label: 'Delete',
          showFunc: obj => obj.userName !== 'admin',
          confirmation: {
            title: this.localization.instant('AbpUi::AreYouSure'),
            message: this.localization.instant('AbpIdentity::UserDeletionConfirmationMessage', ''),
          },
          delegateFunc: obj => {
            this.loading.set(true);
            this.identityUser.delete(obj.id).subscribe({
              next: () => {
                this.alert.success(this.localization.instant('AbpUi::SuccessfullyDeleted'));
                this.refreshSubject.next();
              },
              error: err => {
                this.loading.set(false);
                const message = this.localization.instant(
                  err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
                );
                this.alert.error(message);
              },
            });
          },
        },
      ],
    });

  ngOnDestroy() {
    this.loading.set(false);
  }
}
