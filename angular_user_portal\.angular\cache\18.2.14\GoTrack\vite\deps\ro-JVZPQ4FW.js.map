{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/ro.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length;\n  if (i === 1 && v === 0) return 1;\n  if (!(v === 0) || n === 0 || n % 100 === Math.floor(n % 100) && n % 100 >= 2 && n % 100 <= 19) return 3;\n  return 5;\n}\nexport default [\"ro\", [[\"a.m.\", \"p.m.\"], u, u], u, [[\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"], [\"dum.\", \"lun.\", \"mar.\", \"mie.\", \"joi\", \"vin.\", \"sâm.\"], [\"duminică\", \"luni\", \"marți\", \"miercuri\", \"joi\", \"vineri\", \"sâmbătă\"], [\"du.\", \"lu.\", \"ma.\", \"mi.\", \"joi\", \"vi.\", \"sâ.\"]], u, [[\"I\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"ian.\", \"feb.\", \"mar.\", \"apr.\", \"mai\", \"iun.\", \"iul.\", \"aug.\", \"sept.\", \"oct.\", \"nov.\", \"dec.\"], [\"ianuarie\", \"februarie\", \"martie\", \"aprilie\", \"mai\", \"iunie\", \"iulie\", \"august\", \"septembrie\", \"octombrie\", \"noiembrie\", \"decembrie\"]], u, [[\"î.Hr.\", \"d.Hr.\"], u, [\"înainte de Hristos\", \"după Hristos\"]], 1, [6, 0], [\"dd.MM.y\", \"d MMM y\", \"d MMMM y\", \"EEEE, d MMMM y\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1}, {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"RON\", \"RON\", \"leu românesc\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"EUR\": [u, \"€\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [u, \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [u, \"$\"],\n  \"VND\": [u, \"₫\"],\n  \"XCD\": [u, \"$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE;AAC9C,MAAI,MAAM,KAAK,MAAM,EAAG,QAAO;AAC/B,MAAI,EAAE,MAAM,MAAM,MAAM,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAI,QAAO;AACtG,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,MAAM,GAAG,CAAC,YAAY,QAAQ,SAAS,YAAY,OAAO,UAAU,SAAS,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,MAAM,GAAG,CAAC,YAAY,aAAa,UAAU,WAAW,OAAO,SAAS,SAAS,UAAU,cAAc,aAAa,aAAa,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAC,sBAAsB,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,WAAW,YAAY,gBAAgB,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,OAAO,gBAAgB;AAAA,EACv5B,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAChB,GAAG,OAAO,MAAM;", "names": []}