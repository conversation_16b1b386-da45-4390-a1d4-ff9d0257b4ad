:host {
  --mat-stepper-container-color: #ffffff00;
  --mat-stepper-header-edit-state-icon-background-color: theme('colors.light_perple');
  --mat-stepper-line-color: #ffffff00;
  --mat-stepper-header-hover-state-layer-color: #ffffff00;
  --mat-stepper-header-done-state-icon-background-color: theme('colors.light_perple');
  --mat-stepper-header-height: '10px';
  --mat-stepper-header-icon-background-color: #e0e0e0;
  --mat-stepper-header-hover-state-layer-shape: '20px';
}
:host ::ng-deep {
  .mat-step-icon-content {
    display: none;
  }
  .mat-step-text-label {
    display: none;
  }
  .mat-stepper-horizontal-line {
    display: none;
  }
  .mat-horizontal-stepper-header-container {
    justify-content: center;
    gap: 4px;
  }
  .mat-step-icon {
    height: 10px;
    width: 50px;
    border-radius: 20px;
  }
  .mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label {
    display: none;
  }
  .mat-horizontal-content-container {
    padding: 24px;
  }
}
