import {
  ABP,
  getRoutePath,
  LocalizationModule,
  RouterEvents,
  RoutesService,
  TreeNode,
} from '@abp/ng.core';
import { NgTemplateOutlet } from '@angular/common';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIcon } from '@angular/material/icon';
import { Router, RouterLink } from '@angular/router';
import { filter, map, startWith } from 'rxjs';

@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [RouterLink, MatIcon, NgTemplateOutlet, LocalizationModule],
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
})
export class BreadcrumbComponent {
  private router = inject(Router);
  private events = inject(RouterEvents);
  private routes = inject(RoutesService);

  isAdministration(route: Pick<ABP.Route, 'name'>) {
    return route.name === 'AbpUiNavigation::Menu:Administration';
  }

  protected items = toSignal(
    this.events.getNavigationEvents('End').pipe(
      startWith(null),
      map(() => this.routes.search({ path: getRoutePath(this.router) })),
      filter(route => route !== null),
      map(route => {
        route = route!;
        const segments: Partial<ABP.Route>[] = [];

        let node = { parent: route } as TreeNode<ABP.Route>;

        while (node.parent) {
          node = node.parent;
          const { parent, children, isLeaf, path, ...segment } = node;

          if (!this.isAdministration(segment)) segments.unshift(segment);
        }

        return segments;
      })
    ),
    {
      initialValue: [],
    }
  );
}
