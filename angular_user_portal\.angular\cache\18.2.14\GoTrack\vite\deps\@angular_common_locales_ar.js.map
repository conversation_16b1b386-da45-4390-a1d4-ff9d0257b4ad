{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/ar.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 0) return 0;\n  if (n === 1) return 1;\n  if (n === 2) return 2;\n  if (n % 100 === Math.floor(n % 100) && n % 100 >= 3 && n % 100 <= 10) return 3;\n  if (n % 100 === Math.floor(n % 100) && n % 100 >= 11 && n % 100 <= 99) return 4;\n  return 5;\n}\nexport default [\"ar\", [[\"ص\", \"م\"], u, u], [[\"ص\", \"م\"], u, [\"صباحًا\", \"مساءً\"]], [[\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"], [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"], u, [\"أحد\", \"إثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"]], u, [[\"ي\", \"ف\", \"م\", \"أ\", \"و\", \"ن\", \"ل\", \"غ\", \"س\", \"ك\", \"ب\", \"د\"], [\"يناير\", \"فبراير\", \"مارس\", \"أبريل\", \"مايو\", \"يونيو\", \"يوليو\", \"أغسطس\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"], u], u, [[\"ق.م\", \"م\"], u, [\"قبل الميلاد\", \"ميلادي\"]], 6, [5, 6], [\"d‏/M‏/y\", \"dd‏/MM‏/y\", \"d MMMM y\", \"EEEE، d MMMM y\"], [\"h:mm a\", \"h:mm:ss a\", \"h:mm:ss a z\", \"h:mm:ss a zzzz\"], [\"{1}, {0}\", u, \"{1} في {0}\", u], [\".\", \",\", \";\", \"‎%‎\", \"‎+\", \"‎-\", \"E\", \"×\", \"‰\", \"∞\", \"ليس رقمًا\", \":\"], [\"#,##0.###\", \"#,##0%\", \"¤ #,##0.00\", \"#E0\"], \"EGP\", \"ج.م.‏\", \"جنيه مصري\", {\n  \"AED\": [\"د.إ.‏\"],\n  \"ARS\": [u, \"AR$\"],\n  \"AUD\": [\"AU$\"],\n  \"BBD\": [u, \"BB$\"],\n  \"BHD\": [\"د.ب.‏\"],\n  \"BMD\": [u, \"BM$\"],\n  \"BND\": [u, \"BN$\"],\n  \"BSD\": [u, \"BS$\"],\n  \"BYN\": [u, \"р.\"],\n  \"BZD\": [u, \"BZ$\"],\n  \"CAD\": [\"CA$\"],\n  \"CLP\": [u, \"CL$\"],\n  \"CNY\": [\"CN¥\"],\n  \"COP\": [u, \"CO$\"],\n  \"CUP\": [u, \"CU$\"],\n  \"DOP\": [u, \"DO$\"],\n  \"DZD\": [\"د.ج.‏\"],\n  \"EGP\": [\"ج.م.‏\", \"E£\"],\n  \"FJD\": [u, \"FJ$\"],\n  \"GBP\": [\"UK£\"],\n  \"GYD\": [u, \"GY$\"],\n  \"HKD\": [\"HK$\"],\n  \"IQD\": [\"د.ع.‏\"],\n  \"IRR\": [\"ر.إ.\"],\n  \"JMD\": [u, \"JM$\"],\n  \"JOD\": [\"د.أ.‏\"],\n  \"JPY\": [\"JP¥\"],\n  \"KWD\": [\"د.ك.‏\"],\n  \"KYD\": [u, \"KY$\"],\n  \"LBP\": [\"ل.ل.‏\", \"L£\"],\n  \"LRD\": [u, \"$LR\"],\n  \"LYD\": [\"د.ل.‏\"],\n  \"MAD\": [\"د.م.‏\"],\n  \"MRU\": [\"أ.م.\"],\n  \"MXN\": [\"MX$\"],\n  \"NZD\": [\"NZ$\"],\n  \"OMR\": [\"ر.ع.‏\"],\n  \"PHP\": [u, \"₱\"],\n  \"QAR\": [\"ر.ق.‏\"],\n  \"SAR\": [\"ر.س.‏\"],\n  \"SBD\": [u, \"SB$\"],\n  \"SDD\": [\"د.س.‏\"],\n  \"SDG\": [\"ج.س.\"],\n  \"SRD\": [u, \"SR$\"],\n  \"SYP\": [\"ل.س.‏\", \"£\"],\n  \"THB\": [\"฿\"],\n  \"TND\": [\"د.ت.‏\"],\n  \"TTD\": [u, \"TT$\"],\n  \"TWD\": [\"NT$\"],\n  \"USD\": [\"US$\"],\n  \"UYU\": [u, \"UY$\"],\n  \"YER\": [\"ر.ي.‏\"]\n}, \"rtl\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI;AACV,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAI,QAAO;AAC7E,MAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG,KAAK,IAAI,OAAO,MAAM,IAAI,OAAO,GAAI,QAAO;AAC9E,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,WAAW,YAAY,YAAY,UAAU,UAAU,OAAO,GAAG,GAAG,CAAC,OAAO,SAAS,UAAU,UAAU,QAAQ,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,SAAS,UAAU,QAAQ,SAAS,QAAQ,SAAS,SAAS,SAAS,UAAU,UAAU,UAAU,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC,eAAe,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,aAAa,YAAY,gBAAgB,GAAG,CAAC,UAAU,aAAa,eAAe,gBAAgB,GAAG,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,OAAO,MAAM,MAAM,KAAK,KAAK,KAAK,KAAK,aAAa,GAAG,GAAG,CAAC,aAAa,UAAU,cAAc,KAAK,GAAG,OAAO,SAAS,aAAa;AAAA,EAC5xB,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,SAAS,IAAI;AAAA,EACrB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,MAAM;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,SAAS,IAAI;AAAA,EACrB,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,MAAM;AAAA,EACd,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,MAAM;AAAA,EACd,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,SAAS,GAAG;AAAA,EACpB,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,OAAO;AAAA,EACf,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,KAAK;AAAA,EACb,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO;AACjB,GAAG,OAAO,MAAM;", "names": []}