import { ConfigStateService } from '@abp/ng.core';
import { NgStyle } from '@angular/common';
import { Component, inject, input, signal } from '@angular/core';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatOption, MatSelect } from '@angular/material/select';
import { RouterLink } from '@angular/router';
import { SubscriptionVehicleInfoCreateDto } from '@proxy/mobile/requests/account-subscription-requests';
import { AddVehiclesRequestService } from '@proxy/mobile/requests/add-vehicles-requests';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { ConfirmationDialogService } from '@shared/components/confirmation-dialog';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { customValidatitors } from '@shared/helper-assets/validation';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, map, of, switchMap } from 'rxjs';
import { AddVehicleDialogComponent } from '../add-vehicle-dialog/add-vehicle-dialog.component';

@Component({
  selector: 'app-increase-vehicle',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    LanguagePipe,
    RouterLink,
    MatIcon,
    MatMenuModule,
    NgStyle,
  ],
  templateUrl: './increase-vehicle.component.html',
})
export class IncreaseVehicleComponent {
  private fb = inject(FormBuilder);
  addVehiclesRequestService = inject(AddVehiclesRequestService);
  confirmationDialogService = inject(ConfirmationDialogService);
  configStateService = inject(ConfigStateService);
  dialog = inject(MatDialog);
  private vehicleService = inject(VehicleService);
  totalCapacity = 20;

  id = input<string>();

  vehicles = signal<VehicleDto[]>([]);

  form = this.fb.group({
    trackVehicles: this.fb.array<SubscriptionVehicleInfoCreateDto>(
      [],
      customValidatitors.minLength(1)
    ),
    promoCode: this.fb.control<string>(null),
  });

  ngOnInit(): void {
    this.vehicleService
      .getListVehicleViewModelWithAuditing()
      .pipe(
        map(response => {
          this.vehicles.set(response);
        })
      )
      .subscribe();
  }

  onSubmit() {
    if (this.form.valid) {
      const { trackVehicles, promoCode } = this.form.value;
      this.addVehiclesRequestService
        .createTempBill({
          hasValidDevice: true,
          trackVehicles: trackVehicles,
          promoCode: promoCode,
        })
        .subscribe(val => {
          const dialogref = openPriceOfferDialog(this.dialog, {
            ...val,
            pay: true,
          });
          dialogref
            .afterClosed()
            .pipe(
              switchMap((res: boolean) => {
                return this.confirmationDialogService
                  .open({
                    payConfirm: true,
                  })
                  .pipe(
                    switchMap(() => {
                      if (res) {
                        return this.addVehiclesRequestService
                          .create({
                            hasValidDevice: true,
                            trackVehicles: trackVehicles,
                          })
                          .pipe(
                            filter(
                              () =>
                                this.configStateService.getAll().setting.values[
                                  'GoTrack.TrackAccountRequest.IncreaseUserCountRequestFatoraPayEnabled'
                                ] == 'True'
                            ),
                            switchMap(id => {
                              return this.addVehiclesRequestService
                                .craetePayment({
                                  requestId: JSON.parse(id),
                                  language: 'en',
                                  savedCards: true,
                                  callBackUrl: location.href + 'track-accounts',
                                })
                                .pipe(
                                  map((link: string) => {
                                    window.open(link, '_blank');
                                  })
                                );
                            })
                          );
                      } else {
                        return of(null);
                      }
                    })
                  );
              })
            )
            .subscribe();
        });
    }
  }

  addVehiclesDialog(index: number | null = null) {
    let data: any = null;
    if (index != null) {
      data = this.form.controls.trackVehicles.at(index);
    }
    const ref = this.dialog.open(AddVehicleDialogComponent, { data: data });
    ref.afterClosed().subscribe(res => {
      if (res) {
        if (index != null) {
          this.remove(index);
        }
        this.form.controls.trackVehicles.push(res);
      }
    });
  }
  remove(index) {
    this.form.controls.trackVehicles.removeAt(index);
  }
  ngOnDestroy(): void {
    localStorage.removeItem('tempTrackAccountId');
  }
}
