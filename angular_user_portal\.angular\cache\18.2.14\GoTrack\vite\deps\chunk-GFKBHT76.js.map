{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.setting-management/fesm2022/abp-ng.setting-management-proxy.mjs"], "sourcesContent": ["import * as i1 from '@abp/ng.core';\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nvar index$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Abp: index$1\n});\nclass EmailSettingsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'SettingManagement';\n    this.get = config => this.restService.request({\n      method: 'GET',\n      url: '/api/setting-management/emailing'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.sendTestEmail = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/emailing/send-test-email',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.update = (input, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/emailing',\n      body: input\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function EmailSettingsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmailSettingsService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EmailSettingsService,\n      factory: EmailSettingsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EmailSettingsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\nclass TimeZoneSettingsService {\n  constructor(restService) {\n    this.restService = restService;\n    this.apiName = 'SettingManagement';\n    this.get = config => this.restService.request({\n      method: 'GET',\n      responseType: 'text',\n      url: '/api/setting-management/timezone'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.getTimezones = config => this.restService.request({\n      method: 'GET',\n      url: '/api/setting-management/timezone/timezones'\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n    this.update = (timezone, config) => this.restService.request({\n      method: 'POST',\n      url: '/api/setting-management/timezone',\n      params: {\n        timezone\n      }\n    }, {\n      apiName: this.apiName,\n      ...config\n    });\n  }\n  static {\n    this.ɵfac = function TimeZoneSettingsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TimeZoneSettingsService)(i0.ɵɵinject(i1.RestService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TimeZoneSettingsService,\n      factory: TimeZoneSettingsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TimeZoneSettingsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.RestService\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EmailSettingsService, TimeZoneSettingsService, index as Volo };\n"], "mappings": ";;;;;;;;;;;;;;AAGA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AACb,CAAC;AACD,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX,KAAK;AACP,CAAC;AACD,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,YAAU,KAAK,YAAY,QAAQ;AAAA,MAC5C,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,gBAAgB,CAAC,OAAO,WAAW,KAAK,YAAY,QAAQ;AAAA,MAC/D,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,SAAS,CAAC,OAAO,WAAW,KAAK,YAAY,QAAQ;AAAA,MACxD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACR,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,SAAY,WAAW,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,aAAa;AACvB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,MAAM,YAAU,KAAK,YAAY,QAAQ;AAAA,MAC5C,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,eAAe,YAAU,KAAK,YAAY,QAAQ;AAAA,MACrD,QAAQ;AAAA,MACR,KAAK;AAAA,IACP,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AACD,SAAK,SAAS,CAAC,UAAU,WAAW,KAAK,YAAY,QAAQ;AAAA,MAC3D,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,SAAS,KAAK;AAAA,OACX,OACJ;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,SAAY,WAAW,CAAC;AAAA,IACvF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}