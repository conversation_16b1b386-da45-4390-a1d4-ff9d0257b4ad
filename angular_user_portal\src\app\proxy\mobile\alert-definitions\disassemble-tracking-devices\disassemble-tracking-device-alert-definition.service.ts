import type { CreateDisassembleTrackingDeviceDto, DisassembleTrackingDevicesDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DisassembleTrackingDeviceAlertDefinitionService {
  apiName = 'Default';
  

  create = (disassembleTrackingDeviceDto: CreateDisassembleTrackingDeviceDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/disassembleTrackingDeviceAlertDefinition',
      body: disassembleTrackingDeviceDto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DisassembleTrackingDevicesDto>({
      method: 'GET',
      url: `/api/app/disassembleTrackingDeviceAlertDefinition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, DisassembleTrackingDevicesDto[]>({
      method: 'GET',
      url: '/api/app/disassembleTrackingDeviceAlertDefinition',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
