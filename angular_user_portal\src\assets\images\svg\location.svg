<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="85.627" height="79.14" viewBox="0 0 85.627 79.14">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_413" data-name="Rectangle 413" width="85.627" height="79.14" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_402" data-name="Rectangle 402" width="27.113" height="65.329" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_403" data-name="Rectangle 403" width="27.142" height="65.329" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_404" data-name="Rectangle 404" width="19.083" height="38.242" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_405" data-name="Rectangle 405" width="19.047" height="38.142" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <rect id="Rectangle_406" data-name="Rectangle 406" width="11.412" height="11.437" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_407" data-name="Rectangle 407" width="7.606" height="7.624" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <rect id="Rectangle_408" data-name="Rectangle 408" width="2.843" height="2.848" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <rect id="Rectangle_410" data-name="Rectangle 410" width="2.843" height="2.849" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-12">
      <rect id="Rectangle_412" data-name="Rectangle 412" width="2.841" height="2.85" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_198" data-name="Group 198" opacity="0.744" style="mix-blend-mode: lighten;isolation: isolate">
    <g id="Group_197" data-name="Group 197" clip-path="url(#clip-path)">
      <g id="Group_166" data-name="Group 166" transform="translate(38.581 4.536)" style="isolation: isolate">
        <g id="Group_165" data-name="Group 165">
          <g id="Group_164" data-name="Group 164" clip-path="url(#clip-path-2)">
            <path id="Path_279" data-name="Path 279" d="M155,17.846a10.843,10.843,0,0,0,3.041.74c19.992,4.785,27.715,29.51,13.761,44.584C165.481,70,158.7,76.407,152.127,83a1.246,1.246,0,0,1-.338.173q0-9.521.008-19.042c5.617-.316,10.442-2.267,14.248-6.594a18.967,18.967,0,0,0-.886-26.1A18.642,18.642,0,0,0,151.8,25.891l-.025-8.045Z" transform="translate(-151.78 -17.846)" fill="#6e68dc"/>
          </g>
        </g>
      </g>
      <g id="Group_169" data-name="Group 169" transform="translate(11.463 4.536)" style="isolation: isolate">
        <g id="Group_168" data-name="Group 168">
          <g id="Group_167" data-name="Group 167" clip-path="url(#clip-path-3)">
            <path id="Path_280" data-name="Path 280" d="M72.232,64.134q0,9.521-.008,19.042c-.539-.09-.775-.569-1.109-.9-5.853-5.849-11.664-11.74-17.549-17.556A27.523,27.523,0,0,1,45.373,48.8c-.033-.219-.275-5.042-.275-7.283.488-.429.369-1.058.489-1.595C48.092,28.774,54.855,21.7,65.821,18.609c.965-.272,2.029-.16,2.9-.762h3.491l.024,8.045-.051.052a17.6,17.6,0,0,0-9.991,2.85,18.3,18.3,0,0,0-9,15.521,18.241,18.241,0,0,0,7.339,15.719,18.512,18.512,0,0,0,11.64,4.046Z" transform="translate(-45.098 -17.847)" fill="#7f78eb"/>
          </g>
        </g>
      </g>
      <g id="Group_172" data-name="Group 172" transform="translate(38.544 12.581)" style="isolation: isolate">
        <g id="Group_171" data-name="Group 171">
          <g id="Group_170" data-name="Group 170" clip-path="url(#clip-path-4)">
            <path id="Path_282" data-name="Path 282" d="M151.7,49.5a19.039,19.039,0,0,1,14.241,31.648c-3.807,4.326-8.632,6.278-14.248,6.593l-.054-.054q.017-4,.033-8.009c.221-.025.442-.052.663-.075,6.853-.725,11.289-6.489,10.084-13.127a11.021,11.021,0,0,0-10.749-8.926q-.01-4-.02-8Z" transform="translate(-151.634 -49.496)" fill="#7e78e9"/>
          </g>
        </g>
      </g>
      <g id="Group_175" data-name="Group 175" transform="translate(19.547 12.627)" style="isolation: isolate">
        <g id="Group_174" data-name="Group 174">
          <g id="Group_173" data-name="Group 173" clip-path="url(#clip-path-5)">
            <path id="Path_283" data-name="Path 283" d="M95.905,49.682q.01,4,.02,8l.016.1a10.949,10.949,0,1,0,0,21.9l-.017.127q-.016,4-.033,8.009a18.514,18.514,0,0,1-11.64-4.046,18.241,18.241,0,0,1-7.339-15.719,18.3,18.3,0,0,1,9-15.521,17.6,17.6,0,0,1,9.992-2.849" transform="translate(-76.898 -49.675)" fill="#95adf8"/>
          </g>
        </g>
      </g>
      <path id="Path_284" data-name="Path 284" d="M151.753,81.17A11.021,11.021,0,0,1,162.5,90.1c1.206,6.638-3.23,12.4-10.084,13.127-.221.023-.442.05-.663.075l.017-.127q0-10.949,0-21.9Z" transform="translate(-113.179 -60.538)" fill="#e0e9ee"/>
      <path id="Path_285" data-name="Path 285" d="M119.535,81.563q0,10.948,0,21.9a10.949,10.949,0,1,1,0-21.9" transform="translate(-80.946 -60.827)" fill="#fefefe"/>
      <g id="Group_178" data-name="Group 178" transform="translate(3.803 67.702)" style="isolation: isolate">
        <g id="Group_177" data-name="Group 177">
          <g id="Group_176" data-name="Group 176" clip-path="url(#clip-path-6)">
            <path id="Path_286" data-name="Path 286" d="M26.375,272.1a5.706,5.706,0,1,1-5.648-5.752,5.734,5.734,0,0,1,5.648,5.752" transform="translate(-14.962 -266.346)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
      <g id="Group_181" data-name="Group 181" transform="translate(74.218 3.811)" style="isolation: isolate">
        <g id="Group_180" data-name="Group 180">
          <g id="Group_179" data-name="Group 179" clip-path="url(#clip-path-7)">
            <path id="Path_287" data-name="Path 287" d="M299.586,18.8a3.8,3.8,0,1,1-3.808-3.806,3.825,3.825,0,0,1,3.808,3.806" transform="translate(-291.98 -14.993)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
      <g id="Group_184" data-name="Group 184" transform="translate(65.656)" style="isolation: isolate">
        <g id="Group_183" data-name="Group 183">
          <g id="Group_182" data-name="Group 182" clip-path="url(#clip-path-8)">
            <path id="Path_288" data-name="Path 288" d="M261.141,1.421a1.422,1.422,0,1,1-2.844.006,1.422,1.422,0,1,1,2.844-.006" transform="translate(-258.297 0)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
      <g id="Group_187" data-name="Group 187" transform="translate(2.855 1.907)" style="isolation: isolate">
        <g id="Group_186" data-name="Group 186">
          <g id="Group_185" data-name="Group 185" clip-path="url(#clip-path-8)">
            <path id="Path_289" data-name="Path 289" d="M14.073,8.938A1.421,1.421,0,1,1,12.71,7.5a1.441,1.441,0,0,1,1.363,1.434" transform="translate(-11.231 -7.503)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
      <g id="Group_190" data-name="Group 190" transform="translate(82.784 37.19)" style="isolation: isolate">
        <g id="Group_189" data-name="Group 189">
          <g id="Group_188" data-name="Group 188" clip-path="url(#clip-path-10)">
            <path id="Path_290" data-name="Path 290" d="M328.523,147.724a1.422,1.422,0,1,1-1.383-1.415,1.441,1.441,0,0,1,1.383,1.415" transform="translate(-325.68 -146.309)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
      <g id="Group_193" data-name="Group 193" transform="translate(0 52.448)" style="isolation: isolate">
        <g id="Group_192" data-name="Group 192">
          <g id="Group_191" data-name="Group 191" clip-path="url(#clip-path-8)">
            <path id="Path_291" data-name="Path 291" d="M2.843,207.768a1.441,1.441,0,0,1-1.479,1.413,1.424,1.424,0,0,1,.114-2.846,1.443,1.443,0,0,1,1.365,1.432" transform="translate(0 -206.334)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
      <g id="Group_196" data-name="Group 196" transform="translate(24.741 74.38)" style="isolation: isolate">
        <g id="Group_195" data-name="Group 195">
          <g id="Group_194" data-name="Group 194" clip-path="url(#clip-path-12)">
            <path id="Path_292" data-name="Path 292" d="M98.735,292.617a1.425,1.425,0,1,1-1.4,1.394,1.441,1.441,0,0,1,1.4-1.394" transform="translate(-97.332 -292.617)" fill="#98e1ed"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
