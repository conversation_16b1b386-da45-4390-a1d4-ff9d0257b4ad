<div [formGroup]="alertForm()">
  <mat-label class="pr-2">{{ 'UserPortal:SelectZone' | i18n }}</mat-label>
  <mat-form-field appearance="outline" class="w-full">
    <mat-select
      formControlName="geoZoneIds"
      multiple
      placeholder="{{ 'UserPortal:SelectZone' | i18n }}"
    >
      <mat-select-trigger>
        <div>
          @for (geoZoneId of alertForm().get('geoZoneIds')?.value; track geoZoneId) {
          <mat-chip [removable]="true" (removed)="removeGeoZone(geoZoneId)">
            {{ geoZones().get(geoZoneId).name }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
          }
        </div>
      </mat-select-trigger>
      @for (geoZone of geoZones().values(); track $index) {
      <mat-option [value]="geoZone.id">
        {{ geoZone.name }}
      </mat-option>
      }
    </mat-select>
  </mat-form-field>
</div>
