import type { VehicleLicensePlateSubClass } from '../../../vehicles/license-plates/vehicle-license-plate-sub-class.enum';

export interface SubscriptionVehicleInfoCreateDto {
  licensePlateSerial: string;
  licensePlateSubClass: VehicleLicensePlateSubClass;
  consumptionRate: number;
  needsTrackingDevice: boolean;
  color: string;
}

export interface SubscriptionVehicleInfoDto {
  colorHex?: string;
  licensePlateSubClass?: string;
  licensePlateSerial?: string;
  consumptionRate: number;
  needsTrackingDevice: boolean;
}

export interface CreateAccountRequestPaymentDto {
  requestId?: string;
  language?: string;
  savedCards: boolean;
  callBackUrl?: string;
}
