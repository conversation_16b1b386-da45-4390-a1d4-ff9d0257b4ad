<div>
  <div class="relative w-44 h-44 mx-auto rounded-full border-purple-400" style="border-width: 3px">
    <!-- Profile Image -->
    <img class="rounded-full p-2 w-full h-full" [src]="profileImageIcon" />

    <!-- Camera Icon for Upload -->
    <div
      class="absolute bottom-1 right-1 bg-white rounded-full shadow-md border border-gray-200"
      style="width: 28%; padding: 0.2rem"
    >
      <label for="file-upload">
        <img class="p-2 cursor-pointer" [src]="cameraIcon" />
      </label>
      <input
        id="file-upload"
        type="file"
        accept="image/*"
        (change)="onProfileImageChange($event)"
        hidden
      />
    </div>
  </div>
  <h3 class="mt-4 text-base font-medium text-gray-900">
    {{ user.firstName ?? 'firstName' }} {{ user.lastName ?? 'lastName' }}
  </h3>
  <h4 class="mt-4 text-base font-medium text-gray-900 flex items-center justify-center gap-4">
    <div>
      {{ user.email ?? 'email' }}
    </div>
    <mat-icon [ngClass]="[!user.isVerified ? 'text-main_red' : 'text-main_green']">{{
      user.isVerified ? 'check_circle' : 'warning'
    }}</mat-icon>
  </h4>
  <h4 class="mt-4 text-base font-medium text-gray-900">
    {{ user.phoneNumber ?? 'phoneNumber' }}
  </h4>
</div>
