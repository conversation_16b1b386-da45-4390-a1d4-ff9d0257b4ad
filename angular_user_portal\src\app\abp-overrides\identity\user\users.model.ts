import { IdentityRoleService } from '@abp/ng.identity/proxy';
import { inject } from '@angular/core';
import { arrayMap, fields, model } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';

export const user = () => {
  const identityRoles = inject(IdentityRoleService);

  return model({
    id: fields.text(),
    userName: fields.text(),
    password: fields.text(),
    name: fields.text(),
    surname: fields.text(),
    email: fields.text(),
    phoneNumber: fields.text(),
    isActive: fields.boolean(),
    lockoutEnabled: fields.boolean(),
    roleNames: fields.selectFetch('multiple', () =>
      identityRoles.getAllList().pipe(
        map(res => res.items ?? []),
        arrayMap(role => ({
          label: role.name!,
          value: role.name!,
        }))
      )
    ),
  });
};
