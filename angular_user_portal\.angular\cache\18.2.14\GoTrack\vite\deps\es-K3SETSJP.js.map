{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/es.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val,\n    i = Math.floor(Math.abs(val)),\n    v = val.toString().replace(/^[^.]*\\.?/, '').length,\n    e = parseInt(val.toString().replace(/^[^e]*(e([-+]?\\d+))?/, '$2')) || 0;\n  if (n === 1) return 1;\n  if (e === 0 && !(i === 0) && i % 1000000 === 0 && v === 0 || !(e >= 0 && e <= 5)) return 4;\n  return 5;\n}\nexport default [\"es\", [[\"a. m.\", \"p. m.\"], u, u], u, [[\"D\", \"L\", \"M\", \"X\", \"J\", \"V\", \"S\"], [\"dom\", \"lun\", \"mar\", \"mié\", \"jue\", \"vie\", \"sáb\"], [\"domingo\", \"lunes\", \"martes\", \"miércoles\", \"jueves\", \"viernes\", \"sábado\"], [\"DO\", \"LU\", \"MA\", \"MI\", \"JU\", \"VI\", \"SA\"]], u, [[\"E\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"], [\"ene\", \"feb\", \"mar\", \"abr\", \"may\", \"jun\", \"jul\", \"ago\", \"sept\", \"oct\", \"nov\", \"dic\"], [\"enero\", \"febrero\", \"marzo\", \"abril\", \"mayo\", \"junio\", \"julio\", \"agosto\", \"septiembre\", \"octubre\", \"noviembre\", \"diciembre\"]], u, [[\"a. C.\", \"d. C.\"], u, [\"antes de Cristo\", \"después de Cristo\"]], 1, [6, 0], [\"d/M/yy\", \"d MMM y\", \"d 'de' MMMM 'de' y\", \"EEEE, d 'de' MMMM 'de' y\"], [\"H:mm\", \"H:mm:ss\", \"H:mm:ss z\", \"H:mm:ss (zzzz)\"], [\"{1}, {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"#,##0 %\", \"#,##0.00 ¤\", \"#E0\"], \"EUR\", \"€\", \"euro\", {\n  \"AUD\": [u, \"$\"],\n  \"BRL\": [u, \"R$\"],\n  \"BYN\": [u, \"р.\"],\n  \"CAD\": [u, \"$\"],\n  \"CNY\": [u, \"¥\"],\n  \"EGP\": [],\n  \"ESP\": [\"₧\"],\n  \"GBP\": [u, \"£\"],\n  \"HKD\": [u, \"$\"],\n  \"ILS\": [u, \"₪\"],\n  \"INR\": [u, \"₹\"],\n  \"JPY\": [u, \"¥\"],\n  \"KRW\": [u, \"₩\"],\n  \"MXN\": [u, \"$\"],\n  \"NZD\": [u, \"$\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"THB\": [\"฿\"],\n  \"TWD\": [u, \"NT$\"],\n  \"USD\": [\"US$\", \"$\"],\n  \"XAF\": [],\n  \"XCD\": [u, \"$\"],\n  \"XOF\": []\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI,KACR,IAAI,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,GAC5B,IAAI,IAAI,SAAS,EAAE,QAAQ,aAAa,EAAE,EAAE,QAC5C,IAAI,SAAS,IAAI,SAAS,EAAE,QAAQ,wBAAwB,IAAI,CAAC,KAAK;AACxE,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,MAAM,KAAK,EAAE,MAAM,MAAM,IAAI,QAAY,KAAK,MAAM,KAAK,EAAE,KAAK,KAAK,KAAK,GAAI,QAAO;AACzF,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,WAAW,SAAS,UAAU,aAAa,UAAU,WAAW,QAAQ,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,GAAG,CAAC,SAAS,WAAW,SAAS,SAAS,QAAQ,SAAS,SAAS,UAAU,cAAc,WAAW,aAAa,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAC,mBAAmB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,WAAW,sBAAsB,0BAA0B,GAAG,CAAC,QAAQ,WAAW,aAAa,gBAAgB,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,WAAW,cAAc,KAAK,GAAG,OAAO,KAAK,QAAQ;AAAA,EACt4B,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,GAAG,KAAK;AAAA,EAChB,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC;AAAA,EACR,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC;AACV,GAAG,OAAO,MAAM;", "names": []}