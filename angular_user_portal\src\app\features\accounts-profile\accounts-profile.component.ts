import { Component, inject, signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { GetMobileUserProfileDto } from '@proxy/mobile/mobile-identity-users';

import { ProfileImageComponent } from '@shared/components/profile-image/profile-image.component';
import { openVerifyEmailDialog } from '@shared/components/verify-email-dialog/verify-email-dialog.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-accounts-profile',
  standalone: true,
  templateUrl: './accounts-profile.component.html',
  imports: [LanguagePipe, MatIcon, RouterLink, ProfileImageComponent],
})
export class AccountsProfileComponent {
  profile = signal<GetMobileUserProfileDto | undefined>(undefined);
  dialog = inject(MatDialog);
  reload = signal(false);

  ngOnInit(): void {}

  openEmailVerify() {
    openVerifyEmailDialog(this.dialog, this.profile()).subscribe(v => {
      if (v) {
        this.reload.set(!this.reload());
      }
    });
  }
}
