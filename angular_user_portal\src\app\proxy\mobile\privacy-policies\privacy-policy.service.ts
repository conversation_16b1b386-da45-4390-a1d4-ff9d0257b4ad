import type { AcceptPrivacyPolicyDto, PrivacyPolicyDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PrivacyPolicyService {
  apiName = 'Default';
  

  accept = (input: AcceptPrivacyPolicyDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/privacyPolicy/accept',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  getLatest = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, PrivacyPolicyDto>({
      method: 'GET',
      url: '/api/app/privacyPolicy/latest',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
