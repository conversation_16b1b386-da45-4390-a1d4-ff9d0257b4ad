
export interface CreateObserverDto {
  name?: string;
  phoneNumber?: string;
  vehicleIds: string[];
  vehicleGroupIds: string[];
}

export interface ObservationViewModelDto {
  trackAccountId?: string;
  phoneNumber?: string;
  observerName?: string;
  vehicleOrVehicleGroupName?: string;
  vehicleOrGroupId?: string;
  observationType?: string;
}

export interface UpdateObserverDto {
  userTrackAccountAssociationId?: string;
  name?: string;
  phoneNumber?: string;
}
