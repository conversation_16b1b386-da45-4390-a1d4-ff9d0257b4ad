import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { CreateAccountRequestPaymentDto, SubscriptionVehicleInfoDto } from '../account-subscription-requests/models';
import type { BillDto } from '../../payments/bills/dtos/models';
import type { CreateRenewTrackAccountSubscriptionsDto, RenewSubscriptionRequestDetailsDto, RenewSubscriptionRequestDto } from '../renew-track-account-subscriptions/dtos/models';
import type { UserTrackAccountAssociationDto } from '../../user-track-account-associations/dtos/models';
import type { VehicleDto } from '../../vehicles/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class RenewSubscriptionRequestService {
  apiName = 'Default';
  

  create = (dto: CreateRenewTrackAccountSubscriptionsDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/renewSubscriptionRequest',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  createPayment = (input: CreateAccountRequestPaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/renewSubscriptionRequest/payment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  createTempBill = (dto: CreateRenewTrackAccountSubscriptionsDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/renewSubscriptionRequest/tempBill',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, RenewSubscriptionRequestDetailsDto>({
      method: 'GET',
      url: `/api/app/renewSubscriptionRequest/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<RenewSubscriptionRequestDto>>({
      method: 'GET',
      url: '/api/app/renewSubscriptionRequest',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListNewVehicles = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<SubscriptionVehicleInfoDto>>({
      method: 'GET',
      url: `/api/app/renewSubscriptionRequest/${id}/newVehicles`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListRemainingVehicles = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VehicleDto>>({
      method: 'GET',
      url: `/api/app/renewSubscriptionRequest/${id}/remainingVehicles`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListRemovedUsers = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<UserTrackAccountAssociationDto>>({
      method: 'GET',
      url: `/api/app/renewSubscriptionRequest/${id}/removedUsers`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListRemovedVehicles = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VehicleDto>>({
      method: 'GET',
      url: `/api/app/renewSubscriptionRequest/${id}/removedVehicles`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
