import type { FullAuditedEntityDto } from '@abp/ng.core';
import type { SubscriptionVehicleInfoCreateDto, SubscriptionVehicleInfoDto } from '../../account-subscription-requests/models';

export interface AddVehiclesRequestDto extends FullAuditedEntityDto<string> {
  trackVehicles: SubscriptionVehicleInfoDto[];
  trackerInstallationLocation?: string;
  trackAccountId?: string;
  hasValidDevice: boolean;
}

export interface CreateAddVehiclesRequestsDto {
  trackVehicles: SubscriptionVehicleInfoCreateDto[];
  hasValidDevice: boolean;
  promoCode?: string;
}

export interface CreateAddVehiclesRequestsPaymentDto {
  requestId: string;
  language?: string;
  savedCards: boolean;
  callBackUrl?: string;
}
