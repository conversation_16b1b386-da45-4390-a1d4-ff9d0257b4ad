import type { GeoNodeTreeDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GeoNodeService {
  apiName = 'Default';
  

  get = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, GeoNodeTreeDto[]>({
      method: 'GET',
      url: '/api/app/geoNode',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
