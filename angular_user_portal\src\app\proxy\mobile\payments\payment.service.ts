import type { CreateMultiplePaymentsDto, CreatePaymentDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  apiName = 'Default';
  

  getCheckPaymentStatus = (userFatoraPaymentId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'GET',
      url: `/api/app/payment/checkPaymentStatus/${userFatoraPaymentId}`,
    },
    { apiName: this.apiName,...config });
  

  getProcessMultiplePayments = (masterPaymentId: string, userFatoraPaymentIds: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'GET',
      url: `/api/app/payment/processMultiplePayments/${masterPaymentId}`,
      params: { userFatoraPaymentIds },
    },
    { apiName: this.apiName,...config });
  

  payDevByInput = (input: CreatePaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/payment/payDev',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  payMultipleRequests = (input: CreateMultiplePaymentsDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/payment/payMultipleRequests',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  reversalPaymentDev = (paymentId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/payment/reversalPaymentDev/${paymentId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
