import type { GenerateOtpDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class OtpService {
  apiName = 'Default';
  

  sendOtp = (dto: GenerateOtpDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/otp/sendOtp',
      body: dto,
    },
    { apiName: this.apiName,...config });
  

  sendOtpDevByDto = (dto: GenerateOtpDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/otp/sendOtpDev',
      body: dto,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
