import { registerLocale } from '@abp/ng.core/locale';
import { registerLocaleData } from '@angular/common';
import ar from '@angular/common/locales/ar';
import { APP_INITIALIZER, ApplicationConfig, inject, isDevMode } from '@angular/core';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { LanguageService } from '@ttwr-framework/ngx-main-visuals';
import { environment } from '../environments/environment';
import { provideNgxMainVisualsWithABP } from './abp-overrides';
import { appInitializer } from './app.initializer';
import { routes } from './app.routes';
import { MAT_TABS_CONFIG } from '@angular/material/tabs';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { AuthInterceptor } from '@shared/interceptors/auth.interceptor';
import { TrackAccountInterceptor } from '@shared/interceptors/track-account .interceptor';
import { tempLocalization } from '@shared/constants/temp-localization';
import { errorHandlerInterceptor } from '@shared/interceptors/error.interceptor';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getMessaging, provideMessaging } from '@angular/fire/messaging';
import { provideServiceWorker } from '@angular/service-worker';
import { firebaseConfig } from 'firebase';

registerLocaleData(ar);

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withComponentInputBinding()),
    provideAnimationsAsync(),
    provideHttpClient(
      withInterceptors([AuthInterceptor, TrackAccountInterceptor, errorHandlerInterceptor])
    ),
    provideNgxMainVisualsWithABP({
      isGridSearchableColumnDefault: false,
      isGridSortableColumnDefault: false,
      initialPageSize: 20,
      pageSizeOptions: [20, 30, 40, 100],
      abpOptions: {
        environment,
        registerLocaleFn: registerLocale(),
        localizations: tempLocalization,
      },
    }),
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializer,
      multi: true,
    },
    {
      provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
      useValue: {
        appearance: 'outline',
      },
    },
    {
      provide: MAT_DATE_LOCALE,
      useFactory: () => inject(LanguageService).selectedLanguage(),
    },
    {
      provide: MAT_TABS_CONFIG,
      useValue: {
        stretchTabs: false,
      },
    },
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideFirebaseApp(() => initializeApp(firebaseConfig)),
    provideMessaging(() => getMessaging()),
  ],
};
