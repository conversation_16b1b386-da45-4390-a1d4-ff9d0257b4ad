ol {
  background-color: var(--ttwr-abp-overrides-breadcrumb-background);
  color: var(--ttwr-abp-overrides-breadcrumb-text-color);
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  font-size: 0.825rem;
  border-radius: 2rem;
  width: fit-content;
  padding: 0.1rem 1rem;
  justify-content: center;
  width: 100%;

  a {
    color: inherit;
    font-weight: bold;
  }

  li:not(:first-of-type)::before {
    content: '/';
    padding: 0 0.5rem;
  }

  mat-icon {
    margin-top: 0.25rem;
    font-size: 1.2rem;
    width: auto;
    height: auto;
  }
}
