import { AsyncPipe } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormControl, FormGroup } from '@angular/forms';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map, switchMap } from 'rxjs';

@Component({
  selector: 'app-validation',
  template: `{{ errorText | async | i18n }}`,
  imports: [AsyncPipe, LanguagePipe],
  standalone: true,
})
export class ValidationComponent {
  errors = input.required<FormControl | AbstractControl>();

  customErrors = input<{ [key: string]: string }>();

  list = {
    required: 'UserPortal:Validation_required',
    email: 'UserPortal:Validation_email',
    minimumObserversCount: 'UserPortal:Validation_minimumObserversCount',
    smsBundleRequired: 'UserPortal:Validation_required',
    minLength: 'UserPortal:Validation_minLength',
  };
  errorsList = computed(() => {
    const e = this.customErrors();
    return { ...this.list, e };
  });

  errors$ = toObservable(this.errors);

  errorText = this.errors$.pipe(
    switchMap(c => c.events),
    map(v => {
      let { touched, errors } = this.errors();
      errors = errors ?? {};
      if (touched) {
        const code = Object.keys(this.errorsList()).find(val => {
          return errors[val] == true;
        });
        return this.errorsList()[code];
      }
    })
  );

  ngOnInit(): void {}
}
