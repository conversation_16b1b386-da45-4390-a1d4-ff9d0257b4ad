<div>
  <div class="flex justify-between m-4">
    <img src="assets/images/logo/LogoColored.png" class="" alt="" />
    <button class="top-4 end-4" mat-button [routerLink]="'/track-accounts'">
      {{ 'UserPortal:Back' | i18n }}
    </button>
  </div>

  <div class="flex justify-center items-center h-full">
    <div
      class="p-1 mx-1 w-full bg-white bg-opacity-90 rounded-3xl shadow-lg drop-shadow-lg sm:w-1/2 md:p-8 md:mx-6"
    >
      <div class="grid grid-cols-1 gap-y-4 justify-center justify-items-center">
        @for (item of orders(); track $index) {
        <div class="w-full" auto-animate>
          <!-- for auto animate  -->
          @if(true){
          <div class="p-4 w-full rounded-md bg-main_light_gray">
            <div class="flex justify-center sm:flex-row">
              <div class="flex-shrink-0 p-2 bg-white rounded-full size-fit">
                <img src="assets/images/svg/car.svg" class="size-8 car" alt="" />
              </div>
              <div class="flex-grow me-4">
                <div class="grid grid-cols-[max-content_1fr] gap-y-2 gap-x-4 text-center">
                  <div class="text-main_gray">{{ 'UserPortal:Order Type' | i18n }}</div>
                  <div>{{ item.type | i18n }}</div>
                  <div class="text-main_gray">{{ 'UserPortal:Order Status' | i18n }}</div>
                  <div>{{ item.status | i18n }}</div>
                  <div class="text-main_gray">{{ 'UserPortal:Order Date' | i18n }}</div>
                  <div>{{ item.creationTime | date }}</div>
                </div>
              </div>
              <div class="flex items-center">
                <mat-icon
                  class="text-white rounded-full bg-main_perple ltr:rotate-180"
                  [routerLink]="[item.type, item.id]"
                  >keyboard_arrow_left</mat-icon
                >
              </div>
            </div>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>
</div>
