{"version": 3, "sources": ["../../../../../../node_modules/@angular/common/locales/tr.mjs"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\nfunction plural(val) {\n  const n = val;\n  if (n === 1) return 1;\n  return 5;\n}\nexport default [\"tr\", [[\"öö\", \"ös\"], [\"ÖÖ\", \"ÖS\"], u], [[\"ÖÖ\", \"ÖS\"], u, u], [[\"P\", \"P\", \"S\", \"Ç\", \"P\", \"C\", \"C\"], [\"<PERSON>\", \"<PERSON>zt\", \"Sal\", \"Çar\", \"Per\", \"Cum\", \"Cmt\"], [\"Pazar\", \"Pazartesi\", \"Salı\", \"Çarşamba\", \"Perşembe\", \"Cuma\", \"Cumartesi\"], [\"<PERSON>\", \"Pt\", \"Sa\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\"]], u, [[\"O\", \"<PERSON>\", \"<PERSON>\", \"N\", \"M\", \"H\", \"T\", \"A\", \"E\", \"E\", \"K\", \"A\"], [\"Oca\", \"<PERSON><PERSON>\", \"<PERSON>\", \"Nis\", \"May\", \"Haz\", \"Tem\", \"Ağu\", \"Eyl\", \"Eki\", \"Kas\", \"Ara\"], [\"Ocak\", \"Şubat\", \"Mart\", \"Nisan\", \"Mayıs\", \"Haziran\", \"<PERSON>mmuz\", \"<PERSON>ğustos\", \"<PERSON>ylül\", \"<PERSON>kim\", \"Kasım\", \"Aralık\"]], u, [[\"MÖ\", \"MS\"], u, [\"Milattan Önce\", \"Milattan Sonra\"]], 1, [6, 0], [\"d.MM.y\", \"d MMM y\", \"d MMMM y\", \"d MMMM y EEEE\"], [\"HH:mm\", \"HH:mm:ss\", \"HH:mm:ss z\", \"HH:mm:ss zzzz\"], [\"{1} {0}\", u, u, u], [\",\", \".\", \";\", \"%\", \"+\", \"-\", \"E\", \"×\", \"‰\", \"∞\", \"NaN\", \":\"], [\"#,##0.###\", \"%#,##0\", \"¤#,##0.00\", \"#E0\"], \"TRY\", \"₺\", \"Türk Lirası\", {\n  \"AUD\": [\"AU$\", \"$\"],\n  \"BYN\": [u, \"р.\"],\n  \"PHP\": [u, \"₱\"],\n  \"RON\": [u, \"L\"],\n  \"RUR\": [u, \"р.\"],\n  \"THB\": [\"฿\"],\n  \"TRY\": [\"₺\"],\n  \"TWD\": [\"NT$\"]\n}, \"ltr\", plural];\n"], "mappings": ";;;AAQA,IAAM,IAAI;AACV,SAAS,OAAO,KAAK;AACnB,QAAM,IAAI;AACV,MAAI,MAAM,EAAG,QAAO;AACpB,SAAO;AACT;AACA,IAAO,aAAQ,CAAC,MAAM,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,SAAS,aAAa,QAAQ,YAAY,YAAY,QAAQ,WAAW,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,GAAG,CAAC,QAAQ,SAAS,QAAQ,SAAS,SAAS,WAAW,UAAU,WAAW,SAAS,QAAQ,SAAS,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,iBAAiB,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,WAAW,YAAY,eAAe,GAAG,CAAC,SAAS,YAAY,cAAc,eAAe,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,GAAG,GAAG,CAAC,aAAa,UAAU,aAAa,KAAK,GAAG,OAAO,KAAK,eAAe;AAAA,EACt3B,OAAO,CAAC,OAAO,GAAG;AAAA,EAClB,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,GAAG;AAAA,EACd,OAAO,CAAC,GAAG,IAAI;AAAA,EACf,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,GAAG;AAAA,EACX,OAAO,CAAC,KAAK;AACf,GAAG,OAAO,MAAM;", "names": []}