import { AsyncPipe } from '@angular/common';
import { Component, inject, input, output, signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIcon } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { RouterLink } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import { RouteDto } from '@proxy/mobile/routes/dtos';
import { CustomLine, CustomMarker, MapComponent } from '@shared/components/map/map.component';
import { hexToColor } from '@shared/functions/hex-to-color';
import { live_icon } from '@shared/helper-assets/live';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';
import { BehaviorSubject } from 'rxjs';
@Component({
  selector: 'app-route-accordion',
  standalone: true,
  template: ` <mat-expansion-panel (opened)="GetInfo(route().id)">
    <mat-expansion-panel-header>
      <mat-panel-title class="flex" (click)="$event.stopPropagation(); $event.preventDefault()">
        <button class="e" mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button mat-menu-item (click)="Delete(route())">
            <mat-icon>delete</mat-icon>
            <span>{{ 'remove' | i18n }}</span>
          </button>
          <button mat-menu-item [routerLink]="['add-point', route().id]">
            <mat-icon>add</mat-icon>
            <span>{{ 'add new point' | i18n }}</span>
          </button>
        </mat-menu>
        {{ route().name }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="h-48 w-full">
        <app-map
          [line]="(opendroutes$() | async)[route().id]"
          [nodes]="(openedNodes$() | async)[route().id]"
          [options]="options$()"
        />
      </div>
    </ng-template>
  </mat-expansion-panel>`,
  imports: [
    MatExpansionModule,
    MatDialogModule,
    MapComponent,
    MatIcon,
    AsyncPipe,
    MatDialogModule,
    LanguagePipe,
    MatMenuModule,
    RouterLink,
  ],
})
export class RouteAccordionComponent {
  private routeService = inject(RouteService);

  route = input<RouteDto>();
  opendroutes$ = input<BehaviorSubject<{ [key: string]: Layer[] }>>();
  openedNodes$ = input<BehaviorSubject<{ [key: string]: Layer[] }>>();
  getData = output();

  options$ = signal<MapOptions>({});
  GetInfo(id) {
    if (!this.opendroutes$().value[id]) {
      this.routeService.get(id).subscribe(val => {
        const r = val.line.map(v => [v.latitudeY, v.longitudeX]);
        const n: { latlang: LatLngTuple; icon: string }[] = val.stopPoints.map(v => {
          return {
            latlang: [v.point.latitudeY, v.point.longitudeX],
            icon: live_icon(hexToColor(v.color)),
          };
        });
        this.openedNodes$().next({
          ...this.openedNodes$().value,
          [id]: n.map(v => CustomMarker(v)),
        });
        this.opendroutes$().next({
          ...this.opendroutes$().value,
          [id]: [CustomLine({ line: r, color: hexToColor(val.color) })],
        });
        this.options$.set({ zoom: 12, center: latLng(r[0] as LatLngTuple) });
      });
    }
  }

  Delete(route) {
    this.routeService.delete(route.id).subscribe(() => {
      this.getData.emit();
    });
  }
}

@Component({
  selector: 'app-pre-defined-routes',
  standalone: true,
  templateUrl: `./pre-defined-routes.component.html`,
  imports: [
    MatExpansionModule,
    MatDialogModule,
    MatButton,
    MatDialogModule,
    LanguagePipe,
    MatMenuModule,
    RouterLink,
    RouteAccordionComponent,
  ],
})
export class PreDefinedRoutesComponent {
  private routeService = inject(RouteService);
  routes$ = signal<RouteDto[]>([]);

  opendroutes$: BehaviorSubject<{ [key: string]: Layer[] }> = new BehaviorSubject({});
  openedNodes$: BehaviorSubject<{
    [key: string]: Layer[];
  }> = new BehaviorSubject({});

  ngOnInit(): void {
    this.GetRoutes();
  }
  GetRoutes() {
    this.routeService.getList({ maxResultCount: 1000, skipCount: 0 }).subscribe(val => {
      this.routes$.set(val.items);
    });
  }
}
