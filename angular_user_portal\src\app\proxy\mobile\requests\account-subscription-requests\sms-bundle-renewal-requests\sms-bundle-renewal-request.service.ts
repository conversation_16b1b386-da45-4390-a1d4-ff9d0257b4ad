import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { CreateAccountRequestPaymentDto } from '../models';
import type { BillDto } from '../../../payments/bills/dtos/models';
import type { CreateSmsBundleRenewalRequestDto, SmsBundleRenewalRequestDto } from '../../sms-bundle-renewal-requests/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class SmsBundleRenewalRequestService {
  apiName = 'Default';
  

  create = (createSmsBundleRenewalRequestDto: CreateSmsBundleRenewalRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/smsBundleRenewalRequest',
      body: createSmsBundleRenewalRequestDto,
    },
    { apiName: this.apiName,...config });
  

  createPayment = (input: CreateAccountRequestPaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/smsBundleRenewalRequest/payment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  createTempBill = (input: CreateSmsBundleRenewalRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/smsBundleRenewalRequest/tempBill',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (requestId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, SmsBundleRenewalRequestDto>({
      method: 'GET',
      url: '/api/app/smsBundleRenewalRequest',
      params: { requestId },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
