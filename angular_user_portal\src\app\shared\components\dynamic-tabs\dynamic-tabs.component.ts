import { NgComponentOutlet } from '@angular/common';
import { ChangeDetectorRef, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';

import { MatCardModule } from '@angular/material/card';
import { MatLabel } from '@angular/material/form-field';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-dynamic-tabs',
  standalone: true,
  imports: [MatCardModule, MatTabsModule, NgComponentOutlet, LanguagePipe, MatLabel],
  templateUrl: './dynamic-tabs.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DynamicTabsComponent {
  description = input<string>('');
  tabs = input<{ label: string; iconActive?: string; iconInactive?: string; component: any }[]>([]);
  tabChanged = output<number>();

  activeTabIndex: number = 0;

  constructor(private cdr: ChangeDetectorRef) {}

  onTabChange(event: MatTabChangeEvent) {
    this.activeTabIndex = event.index;
    this.cdr.detectChanges();
    this.tabChanged.emit(this.activeTabIndex);
  }
}
