import { AuthService, SessionStateService } from '@abp/ng.core';
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';

import { HttpHeaders } from '@angular/common/http';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { Router } from '@angular/router';
import { GeoNodeService } from '@proxy/mobile/geo-nodes';
import {
  MobileIdentityUserService,
  MobileUserUpdateProfileDto,
} from '@proxy/mobile/mobile-identity-users';
import { OtpService } from '@proxy/mobile/otps';
import { car_svg, parseJwt } from '@shared';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';
import { UpdateProfileFormComponent } from './components/update-profile-form/update-profile-form.component';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: `./login.component.html`,
  styleUrl: `./login.component.scss`,
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    },
  ],
  imports: [
    ValidationComponent,
    MatStepperModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    LanguagePipe,
    UpdateProfileFormComponent,
    MatMenuModule,
  ],
})
export class LoginComponent {
  index: number;
  personal = true;

  arrays = {
    Country: [],
    Governorate: [],
    City: [],
    Area: [],
  };

  private _formBuilder = inject(FormBuilder);
  private otpService = inject(OtpService);
  private authService = inject(AuthService);
  private router = inject(Router);
  private mobileIdentityUserService = inject(MobileIdentityUserService);
  private geoNodeService = inject(GeoNodeService);
  private session = inject(SessionStateService);

  car_svg = car_svg;

  phone = this._formBuilder.group({
    phone: ['', Validators.required],
  });
  confirm = this._formBuilder.group({
    confirm: ['', Validators.required],
  });
  info = this._formBuilder.group({
    firstName: this._formBuilder.control('', Validators.required),
    lastName: this._formBuilder.control('', Validators.required),
    email: this._formBuilder.control('', [Validators.required, Validators.email]),
  });

  sendOtp(stepper?: MatStepper) {
    this.otpService.sendOtpDevByDto({ msisdn: this.phone.value.phone }).subscribe(val => {
      this.confirm.setValue({ confirm: val });
      if (stepper) stepper.next();
    });
  }
  sendCode(stepper: MatStepper) {
    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
    });
    this.authService
      .loginUsingGrant(
        'otp',
        {
          phone_number: this.phone.value.phone,
          otp: this.confirm.value.confirm,
          scope: 'offline_access',
          client_id: 'GoTrack_Mobile',
        },
        headers
      )
      .then(r => {
        const token = parseJwt(r.access_token);
        if (token.profile_requires_update == 'True') {
          stepper.next();
        } else {
          this.router.navigate(['track-accounts']);
        }
      });
  }

  changeLang(language: 'ar' | 'en') {
    this.session.setLanguage(language);
  }
}
