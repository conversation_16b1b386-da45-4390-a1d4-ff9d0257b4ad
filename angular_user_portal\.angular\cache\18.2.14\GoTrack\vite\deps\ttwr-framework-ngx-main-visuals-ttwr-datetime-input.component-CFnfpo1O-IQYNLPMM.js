import {
  <PERSON><PERSON><PERSON><PERSON>,
  Ttw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TtwrDatetimepickerInput,
  TtwrDatetimepickerToggle,
  provideNativeDatetimeAdapter
} from "./chunk-UX2QZIH4.js";
import "./chunk-GNAWW72V.js";
import "./chunk-PU5VAYLU.js";
import "./chunk-SKIPCM4E.js";
import {
  MatIconModule
} from "./chunk-YNPIWMBQ.js";
import {
  MatInput,
  MatInputModule
} from "./chunk-YLNCLJDS.js";
import "./chunk-JTOCISR5.js";
import "./chunk-6U5A2I3P.js";
import "./chunk-6OP7LWI7.js";
import {
  MatButtonModule
} from "./chunk-KQFENTBY.js";
import "./chunk-GLKBSV6C.js";
import "./chunk-YRZJQTH3.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ieldModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "./chunk-OZQQ7DCO.js";
import "./chunk-K2577LFK.js";
import "./chunk-GXD64NHD.js";
import "./chunk-Y5AGGM2N.js";
import "./chunk-SISCE4G2.js";
import "./chunk-ASZRR2AS.js";
import "./chunk-P7EARM5A.js";
import "./chunk-L3STFTJR.js";
import "./chunk-JQS6LOEL.js";
import "./chunk-WXRVJEAW.js";
import "./chunk-M7LED4FC.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import {
  ControlContainer,
  DefaultValueAccessor,
  FormControlDirective,
  FormGroupDirective,
  NgControlStatus,
  ReactiveFormsModule
} from "./chunk-JP2LMHJE.js";
import "./chunk-6D52GKB4.js";
import {
  Component,
  input,
  setClassMetadata,
  ɵɵProvidersFeature,
  ɵɵStandaloneFeature,
  ɵɵadvance,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵreference,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-QGPYGS5J.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@ttwr-framework/ngx-main-visuals/fesm2022/ttwr-framework-ngx-main-visuals-ttwr-datetime-input.component-CFnfpo1O.mjs
var _forTrack0 = ($index, $item) => $item.name;
var _c0 = () => [];
function TtwrDatetimeInputComponent_For_10_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "span", 4);
    ɵɵtext(1);
    ɵɵpipe(2, "i18n");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    const validator_r1 = ctx.$implicit;
    const ctx_r1 = ɵɵnextContext();
    ɵɵproperty("hidden", !ctx_r1.field().control.hasError(validator_r1.name));
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 2, validator_r1.message), " ");
  }
}
function TtwrDatetimeInputComponent_Conditional_11_Template(rf, ctx) {
  if (rf & 1) {
    ɵɵelementStart(0, "mat-hint");
    ɵɵtext(1);
    ɵɵpipe(2, "i18n");
    ɵɵelementEnd();
  }
  if (rf & 2) {
    ɵɵadvance();
    ɵɵtextInterpolate1(" ", ɵɵpipeBind1(2, 1, ctx), " ");
  }
}
var TtwrDatetimeInputComponent = class _TtwrDatetimeInputComponent {
  constructor() {
    this.field = input.required();
  }
  static {
    this.ɵfac = function TtwrDatetimeInputComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TtwrDatetimeInputComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _TtwrDatetimeInputComponent,
      selectors: [["ttwr-datetime-input"]],
      inputs: {
        field: [1, "field"]
      },
      standalone: true,
      features: [ɵɵProvidersFeature([provideNativeDatetimeAdapter()], [{
        provide: ControlContainer,
        useExisting: FormGroupDirective
      }]), ɵɵStandaloneFeature],
      decls: 12,
      vars: 15,
      consts: [["datetimePicker", ""], [3, "twelvehour", "touchUi", "timeInput", "mode", "type"], ["matInput", "", 3, "ttwrDatetimepicker", "formControl", "readonly"], ["matSuffix", "", 3, "disabled", "for"], [3, "hidden"]],
      template: function TtwrDatetimeInputComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "mat-form-field")(1, "mat-label");
          ɵɵtext(2);
          ɵɵpipe(3, "i18n");
          ɵɵelementEnd();
          ɵɵelement(4, "ttwr-datetimepicker", 1, 0)(6, "input", 2)(7, "ttwr-datetimepicker-toggle", 3);
          ɵɵelementStart(8, "mat-error");
          ɵɵrepeaterCreate(9, TtwrDatetimeInputComponent_For_10_Template, 3, 4, "span", 4, _forTrack0);
          ɵɵelementEnd();
          ɵɵtemplate(11, TtwrDatetimeInputComponent_Conditional_11_Template, 3, 3, "mat-hint");
          ɵɵelementEnd();
        }
        if (rf & 2) {
          let tmp_1_0;
          let tmp_2_0;
          let tmp_3_0;
          let tmp_4_0;
          let tmp_5_0;
          let tmp_6_0;
          let tmp_9_0;
          let tmp_10_0;
          let tmp_12_0;
          let tmp_13_0;
          const datetimePicker_r3 = ɵɵreference(5);
          ɵɵadvance(2);
          ɵɵtextInterpolate(ɵɵpipeBind1(3, 12, (tmp_1_0 = ctx.field().label) !== null && tmp_1_0 !== void 0 ? tmp_1_0 : ctx.field().name));
          ɵɵadvance(2);
          ɵɵproperty("twelvehour", (tmp_2_0 = ctx.field().twelveHour) !== null && tmp_2_0 !== void 0 ? tmp_2_0 : false)("touchUi", (tmp_3_0 = (tmp_3_0 = ctx.field().touchUISignal) == null ? null : tmp_3_0()) !== null && tmp_3_0 !== void 0 ? tmp_3_0 : false)("timeInput", (tmp_4_0 = ctx.field().timeInput) !== null && tmp_4_0 !== void 0 ? tmp_4_0 : true)("mode", (tmp_5_0 = ctx.field().mode) !== null && tmp_5_0 !== void 0 ? tmp_5_0 : "auto")("type", (tmp_6_0 = ctx.field().pickerType) !== null && tmp_6_0 !== void 0 ? tmp_6_0 : "datetime");
          ɵɵadvance(2);
          ɵɵproperty("ttwrDatetimepicker", datetimePicker_r3)("formControl", ctx.field().control)("readonly", (tmp_9_0 = ctx.field().readonlySignal) == null ? null : tmp_9_0());
          ɵɵadvance();
          ɵɵproperty("disabled", ((tmp_10_0 = ctx.field().disabledSignal) == null ? null : tmp_10_0()) || ((tmp_10_0 = ctx.field().readonlySignal) == null ? null : tmp_10_0()))("for", datetimePicker_r3);
          ɵɵadvance(2);
          ɵɵrepeater((tmp_12_0 = ctx.field().validators) !== null && tmp_12_0 !== void 0 ? tmp_12_0 : ɵɵpureFunction0(14, _c0));
          ɵɵadvance(2);
          ɵɵconditional((tmp_13_0 = ctx.field().hint) ? 11 : -1, tmp_13_0);
        }
      },
      dependencies: [LanguagePipe, MatFormFieldModule, MatFormField, MatLabel, MatHint, MatError, MatSuffix, MatInputModule, MatInput, MatButtonModule, MatIconModule, ReactiveFormsModule, DefaultValueAccessor, NgControlStatus, FormControlDirective, TtwrDatetimepickerToggle, TtwrDatetimepickerInput, TtwrDatetimepicker],
      styles: ["mat-form-field[_ngcontent-%COMP%]{width:100%}"]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TtwrDatetimeInputComponent, [{
    type: Component,
    args: [{
      selector: "ttwr-datetime-input",
      standalone: true,
      imports: [LanguagePipe, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, ReactiveFormsModule, TtwrDatetimepickerToggle, TtwrDatetimepickerInput, TtwrDatetimepicker],
      viewProviders: [{
        provide: ControlContainer,
        useExisting: FormGroupDirective
      }],
      providers: [provideNativeDatetimeAdapter()],
      template: `<mat-form-field>\r
  <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>\r
  <ttwr-datetimepicker\r
    [twelvehour]="field().twelveHour ?? false"\r
    [touchUi]="field().touchUISignal?.() ?? false"\r
    [timeInput]="field().timeInput ?? true"\r
    [mode]="field().mode ?? 'auto'"\r
    [type]="field().pickerType ?? 'datetime'"\r
    #datetimePicker\r
  />\r
  <input\r
    matInput\r
    [ttwrDatetimepicker]="datetimePicker"\r
    [formControl]="field().control"\r
    [readonly]="field().readonlySignal?.()"\r
  >\r
  <ttwr-datetimepicker-toggle\r
    [disabled]="field().disabledSignal?.() || field().readonlySignal?.()"\r
    [for]="datetimePicker"\r
    matSuffix\r
  />\r
  <mat-error>\r
    @for (validator of field().validators ?? []; track validator.name) {\r
      <span [hidden]="!field().control.hasError(validator.name)">\r
          {{ validator.message | i18n }}\r
        </span>\r
    }\r
  </mat-error>\r
  @if (field().hint; as hint) {\r
    <mat-hint>\r
      {{ hint | i18n }}\r
    </mat-hint>\r
  }\r
</mat-form-field>\r
`,
      styles: ["mat-form-field{width:100%}\n"]
    }]
  }], null, null);
})();
export {
  TtwrDatetimeInputComponent
};
//# sourceMappingURL=ttwr-framework-ngx-main-visuals-ttwr-datetime-input.component-CFnfpo1O-IQYNLPMM.js.map
