import type { AddVehiclesRequestDto, CreateAddVehiclesRequestsDto, CreateAddVehiclesRequestsPaymentDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { BillDto } from '../../payments/bills/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class AddVehiclesRequestService {
  apiName = 'Default';
  

  craetePayment = (input: CreateAddVehiclesRequestsPaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/addVehiclesRequest/craetePayment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  create = (input: CreateAddVehiclesRequestsDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/addVehiclesRequest',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  createTempBill = (input: CreateAddVehiclesRequestsDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/addVehiclesRequest/tempBill',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AddVehiclesRequestDto>({
      method: 'GET',
      url: `/api/app/addVehiclesRequest/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AddVehiclesRequestDto>>({
      method: 'GET',
      url: '/api/app/addVehiclesRequest',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
