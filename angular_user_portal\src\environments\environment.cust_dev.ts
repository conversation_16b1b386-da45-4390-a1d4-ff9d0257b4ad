import { Environment } from '@abp/ng.core';

const baseUrl = 'https://gotrack-beta.dev05.tatweer.sy';

export const environment = {
  production: true,
  application: {
    baseUrl,
    name: 'GoTrack',
    logoUrl: '',
  },
  oAuthConfig: {
    issuer: 'https://gotrack-httpapihost-beta.dev05.tatweer.sy/',
    redirectUri: baseUrl,
    clientId: 'GoTrack_App',
    responseType: 'code',
    scope: 'offline_access GoTrack',
    requireHttps: true,
  },
  apis: {
    default: {
      url: 'https://gotrack-httpapihost-beta.dev05.tatweer.sy',
      rootNamespace: 'GoTrack',
    },
  },
} as Environment;

