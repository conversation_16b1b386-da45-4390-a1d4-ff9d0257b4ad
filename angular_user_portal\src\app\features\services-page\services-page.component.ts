import { LocalizationModule } from '@abp/ng.core';
import { NgClass } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { FEATURES } from '@shared/constants/features-token';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-services-page',
  templateUrl: './services-page.component.html',
  standalone: true,
  imports: [LocalizationModule, RouterLink, LanguagePipe, NgClass],
  styles: `
    .item {
      @apply rounded-xl flex justify-center items-center  size-40 cursor-pointer;//rotate-45
    }
    .inner_item {
      @apply  text-center align-middle w-2/3 h-2/3 text-white;//-rotate-45
    }
  `,
})
export class ServicesPageComponent {
  feature = inject(FEATURES);
}
