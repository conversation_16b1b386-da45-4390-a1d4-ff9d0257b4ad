import { LocalizationModule } from '@abp/ng.core';
import { KeyValuePipe, NgClass } from '@angular/common';
import { Component, computed, inject, Signal, signal, WritableSignal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormField } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatOption, MatSelect } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';
import { RouterLink } from '@angular/router';
import { MonitoringService } from '@proxy/mobile/monitoring';
import { LiveLocationDto } from '@proxy/mobile/monitoring/dtos/live-locations';
import { VehicleReportService } from '@proxy/mobile/reports';
import { VehicleDistanceAverageAndMaxSpeedReportDto } from '@proxy/mobile/reports/dto';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { car_svg } from '@shared';
import { FEATURES } from '@shared/constants/features-token';
import { hexToColor } from '@shared/functions/hex-to-color';
import { popup } from '@shared/helper-assets/popup';
import { LanguagePipe, LanguageService } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';
import { filter, firstValueFrom, forkJoin, map, skip, switchMap, tap } from 'rxjs';
import { CustomMarker, MapComponent } from '../../shared/components/map/map.component';

@Component({
  selector: 'app-main-page',
  templateUrl: './main-page.component.html',
  standalone: true,
  imports: [
    LocalizationModule,
    MatFormField,
    MatSelect,
    MatOption,
    FormsModule,
    ReactiveFormsModule,
    NgClass,
    LanguagePipe,
    KeyValuePipe,
    MatButtonModule,
    MatIcon,
    RouterLink,
    MatTooltip,
    MapComponent,
  ],
  styles: `
  :host {
  --mdc-outlined-text-field-container-shape: 10px;
  --mat-form-field-container-vertical-padding: 10px;
  --mat-form-field-container-height: 44px;
  --mdc-filled-button-container-shape: 10px;
  --mdc-filled-button-container-color: #7164e4;
  }
  `,
})
export class MainPageComponent {
  displayDialog$ = signal(true);
  layer$ = signal(true);
  options$ = signal<MapOptions>({ zoom: 12, center: latLng(34.71344, 38.6883) });

  nodes$ = signal<Layer[]>([]);

  vehicleService = inject(VehicleService);
  features = inject(FEATURES);
  features$ = toObservable(this.features);
  languageService = inject(LanguageService);
  monitoringService = inject(MonitoringService);
  vehicleReportService = inject(VehicleReportService);

  times = {
    now: 10 * 60 * 1000,
    hour: 60 * 60 * 1000,
    hour24: 24 * 60 * 60 * 1000,
  };

  time$ = signal(this.times.now);
  LayerChange$ = signal<boolean>(true);

  selectedVehicle = new FormControl(null);

  selectedVehicle$: Signal<VehicleDto | null> = signal(null);
  selectedVehcileInfo$: WritableSignal<VehicleDistanceAverageAndMaxSpeedReportDto | any> = signal(
    {}
  );

  vehicles$ = signal<VehicleDto[]>([]);

  vehiclesIds$ = computed<string[]>(() => {
    return this.selectedVehicle$()
      ? [this.selectedVehicle$().id]
      : this.vehicles$().map(val => val.id);
  });

  monitorInfo$ = toObservable(this.vehiclesIds$).pipe(
    switchMap(val => {
      return this.monitoringService.postLiveLocation({
        onlySpeed: true,
        vehicleIds: val,
      });
    }),
    tap(val => {
      const ids = val.map(v => v.vehicleId);
      const trackedVehicles = this.vehicles$().filter(val => {
        return ids.includes(val.id);
      });
      if (this.vehicles$().length != trackedVehicles.length) this.vehicles$.set(trackedVehicles);
    })
  );

  constructor() {
    this.selectedVehicle$ = toSignal(this.selectedVehicle.valueChanges);
    const temp = computed(() => {
      const sel = this.selectedVehicle$();
      return { veh: sel, time: this.time$() };
    });
    toObservable(temp)
      .pipe(
        skip(1),
        filter(val => {
          return !!val.veh;
        }),
        switchMap(val => {
          return this.report(val.veh.id, new Date(+new Date() - val.time), new Date());
        })
      )
      .subscribe(val => {
        this.selectedVehcileInfo$.set(val);
      });
  }
  ngOnInit(): void {
    this.features$
      .pipe(
        filter(v => {
          return v['GoTrack.LiveMonitoring'];
        }),
        switchMap(() => {
          const first = this.monitorInfo$.pipe(
            map(v => {
              v.map(val => {
                const { longitude, latitude } = val.warpGtses[0].values[0];
                this.addMarker(+latitude, +longitude, val);
              });
              if (v.length > 0) {
                const { longitude, latitude } = v[v.length - 1].warpGtses[0].values[0];
                this.options$.update(v => {
                  return { ...v, center: latLng(+latitude, +longitude), zoom: 17 };
                });
              }
            })
          );
          const seq = this.vehicleService.getList({ maxResultCount: 1000, skipCount: 0 }).pipe(
            map(val => {
              const res = val.items.map(val => {
                val.colorHex = hexToColor(val.colorHex);
                return val;
              });
              this.vehicles$.set(res);
            })
          );
          return forkJoin([first, seq]);
        })
      )
      .subscribe();
  }

  async addMarker(latitude: number, longitude: number, info: LiveLocationDto) {
    const vehicle: VehicleDto = this.vehicles$().find(val => val.id === info.vehicleId);
    const popupTemplate = () => {
      const el = document.createElement('div');
      el.classList.add('w-[150px]');
      let html = ``;
      firstValueFrom(this.report(info.vehicleId)).then(r => {
        html += popup({
          status: r.isActive ? 'active' : 'Not Active',
          avg_speed: r.averageSpeed,
          feul: vehicle.consumptionRate,
          number: vehicle.licensePlateSerial,
          ls: this.languageService,
        });
        el.innerHTML = html;
      });
      return el;
    };
    this.nodes$.update(val => {
      return [
        ...val,
        CustomMarker({
          icon: car_svg(hexToColor(vehicle.colorHex)),
          latlang: [latitude, longitude] as LatLngTuple,
          popup: popupTemplate.bind(this),
        }),
      ];
    });
  }

  report(vehicleId: string, from: Date = new Date(), to: Date = new Date()) {
    return this.vehicleReportService.getVehicleDistanceAverageAndMaxSpeedReport({
      vehicleId: vehicleId,
      ignoreSpeedUnder: 0,
      fromDate: from.toISOString(),
      toDate: to.toISOString(),
    });
  }

  changeLayer() {
    this.LayerChange$.set(!this.LayerChange$());
  }

  ngOnDestroy(): void {}
}
