import { CommonModule } from '@angular/common';
import { Component, inject, Signal, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { Router } from '@angular/router';
import { AlertDefinitionService } from '@proxy/mobile/alert-definitions';
import { AlertDefinitionDto } from '@proxy/mobile/alert-definitions/dtos';
import { ExceedingSpeedAlertDefinitionService } from '@proxy/mobile/alert-definitions/exceeding-speed-alert-definitions';
import { AlertService, LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, firstValueFrom, switchMap } from 'rxjs';
import { openAddVehiclesDialog } from '../../../../shared/components/add-vehicles-dialog/add-vehicles-dialog.component';
import { openAddVehiclesGroupDialog } from '../../../../shared/components/add-vehicles-group-dialog/add-vehicles-group-dialog.component';
import { ShowAlertRoutesComponent } from './component/show-route-dialog/show-route-dialog.component';
import { ShowGeozoneDialogComponent } from './component/show-geozone-dialog/show-geozone-dialog.component';
import { AlertType } from '@proxy/alert-definitions';
import { openRelatedItemsDialog } from '@shared/components/related-items-dialog/related-items-dialog.component';

@Component({
  selector: 'app-scheduled-alarms',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    LanguagePipe,
    MatMenuModule,
    MatProgressSpinner,
    MatSlideToggle,
  ],
  templateUrl: './scheduled-alarms.component.html',
})
export class ScheduledAlarmsComponent {
  isLoading = signal<boolean>(false);
  private router = inject(Router);
  private dialog = inject(MatDialog);
  private alertService = inject(AlertDefinitionService);
  private ttwrAlert = inject(AlertService);
  alertsInfo = signal<{ [key: string]: Signal<AlertDefinitionDto & { maxSpeed: number }> }>({});

  alerts$ = signal<(AlertDefinitionDto & { maxSpeed: number })[]>([]);

  private ExceedingSpeedAlertDefinitionService = inject(ExceedingSpeedAlertDefinitionService);

  ngOnInit(): void {
    this.loadAlerts();
  }
  loadAlerts() {
    this.alertService.getList({ type: '', skipCount: 0, maxResultCount: 10 }, {}).subscribe(val => {
      this.alerts$.set(val.items as any);
    });
  }

  navigateToAddAlert() {
    this.router.navigate(['/main/alerts/add-alert']);
  }

  addGroups(alert: AlertDefinitionDto) {
    openAddVehiclesGroupDialog(this.dialog, alert.id)
      .pipe(
        filter(val => !!val),
        switchMap(val => this.alertService.addVehicleGroup(alert.id, val))
      )
      .subscribe(() => {
        this.loadAlerts();
      });
  }

  addVehicles(alert: AlertDefinitionDto) {
    openAddVehiclesDialog(this.dialog, alert.id)
      .pipe(
        filter(val => !!val),
        switchMap(val => this.alertService.addVehicle(alert.id, val))
      )
      .subscribe(() => {
        this.loadAlerts();
      });
  }

  deleteAlert(alert: AlertDefinitionDto): void {
    this.alertService.remove(alert.id).subscribe(() => {
      this.loadAlerts();
    });
  }

  toggleAlert(event: any, alert: AlertDefinitionDto): void {
    const isChecked = event.checked;
    this.isLoading.set(true);
    const toggleAction = isChecked
      ? this.alertService.enabledAlert(alert.id)
      : this.alertService.disabledAlert(alert.id);

    toggleAction.subscribe({
      next: () => {
        this.ttwrAlert.success(isChecked ? 'SuccessEnabled' : 'SuccessDisabled');
      },
      complete: () => {
        this.loadAlerts();
        this.isLoading.set(false);
      },
    });
  }

  async viewAssignedVehicles(alert: AlertDefinitionDto) {
    const vehicles = await firstValueFrom(
      this.alertService.getVehicles(alert.id, { skipCount: 0, maxResultCount: 100 })
    );
    openRelatedItemsDialog({ items: vehicles.items, type: 'VEHICLE', dialog: this.dialog })
      .pipe(
        filter((data: any) => data && data?.action),
        switchMap(data => {
          return this.alertService.removeVehicle(alert.id, data.id);
        })
      )
      .subscribe(() => {
        this.loadAlerts();
      });
  }

  async viewAssignedGroups(alert: AlertDefinitionDto) {
    const vehicles = await firstValueFrom(
      this.alertService.getVehicleGroups(alert.id, { skipCount: 0, maxResultCount: 10 })
    );
    openRelatedItemsDialog({
      items: vehicles.items,
      type: 'VEHICLE_GROUP',
      dialog: this.dialog,
    })
      .pipe(
        filter((data: any) => data && data?.action),
        switchMap(data => {
          return this.alertService.removeVehicleGroup(alert.id, data.id);
        })
      )
      .subscribe(() => {
        this.loadAlerts();
      });
  }

  getInfo(id: string, type: string) {
    if (type == AlertType.ExceedingSpeed)
      this.ExceedingSpeedAlertDefinitionService.get(id).subscribe(val => {
        this.alertsInfo.update(alerts => {
          return { ...alerts, [id]: signal(val) };
        });
      });
  }

  showRoute(id: string) {
    this.dialog.open(ShowAlertRoutesComponent, {
      height: '95%',
      minWidth: '50%',
      data: id,
    });
  }
  showGeozone(id: string, type: string) {
    this.dialog.open(ShowGeozoneDialogComponent, {
      height: '95%',
      minWidth: '50%',
      data: { id, type },
    });
  }
}
