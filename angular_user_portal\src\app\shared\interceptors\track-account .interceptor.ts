import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable } from 'rxjs';

export function TrackAccountInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> {
  const router = inject(Router);
  if (router.url.includes('main') || localStorage.getItem('tempTrackAccountId')) {
    const newReq = req.clone({
      headers: req.headers
        // .set('Content-Type', 'application/json')
        .set(
          'TrackAccountId',
          localStorage.getItem('trackAccountId')
            ? localStorage.getItem('trackAccountId')
            : localStorage.getItem('tempTrackAccountId') || ''
        ),
    });
    return next(newReq);
  } else {
    return next(req);
  }
}
