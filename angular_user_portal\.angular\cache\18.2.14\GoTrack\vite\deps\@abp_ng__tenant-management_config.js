import {
  RoutesService
} from "./chunk-3CXWNYMM.js";
import "./chunk-YKHGT5DJ.js";
import "./chunk-M7LED4FC.js";
import "./chunk-OG4RIIRZ.js";
import "./chunk-SQ2XSFGA.js";
import "./chunk-JP2LMHJE.js";
import "./chunk-6D52GKB4.js";
import {
  APP_INITIALIZER,
  NgModule,
  makeEnvironmentProviders,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-QGPYGS5J.js";
import "./chunk-GJSJXBTC.js";
import "./chunk-BTHIXAM7.js";
import "./chunk-DJECZSZD.js";
import "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.tenant-management/fesm2022/abp-ng.tenant-management-config.mjs
var TENANT_MANAGEMENT_ROUTE_PROVIDERS = [{
  provide: APP_INITIALIZER,
  useFactory: configureRoutes,
  deps: [RoutesService],
  multi: true
}];
function configureRoutes(routes) {
  return () => {
    routes.add([{
      path: void 0,
      name: "AbpTenantManagement::Menu:TenantManagement",
      parentName: "AbpUiNavigation::Menu:Administration",
      requiredPolicy: "AbpTenantManagement.Tenants",
      layout: "application",
      iconClass: "fa fa-users",
      order: 2
    }, {
      path: "/tenant-management/tenants",
      name: "AbpTenantManagement::Tenants",
      parentName: "AbpTenantManagement::Menu:TenantManagement",
      requiredPolicy: "AbpTenantManagement.Tenants",
      order: 1
    }]);
  };
}
function provideTenantManagementConfig() {
  return makeEnvironmentProviders([TENANT_MANAGEMENT_ROUTE_PROVIDERS]);
}
var TenantManagementConfigModule = class _TenantManagementConfigModule {
  static forRoot() {
    return {
      ngModule: _TenantManagementConfigModule,
      providers: [provideTenantManagementConfig()]
    };
  }
  static {
    this.ɵfac = function TenantManagementConfigModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TenantManagementConfigModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _TenantManagementConfigModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TenantManagementConfigModule, [{
    type: NgModule
  }], null, null);
})();
export {
  TENANT_MANAGEMENT_ROUTE_PROVIDERS,
  TenantManagementConfigModule,
  configureRoutes,
  provideTenantManagementConfig
};
//# sourceMappingURL=@abp_ng__tenant-management_config.js.map
