import type { TrackAccountSubscriptionDetailDto, TrackAccountSubscriptionDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TrackAccountSubscriptionService {
  apiName = 'Default';
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, TrackAccountSubscriptionDetailDto>({
      method: 'GET',
      url: `/api/app/trackAccountSubscription/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getCurrentSubscription = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, TrackAccountSubscriptionDetailDto>({
      method: 'GET',
      url: '/api/app/trackAccountSubscription/currentSubscription',
    },
    { apiName: this.apiName,...config });
  

  getList = (requestDto: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<TrackAccountSubscriptionDto>>({
      method: 'GET',
      url: '/api/app/trackAccountSubscription',
      params: { skipCount: requestDto.skipCount, maxResultCount: requestDto.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
