import "./chunk-ZTELYOIP.js";

// node_modules/@angular/common/locales/tr.mjs
var u = void 0;
function plural(val) {
  const n = val;
  if (n === 1) return 1;
  return 5;
}
var tr_default = ["tr", [["öö", "ös"], ["Ö<PERSON>", "ÖS"], u], [["ÖÖ", "ÖS"], u, u], [["P", "P", "S", "Ç", "P", "C", "C"], ["Paz", "Pzt", "Sal", "Çar", "Per", "Cum", "Cmt"], ["Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"], ["<PERSON>", "Pt", "Sa", "Ça", "Pe", "Cu", "Ct"]], u, [["O", "Ş", "M", "N", "M", "H", "T", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ğ<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>s", "<PERSON>"], ["Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "<PERSON>ylül", "Ekim", "Kasım", "Aralık"]], u, [["MÖ", "MS"], u, ["Milattan Önce", "Milatt<PERSON> Sonra"]], 1, [6, 0], ["d.MM.y", "d MMM y", "d MMMM y", "d MMMM y EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "%#,##0", "¤#,##0.00", "#E0"], "TRY", "₺", "Türk Lirası", {
  "AUD": ["AU$", "$"],
  "BYN": [u, "р."],
  "PHP": [u, "₱"],
  "RON": [u, "L"],
  "RUR": [u, "р."],
  "THB": ["฿"],
  "TRY": ["₺"],
  "TWD": ["NT$"]
}, "ltr", plural];
export {
  tr_default as default
};
/*! Bundled license information:

@angular/common/locales/tr.mjs:
  (**
   * @license
   * Copyright Google LLC All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.dev/license
   *)
*/
//# sourceMappingURL=tr-U4CQM4BW.js.map
