import { Routes } from '@angular/router';
import { routes as abpOverridesRoutes } from './abp-overrides';
import { NotFoundComponent } from './common/not-found.component';
import { routes as alertsRoutes } from './features/alerts/alerts.routes';
import { routes as routesRoutes } from './features/routes/routes.routes';
import { MainPageComponent } from './features/main-page/main-page.component';
import { routes as moreRoutes } from './features/more/more.routes';
import { routes as observersManagementRoutes } from './features/observers-management/observers-management.routes';
import { routes as groupsManagementRoutes } from './features/groups-management/groups-management.routes';
import { ServicesPageComponent } from './features/services-page/services-page.component';
import { SubscriptionRequestsViewComponent } from './features/subscription-requests/subscription-requests-view/subscription-requests-view.component';
import { SubscriptionRequestsComponent } from './features/subscription-requests/subscription-requests.component';
import { CreateTrackAccountComponent } from './features/track-accounts/components/create-track-account/create-track-account.component';
import { TrackAccountsComponent } from './features/track-accounts/track-accounts.component';
import { LayoutComponent } from './layout/layout.component';
import { PlansComponent } from './features/plans/plans.component';
import { TrackVehicleComponent } from './features/main-page/track-vehicle/track-vehicle.component';
import { TrackVehicleHistoryComponent } from './features/main-page/track-vehicle-history/track-vehicle-history.component';
import { LoginComponent } from './features/login/login.component';
import { NotificationsComponent } from './features/notifications/notifications.component';
import { RenewSmsBundelComponent } from './features/track-accounts/components/renew-sms-bundel/renew-sms-bundel.component';
import { IncreaseVehicleComponent } from './features/track-accounts/components/increase-vehicle/increase-vehicle.component';
import { RenewSubscriptionComponent } from './features/track-accounts/components/renew-subscription/renew-subscription.component';
import { AccountsProfileComponent } from './features/accounts-profile/accounts-profile.component';

export const routes: Routes = [
  {
    path: '',
    component: LoginComponent,
  },
  {
    path: 'track-accounts',
    children: [
      {
        path: '',
        component: TrackAccountsComponent,
      },
      {
        path: 'sms-bundle-renewal/:id',
        component: RenewSmsBundelComponent,
      },
      {
        path: 'increase-vehicle/:id',
        component: IncreaseVehicleComponent,
      },
      {
        path: 'renew-subscription/:id',
        component: RenewSubscriptionComponent,
      },
      {
        path: 'create',
        component: CreateTrackAccountComponent,
      },
    ],
  },
  {
    path: 'subscription-requests',
    children: [
      {
        path: '',
        component: SubscriptionRequestsComponent,
      },
      {
        path: ':type/:id',
        component: SubscriptionRequestsViewComponent,
      },
    ],
  },
  { path: 'plans', component: PlansComponent },
  { path: 'more', children: moreRoutes },

  // ==== features routes ====
  {
    path: 'main',
    component: LayoutComponent,
    children: [
      { path: '', component: MainPageComponent },
      { path: 'track/:id', component: TrackVehicleComponent },
      { path: 'account-profile', component: AccountsProfileComponent },
      { path: 'history/:id', component: TrackVehicleHistoryComponent },
      { path: 'services', component: ServicesPageComponent },
      { path: 'alerts', children: alertsRoutes },
      { path: 'notifications', component: NotificationsComponent },
      { path: 'observers-management', children: observersManagementRoutes },
      { path: 'groups-management', children: groupsManagementRoutes },
      { path: 'routes', children: routesRoutes },
    ],
  },
  // ...abpOverridesRoutes,
  // ==== catch rest urls === =
  {
    path: '**',
    component: NotFoundComponent,
  },
];
