import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'account',
    loadChildren: () => import('./account/accounts.routes').then(m => m.routes),
  },
  {
    path: 'identity',
    loadChildren: () => import('./identity/identity.routes').then(m => m.routes),
  },
  {
    path: 'tenant-management',
    loadChildren: () => import('./tenant-management/tenant-management.routes').then(m => m.routes),
  },
  {
    path: 'setting-management',
    loadChildren: () =>
      import('./setting-management/setting-management.routes').then(m => m.routes),
  },
];
