import type { SubscriptionVehicleInfoCreateDto } from '../../account-subscription-requests/models';
import type { RenewSubscriptionRequestStage } from '../../../../renew-track-account-subscriptions/renew-subscription-request-stage.enum';
import type { TrackerInstallationLocation } from '../../../../requests/account-subscription-requests/tracker-installation-location.enum';
import type { RequestDto } from '../../models';

export interface CreateRenewTrackAccountSubscriptionsDto {
  subscriptionPlanKey?: string;
  userCount: number;
  smsBundleId?: string;
  newVehicles: SubscriptionVehicleInfoCreateDto[];
  removeVehicles: string[];
  removeUsers: string[];
  subscriptionDurationInMonths: number;
  promoCode?: string;
}

export interface RenewSubscriptionRequestDetailsDto {
  ownerId?: string;
  ownerFirstName?: string;
  ownerLastName?: string;
  ownerEmail?: string;
  ownerPhoneNumber?: string;
  paymentUrl?: string;
  trackAccountId?: string;
  subscriptionPlanKey?: string;
  subscriptionPlanLocalizedName?: string;
  userCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  discountRate?: number;
  renewSubscriptionRequestStage: RenewSubscriptionRequestStage;
  newTrackVehiclesCount: number;
  removeTrackVehiclesCount: number;
  removeUsersCount: number;
  trackerInstallationLocation?: TrackerInstallationLocation;
  trackAccountSubscriptionId?: string;
}

export interface RenewSubscriptionRequestDto extends RequestDto {
  trackAccountId?: string;
  subscriptionPlanKey?: string;
  trackAccountSubscriptionId?: string;
  userCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  discountRate?: number;
  renewSubscriptionRequestStage: RenewSubscriptionRequestStage;
}
