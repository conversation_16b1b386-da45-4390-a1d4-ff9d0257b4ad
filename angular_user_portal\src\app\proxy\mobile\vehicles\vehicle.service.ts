import type { VehicleDto, VehicleViewModelWithAuditingDto } from './dtos/models';
import type { GetVehicleListRequestDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class VehicleService {
  apiName = 'Default';
  

  addAddToVehicleGroupByIdAndVehicleGroupId = (id: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/vehicle/${id}/addToVehicleGroup/${vehicleGroupId}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VehicleDto>({
      method: 'GET',
      url: `/api/app/vehicle/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (requestDto: GetVehicleListRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VehicleDto>>({
      method: 'GET',
      url: '/api/app/vehicle',
      params: { vehicleGroupId: requestDto.vehicleGroupId, skipCount: requestDto.skipCount, maxResultCount: requestDto.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListVehicleViewModelWithAuditing = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, VehicleViewModelWithAuditingDto[]>({
      method: 'GET',
      url: '/api/app/vehicle/vehicleViewModelWithAuditing',
    },
    { apiName: this.apiName,...config });
  

  removeRemoveFromVehicleGroupByIdAndVehicleGroupId = (id: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/vehicle/${id}/removeFromVehicleGroup/${vehicleGroupId}`,
    },
    { apiName: this.apiName,...config });
  

  updateUpdateConsumptionRateByIdAndConsumptionRate = (id: string, consumptionRate: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/vehicle/${id}/updateConsumptionRate`,
      params: { consumptionRate },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
