import { NgStyle } from '@angular/common';
import { Component, inject, input, signal } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { MatOption, MatSelect } from '@angular/material/select';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { ObservationService } from '@proxy/mobile/observations';
import { DiscountService } from '@proxy/mobile/payments/discounts';
import { SubscriptionDurationDiscountDto } from '@proxy/mobile/payments/discounts/dtos';
import { SubscriptionVehicleInfoCreateDto } from '@proxy/mobile/requests/account-subscription-requests';
import { RenewSubscriptionRequestService } from '@proxy/mobile/requests/renew-subscription-requests';
import { CreateRenewTrackAccountSubscriptionsDto } from '@proxy/mobile/requests/renew-track-account-subscriptions/dtos';
import { SmsBundleService } from '@proxy/mobile/sms-bundles';
import { SmsBundleDto } from '@proxy/mobile/sms-bundles/dtos';
import { SubscriptionPlanService } from '@proxy/mobile/subscription-plans';
import { SubscriptionPlanDto } from '@proxy/mobile/subscription-plans/dtos';
import { TrackAccountSubscriptionService } from '@proxy/mobile/track-accounts/track-account-subscriptions';
import { TrackAccountSubscriptionDto } from '@proxy/mobile/track-accounts/track-account-subscriptions/dtos';
import { UserTrackAccountAssociationDto } from '@proxy/mobile/user-track-account-associations/dtos';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';

import { ValidationComponent } from '@shared/components/validation/validation.component';
import { hexToColor } from '@shared/functions/hex-to-color';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { combineLatest, map, Observable, of } from 'rxjs';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import { AddVehicleDialogComponent } from '../add-vehicle-dialog/add-vehicle-dialog.component';
import { ObserverListDialogComponent } from '../observer-list-dialog/observer-list-dialog.component';
import { TempSubscriptionCacheService } from '../../services/temp-subscription-cache.service';
import { customValidatitors } from '@shared/helper-assets/validation';

@Component({
  selector: 'app-renew-subscription',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    LanguagePipe,
    RouterLink,
    MatIcon,
    MatSelect,
    MatOption,
    ValidationComponent,
    MatMenuModule,
    NgStyle,
    MatRadioGroup,
    MatRadioButton,
  ],
  templateUrl: './renew-subscription.component.html',
})
export class RenewSubscriptionComponent {
  fb = inject(FormBuilder);
  renewSubscriptionRequestService = inject(RenewSubscriptionRequestService);
  trackAccountSubscriptionService = inject(TrackAccountSubscriptionService);
  discountService = inject(DiscountService);
  subscriptionPlanService = inject(SubscriptionPlanService);
  smsBundleService = inject(SmsBundleService);
  vehicleService = inject(VehicleService);
  observationService = inject(ObservationService);
  router = inject(Router);
  dialog = inject(MatDialog);
  tempSubscriptionCacheService = inject(TempSubscriptionCacheService);
  activatedRoute = inject(ActivatedRoute);

  withSms = signal(false);
  discounts$ = signal<Map<number, SubscriptionDurationDiscountDto>>(new Map());
  totalCapacity = 20;
  plans = signal<SubscriptionPlanDto[]>([]);
  smsBundles = signal<SmsBundleDto[]>([]);
  subscription = signal<TrackAccountSubscriptionDto | null>(null);
  vehicles = signal<VehicleDto[]>([]);
  observers = signal<UserTrackAccountAssociationDto[]>([]);

  id = input<string>();

  form = this.fb.group({
    subscriptionPlan: this.fb.control(undefined, Validators.required),
    userCount: this.fb.control(null, []),
    smsBundleId: this.fb.control(null, Validators.required),
    subscriptionDurationInMonths: this.fb.control(1, [Validators.required]),
    newVehicles: this.fb.array<SubscriptionVehicleInfoCreateDto & { id?: string }>(
      [],
      Validators.minLength(1)
    ),
    removeVehicles: this.fb.control<string[]>([]),
    removeUsers: this.fb.control<string[]>([]),
    promoCode: this.fb.control<string>(null),
  });

  ngOnInit(): void {
    this.trackAccountSubscriptionService.getCurrentSubscription().subscribe(value => {
      this.subscription.set(value);

      if (this.tempSubscriptionCacheService.take.pop()) {
        this.form.patchValue(this.tempSubscriptionCacheService.getValues());
      } else {
        const val = value;
        this.withSms.set(val.smsBundle.id != null);
        this.form.patchValue({
          userCount: val.userCount,
          smsBundleId: val.smsBundle.id,
          subscriptionDurationInMonths: val.subscriptionDurationInMonth,
          subscriptionPlan: val.subscriptionPlanKey,
        });
      }
    });
    this.getDiscounts();
    this.getsmsBundle();
    this.getPlans();
    this.getVehicles();
    this.getObservation();
    this.form.controls.smsBundleId.addAsyncValidators(
      customValidatitors.smsBundleValidation(this.withSms)
    );
  }

  getVehicles() {
    this.vehicleService
      .getListVehicleViewModelWithAuditing()
      .pipe(
        map(response => {
          response.map(v => {
            const r = this.fb.group({
              id: v.id,
              licensePlateSerial: v.licensePlateSerial,
              licensePlateSubClass: v.licensePlateSubClass,
              consumptionRate: v.consumptionRate,
              needsTrackingDevice: true,
              color: hexToColor(v.colorHex),
            });
            this.form.controls.newVehicles.push(r as any);
          });
        })
      )
      .subscribe();
  }
  getDiscounts() {
    this.discountService
      .getSubscriptionDurationDiscounts()
      .pipe(
        map(discount => {
          const map = new Map();
          discount.map(d => {
            map.set(d.months, d);
          });
          this.discounts$.set(map);
        })
      )
      .subscribe();
  }
  getsmsBundle() {
    this.smsBundleService
      .getList({ maxResultCount: 999, skipCount: 0 })
      .pipe(
        map(sms => {
          this.smsBundles.set(sms.items);
        })
      )
      .subscribe();
  }
  getPlans() {
    this.subscriptionPlanService
      .getList()
      .pipe(
        map(plan => {
          this.plans.set(plan);
        })
      )
      .subscribe();
  }
  getObservation() {
    this.observationService
      .getList({ maxResultCount: 999, skipCount: 0 })
      .pipe(
        map(observe => {
          this.observers.set(observe.items);
        })
      )
      .subscribe();
  }

  onSubmit() {
    if (this.form.valid) {
      const values = this.form.value as CreateRenewTrackAccountSubscriptionsDto;
      this.renewSubscriptionRequestService
        .createTempBill({
          ...values,
        })
        .subscribe(val => {
          openPriceOfferDialog(this.dialog, { ...val, pay: false });
        });
    }
  }

  addVehiclesDialog(index: number | null = null) {
    let data: any = null;
    if (index != null) {
      data = this.form.controls.newVehicles.at(index);
    }
    const ref = this.dialog.open(AddVehicleDialogComponent, { data: data });
    ref.afterClosed().subscribe(res => {
      if (res) {
        if (index != null) {
          this.remove(index);
        }
        this.form.controls.newVehicles.push(res);
      }
    });
  }
  remove(index) {
    const id = this.form.controls.newVehicles.at(index).value.id;
    if (id) {
      this.form.controls.removeVehicles.setValue([...this.form.controls.removeVehicles.value, id]);
    }
    this.form.controls.newVehicles.removeAt(index);
  }
  // toDo
  // ngOnDestroy(): void {
  //   localStorage.removeItem('tempTrackAccountId');
  // }

  openObserversList() {
    const dialogRef = this.dialog.open(ObserverListDialogComponent, {
      data: this.observers(),
    });

    dialogRef.afterClosed().subscribe((result: string[] | undefined) => {
      if (result) {
        this.form.controls.removeUsers.setValue(result);
        // Handle the result if needed
      }
    });
  }

  goToPlans() {
    this.router.navigate(['/plans'], { queryParams: { select: 'true' } });
    this.tempSubscriptionCacheService.setValues(this.form.value);
  }
}
