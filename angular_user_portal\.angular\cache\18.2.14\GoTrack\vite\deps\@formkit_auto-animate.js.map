{"version": 3, "sources": ["../../../../../../node_modules/@formkit/auto-animate/index.mjs"], "sourcesContent": ["/**\n * A set of all the parents currently being observe. This is the only non weak\n * registry.\n */\nconst parents = new Set();\n/**\n * Element coordinates that is constantly kept up to date.\n */\nconst coords = new WeakMap();\n/**\n * Siblings of elements that have been removed from the dom.\n */\nconst siblings = new WeakMap();\n/**\n * Animations that are currently running.\n */\nconst animations = new WeakMap();\n/**\n * A map of existing intersection observers used to track element movements.\n */\nconst intersections = new WeakMap();\n/**\n * Intervals for automatically checking the position of elements occasionally.\n */\nconst intervals = new WeakMap();\n/**\n * The configuration options for each group of elements.\n */\nconst options = new WeakMap();\n/**\n * Debounce counters by id, used to debounce calls to update positions.\n */\nconst debounces = new WeakMap();\n/**\n * All parents that are currently enabled are tracked here.\n */\nconst enabled = new WeakSet();\n/**\n * The document used to calculate transitions.\n */\nlet root;\n/**\n * The root’s XY scroll positions.\n */\nlet scrollX = 0;\nlet scrollY = 0;\n/**\n * Used to sign an element as the target.\n */\nconst TGT = \"__aa_tgt\";\n/**\n * Used to sign an element as being part of a removal.\n */\nconst DEL = \"__aa_del\";\n/**\n * Used to sign an element as being \"new\". When an element is removed from the\n * dom, but may cycle back in we can sign it with new to ensure the next time\n * it is recognized we consider it new.\n */\nconst NEW = \"__aa_new\";\n/**\n * Callback for handling all mutations.\n * @param mutations - A mutation list\n */\nconst handleMutations = mutations => {\n  const elements = getElements(mutations);\n  // If elements is \"false\" that means this mutation that should be ignored.\n  if (elements) {\n    elements.forEach(el => animate(el));\n  }\n};\n/**\n *\n * @param entries - Elements that have been resized.\n */\nconst handleResizes = entries => {\n  entries.forEach(entry => {\n    if (entry.target === root) updateAllPos();\n    if (coords.has(entry.target)) updatePos(entry.target);\n  });\n};\n/**\n * Observe this elements position.\n * @param el - The element to observe the position of.\n */\nfunction observePosition(el) {\n  const oldObserver = intersections.get(el);\n  oldObserver === null || oldObserver === void 0 ? void 0 : oldObserver.disconnect();\n  let rect = coords.get(el);\n  let invocations = 0;\n  const buffer = 5;\n  if (!rect) {\n    rect = getCoords(el);\n    coords.set(el, rect);\n  }\n  const {\n    offsetWidth,\n    offsetHeight\n  } = root;\n  const rootMargins = [rect.top - buffer, offsetWidth - (rect.left + buffer + rect.width), offsetHeight - (rect.top + buffer + rect.height), rect.left - buffer];\n  const rootMargin = rootMargins.map(px => `${-1 * Math.floor(px)}px`).join(\" \");\n  const observer = new IntersectionObserver(() => {\n    ++invocations > 1 && updatePos(el);\n  }, {\n    root,\n    threshold: 1,\n    rootMargin\n  });\n  observer.observe(el);\n  intersections.set(el, observer);\n}\n/**\n * Update the exact position of a given element.\n * @param el - An element to update the position of.\n */\nfunction updatePos(el) {\n  clearTimeout(debounces.get(el));\n  const optionsOrPlugin = getOptions(el);\n  const delay = isPlugin(optionsOrPlugin) ? 500 : optionsOrPlugin.duration;\n  debounces.set(el, setTimeout(async () => {\n    const currentAnimation = animations.get(el);\n    try {\n      await (currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.finished);\n      coords.set(el, getCoords(el));\n      observePosition(el);\n    } catch {\n      // ignore errors as the `.finished` promise is rejected when animations were cancelled\n    }\n  }, delay));\n}\n/**\n * Updates all positions that are currently being tracked.\n */\nfunction updateAllPos() {\n  clearTimeout(debounces.get(root));\n  debounces.set(root, setTimeout(() => {\n    parents.forEach(parent => forEach(parent, el => lowPriority(() => updatePos(el))));\n  }, 100));\n}\n/**\n * Its possible for a quick scroll or other fast events to get past the\n * intersection observer, so occasionally we need want \"cold-poll\" for the\n * latests and greatest position. We try to do this in the most non-disruptive\n * fashion possible. First we only do this ever couple seconds, staggard by a\n * random offset.\n * @param el - Element\n */\nfunction poll(el) {\n  setTimeout(() => {\n    intervals.set(el, setInterval(() => lowPriority(updatePos.bind(null, el)), 2000));\n  }, Math.round(2000 * Math.random()));\n}\n/**\n * Perform some operation that is non critical at some point.\n * @param callback\n */\nfunction lowPriority(callback) {\n  if (typeof requestIdleCallback === \"function\") {\n    requestIdleCallback(() => callback());\n  } else {\n    requestAnimationFrame(() => callback());\n  }\n}\n/**\n * The mutation observer responsible for watching each root element.\n */\nlet mutations;\n/**\n * A resize observer, responsible for recalculating elements on resize.\n */\nlet resize;\n/**\n * Ensure the browser is supported.\n */\nconst supportedBrowser = typeof window !== \"undefined\" && \"ResizeObserver\" in window;\n/**\n * If this is in a browser, initialize our Web APIs\n */\nif (supportedBrowser) {\n  root = document.documentElement;\n  mutations = new MutationObserver(handleMutations);\n  resize = new ResizeObserver(handleResizes);\n  window.addEventListener(\"scroll\", () => {\n    scrollY = window.scrollY;\n    scrollX = window.scrollX;\n  });\n  resize.observe(root);\n}\n/**\n * Retrieves all the elements that may have been affected by the last mutation\n * including ones that have been removed and are no longer in the DOM.\n * @param mutations - A mutation list.\n * @returns\n */\nfunction getElements(mutations) {\n  const observedNodes = mutations.reduce((nodes, mutation) => {\n    return [...nodes, ...Array.from(mutation.addedNodes), ...Array.from(mutation.removedNodes)];\n  }, []);\n  // Short circuit if _only_ comment nodes are observed\n  const onlyCommentNodesObserved = observedNodes.every(node => node.nodeName === \"#comment\");\n  if (onlyCommentNodesObserved) return false;\n  return mutations.reduce((elements, mutation) => {\n    // Short circuit if we find a purposefully deleted node.\n    if (elements === false) return false;\n    if (mutation.target instanceof Element) {\n      target(mutation.target);\n      if (!elements.has(mutation.target)) {\n        elements.add(mutation.target);\n        for (let i = 0; i < mutation.target.children.length; i++) {\n          const child = mutation.target.children.item(i);\n          if (!child) continue;\n          if (DEL in child) {\n            return false;\n          }\n          target(mutation.target, child);\n          elements.add(child);\n        }\n      }\n      if (mutation.removedNodes.length) {\n        for (let i = 0; i < mutation.removedNodes.length; i++) {\n          const child = mutation.removedNodes[i];\n          if (DEL in child) {\n            return false;\n          }\n          if (child instanceof Element) {\n            elements.add(child);\n            target(mutation.target, child);\n            siblings.set(child, [mutation.previousSibling, mutation.nextSibling]);\n          }\n        }\n      }\n    }\n    return elements;\n  }, new Set());\n}\n/**\n * Assign the target to an element.\n * @param el - The root element\n * @param child\n */\nfunction target(el, child) {\n  if (!child && !(TGT in el)) Object.defineProperty(el, TGT, {\n    value: el\n  });else if (child && !(TGT in child)) Object.defineProperty(child, TGT, {\n    value: el\n  });\n}\n/**\n * Determines what kind of change took place on the given element and then\n * performs the proper animation based on that.\n * @param el - The specific element to animate.\n */\nfunction animate(el) {\n  var _a;\n  const isMounted = el.isConnected;\n  const preExisting = coords.has(el);\n  if (isMounted && siblings.has(el)) siblings.delete(el);\n  if (animations.has(el)) {\n    (_a = animations.get(el)) === null || _a === void 0 ? void 0 : _a.cancel();\n  }\n  if (NEW in el) {\n    add(el);\n  } else if (preExisting && isMounted) {\n    remain(el);\n  } else if (preExisting && !isMounted) {\n    remove(el);\n  } else {\n    add(el);\n  }\n}\n/**\n * Removes all non-digits from a string and casts to a number.\n * @param str - A string containing a pixel value.\n * @returns\n */\nfunction raw(str) {\n  return Number(str.replace(/[^0-9.\\-]/g, \"\"));\n}\n/**\n * Get the scroll offset of elements\n * @param el - Element\n * @returns\n */\nfunction getScrollOffset(el) {\n  let p = el.parentElement;\n  while (p) {\n    if (p.scrollLeft || p.scrollTop) {\n      return {\n        x: p.scrollLeft,\n        y: p.scrollTop\n      };\n    }\n    p = p.parentElement;\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n}\n/**\n * Get the coordinates of elements adjusted for scroll position.\n * @param el - Element\n * @returns\n */\nfunction getCoords(el) {\n  const rect = el.getBoundingClientRect();\n  const {\n    x,\n    y\n  } = getScrollOffset(el);\n  return {\n    top: rect.top + y,\n    left: rect.left + x,\n    width: rect.width,\n    height: rect.height\n  };\n}\n/**\n * Returns the width/height that the element should be transitioned between.\n * This takes into account box-sizing.\n * @param el - Element being animated\n * @param oldCoords - Old set of Coordinates coordinates\n * @param newCoords - New set of Coordinates coordinates\n * @returns\n */\nfunction getTransitionSizes(el, oldCoords, newCoords) {\n  let widthFrom = oldCoords.width;\n  let heightFrom = oldCoords.height;\n  let widthTo = newCoords.width;\n  let heightTo = newCoords.height;\n  const styles = getComputedStyle(el);\n  const sizing = styles.getPropertyValue(\"box-sizing\");\n  if (sizing === \"content-box\") {\n    const paddingY = raw(styles.paddingTop) + raw(styles.paddingBottom) + raw(styles.borderTopWidth) + raw(styles.borderBottomWidth);\n    const paddingX = raw(styles.paddingLeft) + raw(styles.paddingRight) + raw(styles.borderRightWidth) + raw(styles.borderLeftWidth);\n    widthFrom -= paddingX;\n    widthTo -= paddingX;\n    heightFrom -= paddingY;\n    heightTo -= paddingY;\n  }\n  return [widthFrom, widthTo, heightFrom, heightTo].map(Math.round);\n}\n/**\n * Retrieves animation options for the current element.\n * @param el - Element to retrieve options for.\n * @returns\n */\nfunction getOptions(el) {\n  return TGT in el && options.has(el[TGT]) ? options.get(el[TGT]) : {\n    duration: 250,\n    easing: \"ease-in-out\"\n  };\n}\n/**\n * Returns the target of a given animation (generally the parent).\n * @param el - An element to check for a target\n * @returns\n */\nfunction getTarget(el) {\n  if (TGT in el) return el[TGT];\n  return undefined;\n}\n/**\n * Checks if animations are enabled or disabled for a given element.\n * @param el - Any element\n * @returns\n */\nfunction isEnabled(el) {\n  const target = getTarget(el);\n  return target ? enabled.has(target) : false;\n}\n/**\n * Iterate over the children of a given parent.\n * @param parent - A parent element\n * @param callback - A callback\n */\nfunction forEach(parent, ...callbacks) {\n  callbacks.forEach(callback => callback(parent, options.has(parent)));\n  for (let i = 0; i < parent.children.length; i++) {\n    const child = parent.children.item(i);\n    if (child) {\n      callbacks.forEach(callback => callback(child, options.has(child)));\n    }\n  }\n}\n/**\n * Always return tuple to provide consistent interface\n */\nfunction getPluginTuple(pluginReturn) {\n  if (Array.isArray(pluginReturn)) return pluginReturn;\n  return [pluginReturn];\n}\n/**\n * Determine if config is plugin\n */\nfunction isPlugin(config) {\n  return typeof config === \"function\";\n}\n/**\n * The element in question is remaining in the DOM.\n * @param el - Element to flip\n * @returns\n */\nfunction remain(el) {\n  const oldCoords = coords.get(el);\n  const newCoords = getCoords(el);\n  if (!isEnabled(el)) return coords.set(el, newCoords);\n  let animation;\n  if (!oldCoords) return;\n  const pluginOrOptions = getOptions(el);\n  if (typeof pluginOrOptions !== \"function\") {\n    const deltaX = oldCoords.left - newCoords.left;\n    const deltaY = oldCoords.top - newCoords.top;\n    const [widthFrom, widthTo, heightFrom, heightTo] = getTransitionSizes(el, oldCoords, newCoords);\n    const start = {\n      transform: `translate(${deltaX}px, ${deltaY}px)`\n    };\n    const end = {\n      transform: `translate(0, 0)`\n    };\n    if (widthFrom !== widthTo) {\n      start.width = `${widthFrom}px`;\n      end.width = `${widthTo}px`;\n    }\n    if (heightFrom !== heightTo) {\n      start.height = `${heightFrom}px`;\n      end.height = `${heightTo}px`;\n    }\n    animation = el.animate([start, end], {\n      duration: pluginOrOptions.duration,\n      easing: pluginOrOptions.easing\n    });\n  } else {\n    const [keyframes] = getPluginTuple(pluginOrOptions(el, \"remain\", oldCoords, newCoords));\n    animation = new Animation(keyframes);\n    animation.play();\n  }\n  animations.set(el, animation);\n  coords.set(el, newCoords);\n  animation.addEventListener(\"finish\", updatePos.bind(null, el));\n}\n/**\n * Adds the element with a transition.\n * @param el - Animates the element being added.\n */\nfunction add(el) {\n  if (NEW in el) delete el[NEW];\n  const newCoords = getCoords(el);\n  coords.set(el, newCoords);\n  const pluginOrOptions = getOptions(el);\n  if (!isEnabled(el)) return;\n  let animation;\n  if (typeof pluginOrOptions !== \"function\") {\n    animation = el.animate([{\n      transform: \"scale(.98)\",\n      opacity: 0\n    }, {\n      transform: \"scale(0.98)\",\n      opacity: 0,\n      offset: 0.5\n    }, {\n      transform: \"scale(1)\",\n      opacity: 1\n    }], {\n      duration: pluginOrOptions.duration * 1.5,\n      easing: \"ease-in\"\n    });\n  } else {\n    const [keyframes] = getPluginTuple(pluginOrOptions(el, \"add\", newCoords));\n    animation = new Animation(keyframes);\n    animation.play();\n  }\n  animations.set(el, animation);\n  animation.addEventListener(\"finish\", updatePos.bind(null, el));\n}\n/**\n * Clean up after removing an element from the dom.\n * @param el - Element being removed\n * @param styles - Optional styles that should be removed from the element.\n */\nfunction cleanUp(el, styles) {\n  var _a;\n  el.remove();\n  coords.delete(el);\n  siblings.delete(el);\n  animations.delete(el);\n  (_a = intersections.get(el)) === null || _a === void 0 ? void 0 : _a.disconnect();\n  setTimeout(() => {\n    if (DEL in el) delete el[DEL];\n    Object.defineProperty(el, NEW, {\n      value: true,\n      configurable: true\n    });\n    if (styles && el instanceof HTMLElement) {\n      for (const style in styles) {\n        el.style[style] = \"\";\n      }\n    }\n  }, 0);\n}\n/**\n * Animates the removal of an element.\n * @param el - Element to remove\n */\nfunction remove(el) {\n  var _a;\n  if (!siblings.has(el) || !coords.has(el)) return;\n  const [prev, next] = siblings.get(el);\n  Object.defineProperty(el, DEL, {\n    value: true,\n    configurable: true\n  });\n  const finalX = window.scrollX;\n  const finalY = window.scrollY;\n  if (next && next.parentNode && next.parentNode instanceof Element) {\n    next.parentNode.insertBefore(el, next);\n  } else if (prev && prev.parentNode) {\n    prev.parentNode.appendChild(el);\n  } else {\n    (_a = getTarget(el)) === null || _a === void 0 ? void 0 : _a.appendChild(el);\n  }\n  if (!isEnabled(el)) return cleanUp(el);\n  const [top, left, width, height] = deletePosition(el);\n  const optionsOrPlugin = getOptions(el);\n  const oldCoords = coords.get(el);\n  if (finalX !== scrollX || finalY !== scrollY) {\n    adjustScroll(el, finalX, finalY, optionsOrPlugin);\n  }\n  let animation;\n  let styleReset = {\n    position: \"absolute\",\n    top: `${top}px`,\n    left: `${left}px`,\n    width: `${width}px`,\n    height: `${height}px`,\n    margin: \"0\",\n    pointerEvents: \"none\",\n    transformOrigin: \"center\",\n    zIndex: \"100\"\n  };\n  if (!isPlugin(optionsOrPlugin)) {\n    Object.assign(el.style, styleReset);\n    animation = el.animate([{\n      transform: \"scale(1)\",\n      opacity: 1\n    }, {\n      transform: \"scale(.98)\",\n      opacity: 0\n    }], {\n      duration: optionsOrPlugin.duration,\n      easing: \"ease-out\"\n    });\n  } else {\n    const [keyframes, options] = getPluginTuple(optionsOrPlugin(el, \"remove\", oldCoords));\n    if ((options === null || options === void 0 ? void 0 : options.styleReset) !== false) {\n      styleReset = (options === null || options === void 0 ? void 0 : options.styleReset) || styleReset;\n      Object.assign(el.style, styleReset);\n    }\n    animation = new Animation(keyframes);\n    animation.play();\n  }\n  animations.set(el, animation);\n  animation.addEventListener(\"finish\", cleanUp.bind(null, el, styleReset));\n}\n/**\n * If the element being removed is at the very bottom of the page, and the\n * the page was scrolled into a space being \"made available\" by the element\n * that was removed, the page scroll will have jumped up some amount. We need\n * to offset the jump by the amount that the page was \"automatically\" scrolled\n * up. We can do this by comparing the scroll position before and after the\n * element was removed, and then offsetting by that amount.\n *\n * @param el - The element being deleted\n * @param finalX - The final X scroll position\n * @param finalY - The final Y scroll position\n * @param optionsOrPlugin - The options or plugin\n * @returns\n */\nfunction adjustScroll(el, finalX, finalY, optionsOrPlugin) {\n  const scrollDeltaX = scrollX - finalX;\n  const scrollDeltaY = scrollY - finalY;\n  const scrollBefore = document.documentElement.style.scrollBehavior;\n  const scrollBehavior = getComputedStyle(root).scrollBehavior;\n  if (scrollBehavior === \"smooth\") {\n    document.documentElement.style.scrollBehavior = \"auto\";\n  }\n  window.scrollTo(window.scrollX + scrollDeltaX, window.scrollY + scrollDeltaY);\n  if (!el.parentElement) return;\n  const parent = el.parentElement;\n  let lastHeight = parent.clientHeight;\n  let lastWidth = parent.clientWidth;\n  const startScroll = performance.now();\n  // Here we use a manual scroll animation to keep the element using the same\n  // easing and timing as the parent’s scroll animation.\n  function smoothScroll() {\n    requestAnimationFrame(() => {\n      if (!isPlugin(optionsOrPlugin)) {\n        const deltaY = lastHeight - parent.clientHeight;\n        const deltaX = lastWidth - parent.clientWidth;\n        if (startScroll + optionsOrPlugin.duration > performance.now()) {\n          window.scrollTo({\n            left: window.scrollX - deltaX,\n            top: window.scrollY - deltaY\n          });\n          lastHeight = parent.clientHeight;\n          lastWidth = parent.clientWidth;\n          smoothScroll();\n        } else {\n          document.documentElement.style.scrollBehavior = scrollBefore;\n        }\n      }\n    });\n  }\n  smoothScroll();\n}\n/**\n * Determines the position of the element being removed.\n * @param el - The element being deleted\n * @returns\n */\nfunction deletePosition(el) {\n  const oldCoords = coords.get(el);\n  const [width,, height] = getTransitionSizes(el, oldCoords, getCoords(el));\n  let offsetParent = el.parentElement;\n  while (offsetParent && (getComputedStyle(offsetParent).position === \"static\" || offsetParent instanceof HTMLBodyElement)) {\n    offsetParent = offsetParent.parentElement;\n  }\n  if (!offsetParent) offsetParent = document.body;\n  const parentStyles = getComputedStyle(offsetParent);\n  const parentCoords = coords.get(offsetParent) || getCoords(offsetParent);\n  const top = Math.round(oldCoords.top - parentCoords.top) - raw(parentStyles.borderTopWidth);\n  const left = Math.round(oldCoords.left - parentCoords.left) - raw(parentStyles.borderLeftWidth);\n  return [top, left, width, height];\n}\n/**\n * A function that automatically adds animation effects to itself and its\n * immediate children. Specifically it adds effects for adding, moving, and\n * removing DOM elements.\n * @param el - A parent element to add animations to.\n * @param options - An optional object of options.\n */\nfunction autoAnimate(el, config = {}) {\n  if (mutations && resize) {\n    const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n    const isDisabledDueToReduceMotion = mediaQuery.matches && !isPlugin(config) && !config.disrespectUserMotionPreference;\n    if (!isDisabledDueToReduceMotion) {\n      enabled.add(el);\n      if (getComputedStyle(el).position === \"static\") {\n        Object.assign(el.style, {\n          position: \"relative\"\n        });\n      }\n      forEach(el, updatePos, poll, element => resize === null || resize === void 0 ? void 0 : resize.observe(element));\n      if (isPlugin(config)) {\n        options.set(el, config);\n      } else {\n        options.set(el, {\n          duration: 250,\n          easing: \"ease-in-out\",\n          ...config\n        });\n      }\n      mutations.observe(el, {\n        childList: true\n      });\n      parents.add(el);\n    }\n  }\n  return Object.freeze({\n    parent: el,\n    enable: () => {\n      enabled.add(el);\n    },\n    disable: () => {\n      enabled.delete(el);\n    },\n    isEnabled: () => enabled.has(el)\n  });\n}\n/**\n * The vue directive.\n */\nconst vAutoAnimate = {\n  mounted: (el, binding) => {\n    autoAnimate(el, binding.value || {});\n  },\n  // ignore ssr see #96:\n  getSSRProps: () => ({})\n};\nexport { autoAnimate as default, getTransitionSizes, vAutoAnimate };"], "mappings": ";;;;;;AAIA,IAAM,UAAU,oBAAI,IAAI;AAIxB,IAAM,SAAS,oBAAI,QAAQ;AAI3B,IAAM,WAAW,oBAAI,QAAQ;AAI7B,IAAM,aAAa,oBAAI,QAAQ;AAI/B,IAAM,gBAAgB,oBAAI,QAAQ;AAIlC,IAAM,YAAY,oBAAI,QAAQ;AAI9B,IAAM,UAAU,oBAAI,QAAQ;AAI5B,IAAM,YAAY,oBAAI,QAAQ;AAI9B,IAAM,UAAU,oBAAI,QAAQ;AAI5B,IAAI;AAIJ,IAAI,UAAU;AACd,IAAI,UAAU;AAId,IAAM,MAAM;AAIZ,IAAM,MAAM;AAMZ,IAAM,MAAM;AAKZ,IAAM,kBAAkB,CAAAA,eAAa;AACnC,QAAM,WAAW,YAAYA,UAAS;AAEtC,MAAI,UAAU;AACZ,aAAS,QAAQ,QAAM,QAAQ,EAAE,CAAC;AAAA,EACpC;AACF;AAKA,IAAM,gBAAgB,aAAW;AAC/B,UAAQ,QAAQ,WAAS;AACvB,QAAI,MAAM,WAAW,KAAM,cAAa;AACxC,QAAI,OAAO,IAAI,MAAM,MAAM,EAAG,WAAU,MAAM,MAAM;AAAA,EACtD,CAAC;AACH;AAKA,SAAS,gBAAgB,IAAI;AAC3B,QAAM,cAAc,cAAc,IAAI,EAAE;AACxC,kBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,WAAW;AACjF,MAAI,OAAO,OAAO,IAAI,EAAE;AACxB,MAAI,cAAc;AAClB,QAAM,SAAS;AACf,MAAI,CAAC,MAAM;AACT,WAAO,UAAU,EAAE;AACnB,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,cAAc,CAAC,KAAK,MAAM,QAAQ,eAAe,KAAK,OAAO,SAAS,KAAK,QAAQ,gBAAgB,KAAK,MAAM,SAAS,KAAK,SAAS,KAAK,OAAO,MAAM;AAC7J,QAAM,aAAa,YAAY,IAAI,QAAM,GAAG,KAAK,KAAK,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7E,QAAM,WAAW,IAAI,qBAAqB,MAAM;AAC9C,MAAE,cAAc,KAAK,UAAU,EAAE;AAAA,EACnC,GAAG;AAAA,IACD;AAAA,IACA,WAAW;AAAA,IACX;AAAA,EACF,CAAC;AACD,WAAS,QAAQ,EAAE;AACnB,gBAAc,IAAI,IAAI,QAAQ;AAChC;AAKA,SAAS,UAAU,IAAI;AACrB,eAAa,UAAU,IAAI,EAAE,CAAC;AAC9B,QAAM,kBAAkB,WAAW,EAAE;AACrC,QAAM,QAAQ,SAAS,eAAe,IAAI,MAAM,gBAAgB;AAChE,YAAU,IAAI,IAAI,WAAW,MAAY;AACvC,UAAM,mBAAmB,WAAW,IAAI,EAAE;AAC1C,QAAI;AACF,YAAO,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AAC5F,aAAO,IAAI,IAAI,UAAU,EAAE,CAAC;AAC5B,sBAAgB,EAAE;AAAA,IACpB,QAAQ;AAAA,IAER;AAAA,EACF,IAAG,KAAK,CAAC;AACX;AAIA,SAAS,eAAe;AACtB,eAAa,UAAU,IAAI,IAAI,CAAC;AAChC,YAAU,IAAI,MAAM,WAAW,MAAM;AACnC,YAAQ,QAAQ,YAAU,QAAQ,QAAQ,QAAM,YAAY,MAAM,UAAU,EAAE,CAAC,CAAC,CAAC;AAAA,EACnF,GAAG,GAAG,CAAC;AACT;AASA,SAAS,KAAK,IAAI;AAChB,aAAW,MAAM;AACf,cAAU,IAAI,IAAI,YAAY,MAAM,YAAY,UAAU,KAAK,MAAM,EAAE,CAAC,GAAG,GAAI,CAAC;AAAA,EAClF,GAAG,KAAK,MAAM,MAAO,KAAK,OAAO,CAAC,CAAC;AACrC;AAKA,SAAS,YAAY,UAAU;AAC7B,MAAI,OAAO,wBAAwB,YAAY;AAC7C,wBAAoB,MAAM,SAAS,CAAC;AAAA,EACtC,OAAO;AACL,0BAAsB,MAAM,SAAS,CAAC;AAAA,EACxC;AACF;AAIA,IAAI;AAIJ,IAAI;AAIJ,IAAM,mBAAmB,OAAO,WAAW,eAAe,oBAAoB;AAI9E,IAAI,kBAAkB;AACpB,SAAO,SAAS;AAChB,cAAY,IAAI,iBAAiB,eAAe;AAChD,WAAS,IAAI,eAAe,aAAa;AACzC,SAAO,iBAAiB,UAAU,MAAM;AACtC,cAAU,OAAO;AACjB,cAAU,OAAO;AAAA,EACnB,CAAC;AACD,SAAO,QAAQ,IAAI;AACrB;AAOA,SAAS,YAAYA,YAAW;AAC9B,QAAM,gBAAgBA,WAAU,OAAO,CAAC,OAAO,aAAa;AAC1D,WAAO,CAAC,GAAG,OAAO,GAAG,MAAM,KAAK,SAAS,UAAU,GAAG,GAAG,MAAM,KAAK,SAAS,YAAY,CAAC;AAAA,EAC5F,GAAG,CAAC,CAAC;AAEL,QAAM,2BAA2B,cAAc,MAAM,UAAQ,KAAK,aAAa,UAAU;AACzF,MAAI,yBAA0B,QAAO;AACrC,SAAOA,WAAU,OAAO,CAAC,UAAU,aAAa;AAE9C,QAAI,aAAa,MAAO,QAAO;AAC/B,QAAI,SAAS,kBAAkB,SAAS;AACtC,aAAO,SAAS,MAAM;AACtB,UAAI,CAAC,SAAS,IAAI,SAAS,MAAM,GAAG;AAClC,iBAAS,IAAI,SAAS,MAAM;AAC5B,iBAAS,IAAI,GAAG,IAAI,SAAS,OAAO,SAAS,QAAQ,KAAK;AACxD,gBAAM,QAAQ,SAAS,OAAO,SAAS,KAAK,CAAC;AAC7C,cAAI,CAAC,MAAO;AACZ,cAAI,OAAO,OAAO;AAChB,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS,QAAQ,KAAK;AAC7B,mBAAS,IAAI,KAAK;AAAA,QACpB;AAAA,MACF;AACA,UAAI,SAAS,aAAa,QAAQ;AAChC,iBAAS,IAAI,GAAG,IAAI,SAAS,aAAa,QAAQ,KAAK;AACrD,gBAAM,QAAQ,SAAS,aAAa,CAAC;AACrC,cAAI,OAAO,OAAO;AAChB,mBAAO;AAAA,UACT;AACA,cAAI,iBAAiB,SAAS;AAC5B,qBAAS,IAAI,KAAK;AAClB,mBAAO,SAAS,QAAQ,KAAK;AAC7B,qBAAS,IAAI,OAAO,CAAC,SAAS,iBAAiB,SAAS,WAAW,CAAC;AAAA,UACtE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,oBAAI,IAAI,CAAC;AACd;AAMA,SAAS,OAAO,IAAI,OAAO;AACzB,MAAI,CAAC,SAAS,EAAE,OAAO,IAAK,QAAO,eAAe,IAAI,KAAK;AAAA,IACzD,OAAO;AAAA,EACT,CAAC;AAAA,WAAW,SAAS,EAAE,OAAO,OAAQ,QAAO,eAAe,OAAO,KAAK;AAAA,IACtE,OAAO;AAAA,EACT,CAAC;AACH;AAMA,SAAS,QAAQ,IAAI;AACnB,MAAI;AACJ,QAAM,YAAY,GAAG;AACrB,QAAM,cAAc,OAAO,IAAI,EAAE;AACjC,MAAI,aAAa,SAAS,IAAI,EAAE,EAAG,UAAS,OAAO,EAAE;AACrD,MAAI,WAAW,IAAI,EAAE,GAAG;AACtB,KAAC,KAAK,WAAW,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,EAC3E;AACA,MAAI,OAAO,IAAI;AACb,QAAI,EAAE;AAAA,EACR,WAAW,eAAe,WAAW;AACnC,WAAO,EAAE;AAAA,EACX,WAAW,eAAe,CAAC,WAAW;AACpC,WAAO,EAAE;AAAA,EACX,OAAO;AACL,QAAI,EAAE;AAAA,EACR;AACF;AAMA,SAAS,IAAI,KAAK;AAChB,SAAO,OAAO,IAAI,QAAQ,cAAc,EAAE,CAAC;AAC7C;AAMA,SAAS,gBAAgB,IAAI;AAC3B,MAAI,IAAI,GAAG;AACX,SAAO,GAAG;AACR,QAAI,EAAE,cAAc,EAAE,WAAW;AAC/B,aAAO;AAAA,QACL,GAAG,EAAE;AAAA,QACL,GAAG,EAAE;AAAA,MACP;AAAA,IACF;AACA,QAAI,EAAE;AAAA,EACR;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAMA,SAAS,UAAU,IAAI;AACrB,QAAM,OAAO,GAAG,sBAAsB;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,EAAE;AACtB,SAAO;AAAA,IACL,KAAK,KAAK,MAAM;AAAA,IAChB,MAAM,KAAK,OAAO;AAAA,IAClB,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AASA,SAAS,mBAAmB,IAAI,WAAW,WAAW;AACpD,MAAI,YAAY,UAAU;AAC1B,MAAI,aAAa,UAAU;AAC3B,MAAI,UAAU,UAAU;AACxB,MAAI,WAAW,UAAU;AACzB,QAAM,SAAS,iBAAiB,EAAE;AAClC,QAAM,SAAS,OAAO,iBAAiB,YAAY;AACnD,MAAI,WAAW,eAAe;AAC5B,UAAM,WAAW,IAAI,OAAO,UAAU,IAAI,IAAI,OAAO,aAAa,IAAI,IAAI,OAAO,cAAc,IAAI,IAAI,OAAO,iBAAiB;AAC/H,UAAM,WAAW,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,YAAY,IAAI,IAAI,OAAO,gBAAgB,IAAI,IAAI,OAAO,eAAe;AAC/H,iBAAa;AACb,eAAW;AACX,kBAAc;AACd,gBAAY;AAAA,EACd;AACA,SAAO,CAAC,WAAW,SAAS,YAAY,QAAQ,EAAE,IAAI,KAAK,KAAK;AAClE;AAMA,SAAS,WAAW,IAAI;AACtB,SAAO,OAAO,MAAM,QAAQ,IAAI,GAAG,GAAG,CAAC,IAAI,QAAQ,IAAI,GAAG,GAAG,CAAC,IAAI;AAAA,IAChE,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AACF;AAMA,SAAS,UAAU,IAAI;AACrB,MAAI,OAAO,GAAI,QAAO,GAAG,GAAG;AAC5B,SAAO;AACT;AAMA,SAAS,UAAU,IAAI;AACrB,QAAMC,UAAS,UAAU,EAAE;AAC3B,SAAOA,UAAS,QAAQ,IAAIA,OAAM,IAAI;AACxC;AAMA,SAAS,QAAQ,WAAW,WAAW;AACrC,YAAU,QAAQ,cAAY,SAAS,QAAQ,QAAQ,IAAI,MAAM,CAAC,CAAC;AACnE,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAAK;AAC/C,UAAM,QAAQ,OAAO,SAAS,KAAK,CAAC;AACpC,QAAI,OAAO;AACT,gBAAU,QAAQ,cAAY,SAAS,OAAO,QAAQ,IAAI,KAAK,CAAC,CAAC;AAAA,IACnE;AAAA,EACF;AACF;AAIA,SAAS,eAAe,cAAc;AACpC,MAAI,MAAM,QAAQ,YAAY,EAAG,QAAO;AACxC,SAAO,CAAC,YAAY;AACtB;AAIA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,WAAW;AAC3B;AAMA,SAAS,OAAO,IAAI;AAClB,QAAM,YAAY,OAAO,IAAI,EAAE;AAC/B,QAAM,YAAY,UAAU,EAAE;AAC9B,MAAI,CAAC,UAAU,EAAE,EAAG,QAAO,OAAO,IAAI,IAAI,SAAS;AACnD,MAAI;AACJ,MAAI,CAAC,UAAW;AAChB,QAAM,kBAAkB,WAAW,EAAE;AACrC,MAAI,OAAO,oBAAoB,YAAY;AACzC,UAAM,SAAS,UAAU,OAAO,UAAU;AAC1C,UAAM,SAAS,UAAU,MAAM,UAAU;AACzC,UAAM,CAAC,WAAW,SAAS,YAAY,QAAQ,IAAI,mBAAmB,IAAI,WAAW,SAAS;AAC9F,UAAM,QAAQ;AAAA,MACZ,WAAW,aAAa,MAAM,OAAO,MAAM;AAAA,IAC7C;AACA,UAAM,MAAM;AAAA,MACV,WAAW;AAAA,IACb;AACA,QAAI,cAAc,SAAS;AACzB,YAAM,QAAQ,GAAG,SAAS;AAC1B,UAAI,QAAQ,GAAG,OAAO;AAAA,IACxB;AACA,QAAI,eAAe,UAAU;AAC3B,YAAM,SAAS,GAAG,UAAU;AAC5B,UAAI,SAAS,GAAG,QAAQ;AAAA,IAC1B;AACA,gBAAY,GAAG,QAAQ,CAAC,OAAO,GAAG,GAAG;AAAA,MACnC,UAAU,gBAAgB;AAAA,MAC1B,QAAQ,gBAAgB;AAAA,IAC1B,CAAC;AAAA,EACH,OAAO;AACL,UAAM,CAAC,SAAS,IAAI,eAAe,gBAAgB,IAAI,UAAU,WAAW,SAAS,CAAC;AACtF,gBAAY,IAAI,UAAU,SAAS;AACnC,cAAU,KAAK;AAAA,EACjB;AACA,aAAW,IAAI,IAAI,SAAS;AAC5B,SAAO,IAAI,IAAI,SAAS;AACxB,YAAU,iBAAiB,UAAU,UAAU,KAAK,MAAM,EAAE,CAAC;AAC/D;AAKA,SAAS,IAAI,IAAI;AACf,MAAI,OAAO,GAAI,QAAO,GAAG,GAAG;AAC5B,QAAM,YAAY,UAAU,EAAE;AAC9B,SAAO,IAAI,IAAI,SAAS;AACxB,QAAM,kBAAkB,WAAW,EAAE;AACrC,MAAI,CAAC,UAAU,EAAE,EAAG;AACpB,MAAI;AACJ,MAAI,OAAO,oBAAoB,YAAY;AACzC,gBAAY,GAAG,QAAQ,CAAC;AAAA,MACtB,WAAW;AAAA,MACX,SAAS;AAAA,IACX,GAAG;AAAA,MACD,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC,GAAG;AAAA,MACF,UAAU,gBAAgB,WAAW;AAAA,MACrC,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,OAAO;AACL,UAAM,CAAC,SAAS,IAAI,eAAe,gBAAgB,IAAI,OAAO,SAAS,CAAC;AACxE,gBAAY,IAAI,UAAU,SAAS;AACnC,cAAU,KAAK;AAAA,EACjB;AACA,aAAW,IAAI,IAAI,SAAS;AAC5B,YAAU,iBAAiB,UAAU,UAAU,KAAK,MAAM,EAAE,CAAC;AAC/D;AAMA,SAAS,QAAQ,IAAI,QAAQ;AAC3B,MAAI;AACJ,KAAG,OAAO;AACV,SAAO,OAAO,EAAE;AAChB,WAAS,OAAO,EAAE;AAClB,aAAW,OAAO,EAAE;AACpB,GAAC,KAAK,cAAc,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAChF,aAAW,MAAM;AACf,QAAI,OAAO,GAAI,QAAO,GAAG,GAAG;AAC5B,WAAO,eAAe,IAAI,KAAK;AAAA,MAC7B,OAAO;AAAA,MACP,cAAc;AAAA,IAChB,CAAC;AACD,QAAI,UAAU,cAAc,aAAa;AACvC,iBAAW,SAAS,QAAQ;AAC1B,WAAG,MAAM,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF,GAAG,CAAC;AACN;AAKA,SAAS,OAAO,IAAI;AAClB,MAAI;AACJ,MAAI,CAAC,SAAS,IAAI,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE,EAAG;AAC1C,QAAM,CAAC,MAAM,IAAI,IAAI,SAAS,IAAI,EAAE;AACpC,SAAO,eAAe,IAAI,KAAK;AAAA,IAC7B,OAAO;AAAA,IACP,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,SAAS,OAAO;AACtB,QAAM,SAAS,OAAO;AACtB,MAAI,QAAQ,KAAK,cAAc,KAAK,sBAAsB,SAAS;AACjE,SAAK,WAAW,aAAa,IAAI,IAAI;AAAA,EACvC,WAAW,QAAQ,KAAK,YAAY;AAClC,SAAK,WAAW,YAAY,EAAE;AAAA,EAChC,OAAO;AACL,KAAC,KAAK,UAAU,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,EAAE;AAAA,EAC7E;AACA,MAAI,CAAC,UAAU,EAAE,EAAG,QAAO,QAAQ,EAAE;AACrC,QAAM,CAAC,KAAK,MAAM,OAAO,MAAM,IAAI,eAAe,EAAE;AACpD,QAAM,kBAAkB,WAAW,EAAE;AACrC,QAAM,YAAY,OAAO,IAAI,EAAE;AAC/B,MAAI,WAAW,WAAW,WAAW,SAAS;AAC5C,iBAAa,IAAI,QAAQ,QAAQ,eAAe;AAAA,EAClD;AACA,MAAI;AACJ,MAAI,aAAa;AAAA,IACf,UAAU;AAAA,IACV,KAAK,GAAG,GAAG;AAAA,IACX,MAAM,GAAG,IAAI;AAAA,IACb,OAAO,GAAG,KAAK;AAAA,IACf,QAAQ,GAAG,MAAM;AAAA,IACjB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,QAAQ;AAAA,EACV;AACA,MAAI,CAAC,SAAS,eAAe,GAAG;AAC9B,WAAO,OAAO,GAAG,OAAO,UAAU;AAClC,gBAAY,GAAG,QAAQ,CAAC;AAAA,MACtB,WAAW;AAAA,MACX,SAAS;AAAA,IACX,GAAG;AAAA,MACD,WAAW;AAAA,MACX,SAAS;AAAA,IACX,CAAC,GAAG;AAAA,MACF,UAAU,gBAAgB;AAAA,MAC1B,QAAQ;AAAA,IACV,CAAC;AAAA,EACH,OAAO;AACL,UAAM,CAAC,WAAWC,QAAO,IAAI,eAAe,gBAAgB,IAAI,UAAU,SAAS,CAAC;AACpF,SAAKA,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ,gBAAgB,OAAO;AACpF,oBAAcA,aAAY,QAAQA,aAAY,SAAS,SAASA,SAAQ,eAAe;AACvF,aAAO,OAAO,GAAG,OAAO,UAAU;AAAA,IACpC;AACA,gBAAY,IAAI,UAAU,SAAS;AACnC,cAAU,KAAK;AAAA,EACjB;AACA,aAAW,IAAI,IAAI,SAAS;AAC5B,YAAU,iBAAiB,UAAU,QAAQ,KAAK,MAAM,IAAI,UAAU,CAAC;AACzE;AAeA,SAAS,aAAa,IAAI,QAAQ,QAAQ,iBAAiB;AACzD,QAAM,eAAe,UAAU;AAC/B,QAAM,eAAe,UAAU;AAC/B,QAAM,eAAe,SAAS,gBAAgB,MAAM;AACpD,QAAM,iBAAiB,iBAAiB,IAAI,EAAE;AAC9C,MAAI,mBAAmB,UAAU;AAC/B,aAAS,gBAAgB,MAAM,iBAAiB;AAAA,EAClD;AACA,SAAO,SAAS,OAAO,UAAU,cAAc,OAAO,UAAU,YAAY;AAC5E,MAAI,CAAC,GAAG,cAAe;AACvB,QAAM,SAAS,GAAG;AAClB,MAAI,aAAa,OAAO;AACxB,MAAI,YAAY,OAAO;AACvB,QAAM,cAAc,YAAY,IAAI;AAGpC,WAAS,eAAe;AACtB,0BAAsB,MAAM;AAC1B,UAAI,CAAC,SAAS,eAAe,GAAG;AAC9B,cAAM,SAAS,aAAa,OAAO;AACnC,cAAM,SAAS,YAAY,OAAO;AAClC,YAAI,cAAc,gBAAgB,WAAW,YAAY,IAAI,GAAG;AAC9D,iBAAO,SAAS;AAAA,YACd,MAAM,OAAO,UAAU;AAAA,YACvB,KAAK,OAAO,UAAU;AAAA,UACxB,CAAC;AACD,uBAAa,OAAO;AACpB,sBAAY,OAAO;AACnB,uBAAa;AAAA,QACf,OAAO;AACL,mBAAS,gBAAgB,MAAM,iBAAiB;AAAA,QAClD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,eAAa;AACf;AAMA,SAAS,eAAe,IAAI;AAC1B,QAAM,YAAY,OAAO,IAAI,EAAE;AAC/B,QAAM,CAAC,OAAM,EAAE,MAAM,IAAI,mBAAmB,IAAI,WAAW,UAAU,EAAE,CAAC;AACxE,MAAI,eAAe,GAAG;AACtB,SAAO,iBAAiB,iBAAiB,YAAY,EAAE,aAAa,YAAY,wBAAwB,kBAAkB;AACxH,mBAAe,aAAa;AAAA,EAC9B;AACA,MAAI,CAAC,aAAc,gBAAe,SAAS;AAC3C,QAAM,eAAe,iBAAiB,YAAY;AAClD,QAAM,eAAe,OAAO,IAAI,YAAY,KAAK,UAAU,YAAY;AACvE,QAAM,MAAM,KAAK,MAAM,UAAU,MAAM,aAAa,GAAG,IAAI,IAAI,aAAa,cAAc;AAC1F,QAAM,OAAO,KAAK,MAAM,UAAU,OAAO,aAAa,IAAI,IAAI,IAAI,aAAa,eAAe;AAC9F,SAAO,CAAC,KAAK,MAAM,OAAO,MAAM;AAClC;AAQA,SAAS,YAAY,IAAI,SAAS,CAAC,GAAG;AACpC,MAAI,aAAa,QAAQ;AACvB,UAAM,aAAa,OAAO,WAAW,kCAAkC;AACvE,UAAM,8BAA8B,WAAW,WAAW,CAAC,SAAS,MAAM,KAAK,CAAC,OAAO;AACvF,QAAI,CAAC,6BAA6B;AAChC,cAAQ,IAAI,EAAE;AACd,UAAI,iBAAiB,EAAE,EAAE,aAAa,UAAU;AAC9C,eAAO,OAAO,GAAG,OAAO;AAAA,UACtB,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AACA,cAAQ,IAAI,WAAW,MAAM,aAAW,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,QAAQ,OAAO,CAAC;AAC/G,UAAI,SAAS,MAAM,GAAG;AACpB,gBAAQ,IAAI,IAAI,MAAM;AAAA,MACxB,OAAO;AACL,gBAAQ,IAAI,IAAI;AAAA,UACd,UAAU;AAAA,UACV,QAAQ;AAAA,WACL,OACJ;AAAA,MACH;AACA,gBAAU,QAAQ,IAAI;AAAA,QACpB,WAAW;AAAA,MACb,CAAC;AACD,cAAQ,IAAI,EAAE;AAAA,IAChB;AAAA,EACF;AACA,SAAO,OAAO,OAAO;AAAA,IACnB,QAAQ;AAAA,IACR,QAAQ,MAAM;AACZ,cAAQ,IAAI,EAAE;AAAA,IAChB;AAAA,IACA,SAAS,MAAM;AACb,cAAQ,OAAO,EAAE;AAAA,IACnB;AAAA,IACA,WAAW,MAAM,QAAQ,IAAI,EAAE;AAAA,EACjC,CAAC;AACH;AAIA,IAAM,eAAe;AAAA,EACnB,SAAS,CAAC,IAAI,YAAY;AACxB,gBAAY,IAAI,QAAQ,SAAS,CAAC,CAAC;AAAA,EACrC;AAAA;AAAA,EAEA,aAAa,OAAO,CAAC;AACvB;", "names": ["mutations", "target", "options"]}