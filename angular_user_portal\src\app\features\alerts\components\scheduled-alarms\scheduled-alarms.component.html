<div class="flex justify-between items-center mt-4">
  <h3 class="text-lg font-semibold">{{ 'UserPortal:SavedAlerts' | i18n }}</h3>
  <button mat-button color="warn" (click)="navigateToAddAlert()">
    + {{ 'UserPortal:AddAlert' | i18n }}
  </button>
</div>

<mat-accordion class="mt-4 scale-90">
  @for(alert of alerts$(); track alert; let i = $index) {
  <mat-expansion-panel (opened)="getInfo(alert.id, alert.type)">
    <mat-expansion-panel-header>
      <mat-panel-title
        class="flex !flex-basis-[unset]"
        (click)="$event.stopPropagation(); $event.preventDefault()"
      >
        <button mat-icon-button [matMenuTriggerFor]="menu" class="ml-auto">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="viewAssignedVehicles(alert)"
          >
            <mat-icon>directions_car</mat-icon>
            <span>{{ 'UserPortal:ViewAssignedVehicles' | i18n }}</span>
          </button>

          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="viewAssignedGroups(alert)"
          >
            <mat-icon>groups</mat-icon>
            <span>{{ 'UserPortal:ViewAssignedGroups' | i18n }}</span>
          </button>

          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="addVehicles(alert)"
          >
            <mat-icon>add_circle</mat-icon>
            <span>{{ 'UserPortal:AddNewVehicles' | i18n }}</span>
          </button>

          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="addGroups(alert)"
          >
            <mat-icon>add_circle</mat-icon>
            <span>{{ 'UserPortal:AddNewGroups' | i18n }}</span>
          </button>

          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="deleteAlert(alert)"
          >
            <mat-icon>delete</mat-icon>
            <span>{{ 'UserPortal:DeleteAlert' | i18n }}</span>
          </button>

          <button
            mat-menu-item
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50 py-2"
          >
            @if (isLoading()) {
            <mat-progress-spinner diameter="24" mode="indeterminate"></mat-progress-spinner>
            } @else {
            <mat-slide-toggle
              [checked]="alert.isEnabled"
              (change)="toggleAlert($event, alert)"
            ></mat-slide-toggle>
            }
            <span>{{ 'UserPortal:ActivateAlert' | i18n }}</span>
          </button>
        </mat-menu>
        <div class="flex-grow">
          <span
            [ngClass]="alert.isEnabled ? 'bg-green-500' : 'bg-gray-400'"
            class="inline-block mx-1 w-2 h-2 rounded-full"
          ></span>

          {{ alert.type || 'UserPortal:AlertType' | i18n }}
        </div>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="flex flex-col p-3 gap-3">
      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray"
          >{{ 'UserPortal:NotificationMethod' | i18n }}:</span
        >
        @for (item of alert.notificationMethods; track $index) {
        <span class="text-main_gray">{{ item | i18n }}</span>
        }
      </div>

      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray">{{ 'UserPortal:Status' | i18n }}:</span>
        <span class="text-main_gray">
          <span
            class="px-2 py-1 text-white rounded"
            [ngClass]="alert.isEnabled ? 'bg-green-500' : 'bg-red-500'"
          >
            {{ alert.isEnabled ? ('UserPortal:Active' | i18n) : ('UserPortal:Inactive' | i18n) }}
          </span>
        </span>
      </div>

      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray">{{ 'UserPortal:CreationDate' | i18n }}:</span>
        <span class="text-main_gray">{{ alert.creationTime | date : 'dd/MM/yyyy' }}</span>
      </div>
      @if (alert.type=='ExceedingSpeed') {
      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray">{{ 'UserPortal:maxSpeed' | i18n }}:</span>
        <span class="text-main_gray">{{
          alertsInfo()[alert.id] ? alertsInfo()[alert.id]().maxSpeed : ''
        }}</span>
      </div>
      }

      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        @if (alert.type=='ExitingRoute') {
        <button mat-flat-button (click)="showRoute(alert.id)">
          {{ 'UserPortal:showRoute' | i18n }}
        </button>
        } @if (['EnteringZone','ExitingZone'].includes(alert.type)) {
        <button mat-flat-button (click)="showGeozone(alert.id, alert.type)">
          {{ 'UserPortal:showGeoZone' | i18n }}
        </button>
        }
      </div>
    </div>
  </mat-expansion-panel>
  }
</mat-accordion>
