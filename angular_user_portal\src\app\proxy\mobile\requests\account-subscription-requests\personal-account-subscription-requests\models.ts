import type { SubscriptionVehicleInfoCreateDto, SubscriptionVehicleInfoDto } from '../models';
import type { AddressDto, AddressFormDto } from '../../../addresses/models';
import type { RequestDto, RequestNoteDto } from '../../models';

export interface PersonalAccountSubscriptionRequestCreateDto {
  subscriptionVehicleInfoCreateDtos: SubscriptionVehicleInfoCreateDto[];
  accountName: string;
  subscriptionPlanKey: string;
  userCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  promoCode?: string;
  address: AddressFormDto;
}

export interface PersonalAccountSubscriptionRequestDetailsDto extends RequestDto {
  ownerId?: string;
  accountName?: string;
  trackerInstallationLocation?: string;
  trackVehicles: SubscriptionVehicleInfoDto[];
  requestNotes: RequestNoteDto[];
  subscriptionPlanKey?: string;
  subscriptionPlanLocalizedName?: string;
  stage?: string;
  price: number;
  userCount: number;
  smsBundleId?: string;
  subscriptionDurationInMonths: number;
  paymentUrl?: string;
  address: AddressDto;
}
