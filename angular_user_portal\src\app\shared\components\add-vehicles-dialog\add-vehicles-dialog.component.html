<div class="p-6 bg-white rounded-lg shadow-lg text-sm">
  <h2 class="font-semibold text-indigo-600">
    {{ 'UserPortal:addvehicle' | i18n }}
  </h2>

  <div class="border-t border-gray-300 my-1 pb-1"></div>

  <div class="space-y-2">
    <div class="flex justify-between items-center">
      <mat-label class="pr-2">{{ 'UserPortal:SelectVehicles' | i18n }}</mat-label>
    </div>
    <mat-form-field appearance="outline" class="w-full">
      <mat-select [(ngModel)]="vehicleId" placeholder="{{ 'UserPortal:SelectVehicles' | i18n }}">
        @for (vehicle of availableVehicles$(); track vehicle) {
        <mat-option [value]="vehicle.id">
          {{ vehicle.licensePlateSerial + ' ' + vehicle.licensePlateSubClass }}
        </mat-option>
        }
      </mat-select>
    </mat-form-field>
  </div>

  <div class="mt-5 flex justify-between items-center">
    <button mat-button mat-flat-button class="cancleButton" (click)="closeDialog()">
      {{ 'UserPortal:cancel' | i18n }}
    </button>
    <button mat-button mat-flat-button (click)="save()" [disabled]="!vehicleId">
      {{ 'UserPortal:save' | i18n }}
    </button>
  </div>
</div>
