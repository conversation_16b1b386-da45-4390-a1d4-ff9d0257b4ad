import type { AlertDefinitionDto } from './dtos/models';
import type { AlertDefinitionTypePagedResultRequestDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { VehicleGroupDto } from '../vehicle-groups/models';
import type { VehicleDto } from '../vehicles/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class AlertDefinitionService {
  apiName = 'Default';
  

  addVehicle = (id: string, vehicleId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/alertDefinition/${id}/vehicle/${vehicleId}`,
    },
    { apiName: this.apiName,...config });
  

  addVehicleGroup = (id: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/alertDefinition/${id}/vehicleGroup/${vehicleGroupId}`,
    },
    { apiName: this.apiName,...config });
  

  disabledAlert = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/alertDefinition/${id}/disabledAlert`,
    },
    { apiName: this.apiName,...config });
  

  enabledAlert = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/alertDefinition/${id}/enabledAlert`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: AlertDefinitionTypePagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AlertDefinitionDto>>({
      method: 'GET',
      url: '/api/app/alertDefinition',
      params: { type: input.type, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getVehicleGroups = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VehicleGroupDto>>({
      method: 'GET',
      url: `/api/app/alertDefinition/${id}/vehicleGroups`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getVehicles = (id: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VehicleDto>>({
      method: 'GET',
      url: `/api/app/alertDefinition/${id}/vehicles`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  remove = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/alertDefinition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  removeVehicle = (id: string, vehicleId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/alertDefinition/${id}/vehicle/${vehicleId}`,
    },
    { apiName: this.apiName,...config });
  

  removeVehicleGroup = (id: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/alertDefinition/${id}/vehicleGroup/${vehicleGroupId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
