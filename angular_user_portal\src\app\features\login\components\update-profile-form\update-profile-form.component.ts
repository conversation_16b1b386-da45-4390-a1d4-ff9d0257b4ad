import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';

import { Component, inject, input } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatStepperModule } from '@angular/material/stepper';
import { Router } from '@angular/router';
import { GeoNodeService } from '@proxy/mobile/geo-nodes';
import { MobileIdentityUserService } from '@proxy/mobile/mobile-identity-users';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { filter, map, switchMap } from 'rxjs';

@Component({
  selector: 'app-update-profile-form',
  standalone: true,
  templateUrl: `./update-profile-form.component.html`,
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false },
    },
  ],
  imports: [
    ValidationComponent,
    MatStepperModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    LanguagePipe,
  ],
})
export class UpdateProfileFormComponent {
  index: number;
  personal = true;

  private router = inject(Router);
  private mobileIdentityUserService = inject(MobileIdentityUserService);
  private geoNodeService = inject(GeoNodeService);

  info = input.required<FormGroup>();

  update_profile() {
    const obj = this.info().getRawValue();
    this.mobileIdentityUserService.updateProfile(obj).subscribe(val => {
      this.router.navigate(['track-accounts']);
    });
  }
}
