:host {
  --mdc-checkbox-unselected-icon-color: #ffffff00;
  --mdc-checkbox-unselected-focus-state-layer-color: #ffffff00;
  --mdc-checkbox-unselected-hover-icon-color: #ffffff00;
  --mdc-checkbox-unselected-focus-icon-color: #ffffff00;
  --sys-tertiary: #ffffff;
  --sys-on-tertiary: #ffffff00;
  --mdc-checkbox-unselected-icon-color: #ffffff00;
  --mdc-checkbox-unselected-focus-state-layer-color: #ffffff00;
  --mdc-checkbox-unselected-hover-icon-color: #ffffff00;
  --mdc-checkbox-unselected-focus-icon-color: #ffffff00;
}

:host ::ng-deep {
  .mdc-text-field--no-label:not(.mdc-text-field--textarea)
    .mat-mdc-form-field-input-control.mdc-text-field__input,
  .mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control {
    height: 25px;
  }

  .mat-checkbox-layout {
    align-items: center;
  }

  .mat-mdc-text-field-wrapper {
    background-color: white !important;
    border-radius: 8px;
  }

  .mat-mdc-checkbox .mat-internal-form-field {
    width: 100%;

    .mdc-label {
      display: flex;
      flex-grow: 1;
    }

    .mdc-checkbox__background {
      background-color: white;
    }

    .mdc-checkbox__checkmark {
      color: inherit;
    }

    .mdc-checkbox:hover .mdc-checkbox__native-control:not(:checked) ~ .mdc-checkbox__background,
    .mdc-checkbox:hover
      .mdc-checkbox__native-control:not(:indeterminate)
      ~ .mdc-checkbox__background {
      background-color: white;
    }
  }

  .mat-mdc-chip {
    margin-left: 5px !important;
  }

  .mat-mdc-standard-chip {
    background-color: #8a80f8 !important;
  }

  .mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing,
  .mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label {
    color: #fff;
  }

  .gauge-card {
    background: white;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 450px !important;
  }
}
