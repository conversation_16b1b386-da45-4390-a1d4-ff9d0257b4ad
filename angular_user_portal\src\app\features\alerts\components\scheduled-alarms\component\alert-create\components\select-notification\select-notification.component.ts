import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, computed, inject, input, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import {
  GetMobileUserProfileDto,
  MobileIdentityUserService,
} from '@proxy/mobile/mobile-identity-users';
import { openVerifyEmailDialog } from '@shared/components/verify-email-dialog/verify-email-dialog.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'alert-select-notification',
  standalone: true,
  templateUrl: `./select-notification.component.html`,
  imports: [<PERSON><PERSON>abel, MatCheckbox, NgStyle, LanguagePipe, MatIcon, NgClass],
})
export class SelectNotificationComponent {
  notificationMethods = signal([
    {
      icon: 'assets/images/svg/by-app.svg',
      name: 'UserPortal:NotificationMobile',
      color: '#F2F2F2',
      value: 'MobileNotification',
    },
    {
      icon: 'assets/images/svg/by-message.svg',
      name: 'UserPortal:NotificationSms',
      color: '#D9CEF2',
      value: 'Sms',
    },
    {
      icon: 'assets/images/svg/by-email.svg',
      name: 'UserPortal:NotificationEmail',
      color: '#8A80F8',
      value: 'Mailing',
    },
  ]);

  user = signal<GetMobileUserProfileDto | any>({});

  private userService = inject(MobileIdentityUserService);
  private dialog = inject(MatDialog);
  ngOnInit(): void {
    this.loadProfile();
  }
  loadProfile() {
    this.userService.getProfile().subscribe(userData => {
      this.user.set(userData);
    });
  }

  alertForm = input.required<FormGroup>();
  methods = computed(() => {
    return this.alertForm().get('notificationMethods');
  });

  async addMethod(method) {
    let v: string[] = this.methods().value;
    if (v.includes(method)) {
      v = v.filter(m => m != method);
    } else {
      v.push(method);
    }
    if (v.includes('Mailing')) {
      if (!this.user().isVerified) {
        const result = await firstValueFrom(openVerifyEmailDialog(this.dialog, this.user()));
        if (result) {
          this.loadProfile();
        }
        v = v.filter(m => m != 'Mailing');
      }
    }
    this.alertForm().controls.notificationMethods.setValue(v);
  }
}
