import type { BusinessAccountSubscriptionRequestCreateDto, BusinessAccountSubscriptionRequestCreateWithoutVehiclesDataDto, BusinessAccountSubscriptionRequestDetailsDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { CreateAccountRequestPaymentDto } from '../models';
import type { BillDto } from '../../../payments/bills/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class BusinessAccountSubscriptionRequestService {
  apiName = 'Default';
  

  create = (createDto: BusinessAccountSubscriptionRequestCreateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/businessAccountSubscriptionRequest',
      body: createDto,
    },
    { apiName: this.apiName,...config });
  

  createPayment = (input: CreateAccountRequestPaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/businessAccountSubscriptionRequest/payment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  createTempBill = (createDto: BusinessAccountSubscriptionRequestCreateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/businessAccountSubscriptionRequest/tempBill',
      body: createDto,
    },
    { apiName: this.apiName,...config });
  

  createTempBillWithoutVehiclesData = (createDataDto: BusinessAccountSubscriptionRequestCreateWithoutVehiclesDataDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/businessAccountSubscriptionRequest/tempBillWithoutVehiclesData',
      body: createDataDto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BusinessAccountSubscriptionRequestDetailsDto>({
      method: 'GET',
      url: `/api/app/businessAccountSubscriptionRequest/${id}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
