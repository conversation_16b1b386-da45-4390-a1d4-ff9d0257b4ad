import { <PERSON>son<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, computed, input, model, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatChipListbox, MatChipsModule } from '@angular/material/chips';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatOption, MatSelect, MatSelectTrigger } from '@angular/material/select';
import { GeoZoneDto } from '@proxy/mobile/geo-zones';
import { RouteDto } from '@proxy/mobile/routes/dtos';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'alert-path-alert-type',
  standalone: true,
  templateUrl: `./path-alert-type.component.html`,
  imports: [
    <PERSON><PERSON><PERSON><PERSON>,
    MatForm<PERSON>ield,
    MatSelect,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatSelectTrigger,
    MatOption,
    MatChipsModule,
    MatIcon,
  ],
})
export class PathAlertTypeComponent {
  alertForm = input.required<FormGroup>();
  paths = input.required<Map<string, RouteDto>>();

  removeGeoZone(id) {
    const control = this.alertForm().get('routeIds');
    const newVal = [...control.value].filter(v => v != id);
    control.setValue(newVal);
  }
}
