import { DatePip<PERSON>, NgClass } from '@angular/common';
import { Component, computed, inject, signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>on, MatMiniFabButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { BillService } from '@proxy/mobile/payments/bills';
import { BillDto, BillLineItemDto } from '@proxy/mobile/payments/bills/dtos';
import { BillStatus } from '@proxy/payments/bills';
import { pricingTypeOptions } from '@proxy/payments/pricing-items';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-price-offer-dialog',
  standalone: true,
  templateUrl: `./price-offer-dialog.component.html`,
  imports: [Mat<PERSON>utton, LanguagePipe, NgC<PERSON>, DateP<PERSON><PERSON>, MatIcon, MatM<PERSON><PERSON>abButton],
})
export class PriceOfferDialogComponent {
  dialogRef = inject(MatDialogRef<PriceOfferDialogComponent>);
  data: BillDto & { id: string; pay: boolean } = inject(MAT_DIALOG_DATA);
  billService = inject(BillService);

  filter$ = signal('all');
  details$ = signal(false);
  filters = signal<string[]>(['all', ...pricingTypeOptions.map(x => x.value)]);

  pricingTypeOptionsCounts = signal<any>({});

  bills = signal<BillDto>({
    status: BillStatus.Draft,
    appliedDiscounts: [],
    billLineItems: [],
    total: 0,
    totalBeforeDiscounts: 0,
  });

  filteredBillLineItems$ = computed(() => {
    const filter = this.filter$();
    return this.bills().billLineItems.filter(x => {
      if (filter === 'all') {
        return true;
      }
      return x.pricingType === filter;
    });
  });
  ngOnInit(): void {
    this.getbill();
  }

  async getbill() {
    let bill: BillDto = { ...this.data };
    if (this.data.id) {
      bill = await firstValueFrom(this.billService.getBill(this.data.id));
    }
    const r = {};
    let onceitems: BillLineItemDto | null = null;
    bill.billLineItems = bill.billLineItems.reduce((acc, cu) => {
      if (
        !['PricingItemKeys:DeviceInstallation', 'PricingItemKeys:Device'].includes(
          cu.pricingItemKey
        )
      ) {
        return [...acc, cu];
      } else {
        if (!onceitems) {
          onceitems = { ...cu };
        } else {
          onceitems.pricingItemDisplayName =
            onceitems.pricingItemDisplayName + ' + ' + cu.pricingItemDisplayName;
          onceitems.unitPrice = onceitems.unitPrice + cu.unitPrice;
          onceitems.quantity = onceitems.quantity + cu.quantity;
        }
        return [...acc];
      }
    }, []);
    if (onceitems) bill.billLineItems.push(onceitems);
    bill.billLineItems.map(x => {
      if (r[x.pricingType]) {
        r[x.pricingType]++;
      } else {
        r[x.pricingType] = 1;
      }
    });
    r['all'] = bill.billLineItems.length;
    this.pricingTypeOptionsCounts.set(r);
    this.bills.set(bill);
  }

  closeDialog(data: boolean | undefined = undefined) {
    if (data == undefined) {
      this.dialogRef.close();
    } else {
      if (data) {
        this.dialogRef.close(this.data.id ?? this.data.requestId);
      } else {
        this.dialogRef.close(false);
      }
    }
  }
}

export const openPriceOfferDialog = (dialog: MatDialog, data: any) => {
  return dialog.open(PriceOfferDialogComponent, {
    data: data,
  });
};
