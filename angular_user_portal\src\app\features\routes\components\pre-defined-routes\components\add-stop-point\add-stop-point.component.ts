import { CommonModule, NgStyle } from '@angular/common';
import { Component, inject, input, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Router } from '@angular/router';
import { RouteService } from '@proxy/mobile/routes';
import {
  changeNodeDto,
  CustomLine,
  CustomMarker,
  MapComponent,
} from '@shared/components/map/map.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { colors } from '@shared/constants/colors.constants';
import { colorToHex, hexToColor } from '@shared/functions/hex-to-color';
import { live_icon } from '@shared/helper-assets/live';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';

@Component({
  selector: 'app-add-stop-point',
  standalone: true,
  templateUrl: './add-stop-point.component.html',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    MapComponent,
    LanguagePipe,
    MatSelectModule,
    NgStyle,
    ValidationComponent,
  ],
})
export class AddStopPointComponent implements OnInit {
  draw = { marker: true };
  nodes$ = signal<Layer[]>([]);
  line$ = signal<Layer[]>([]);
  options$ = signal<MapOptions>({});
  route_id = input<string>('');
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private routeService = inject(RouteService);

  colors = signal(colors);
  form: FormGroup = this.fb.group({
    name: [''],
    hexColor: ['#ffffff'],
    point: [],
  });
  ngOnInit(): void {
    this.getInfo(this.route_id());
  }

  getInfo(id) {
    this.routeService.get(id).subscribe(val => {
      const r = val.line.map(v => [+v.latitudeY, +v.longitudeX]);
      const n = val.stopPoints.map(v => {
        return CustomMarker({
          latlang: [v.point.latitudeY, v.point.longitudeX],
          icon: live_icon(hexToColor(v.color)),
        });
      });
      const line = [CustomLine({ line: r, color: hexToColor(val.color) })];
      this.options$.set({ zoom: 12, center: latLng(r[0] as LatLngTuple) });
      this.nodes$.set(n);
      this.line$.set(line);
    });
  }

  onNodeChange(event: changeNodeDto) {
    console.log(event);
    const { lat, lng } = event.event.layer._latlng;
    const pos = { longitudeX: lng, latitudeY: lat };
    if (this.form.controls['point'].value) {
    }
    this.form.controls['point'].setValue(pos);
  }

  save() {
    const obj = { ...this.form.value };
    obj.hexColor = colorToHex(obj.hexColor);
    this.routeService.addStopPointToRoute(this.route_id(), obj).subscribe(res => {
      this.router.navigate(['/main', 'routes']);
    });
  }

  close() {
    this.router.navigate(['/main', 'routes']);
  }
}
