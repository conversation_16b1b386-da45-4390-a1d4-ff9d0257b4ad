{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.feature-management/fesm2022/abp-ng.feature-management.mjs"], "sourcesContent": ["import * as i2 from '@abp/ng.core';\nimport { TrackByService, ConfigStateService, LocalizationModule, ReplaceableTemplateDirective, CoreModule } from '@abp/ng.core';\nimport { FeaturesService } from '@abp/ng.feature-management/proxy';\nimport * as i1 from '@abp/ng.theme.shared';\nimport { ToasterService, ConfirmationService, Confirmation, ThemeSharedModule } from '@abp/ng.theme.shared';\nimport * as i0 from '@angular/core';\nimport { HostBinding, Input, Directive, inject, EventEmitter, Output, Component, APP_INITIALIZER, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i3 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i4 from '@ng-bootstrap/ng-bootstrap';\nimport { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';\nimport { finalize } from 'rxjs/operators';\nimport { SettingTabsService } from '@abp/ng.setting-management/config';\nconst _forTrack0 = ($index, $item) => $item.name;\nconst _forTrack1 = ($index, $item) => $item.id;\nconst _forTrack2 = ($index, $item) => $item.value;\nconst _c0 = () => ({\n  size: \"lg\"\n});\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nfunction FeatureManagementComponent_Conditional_0_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"AbpFeatureManagement::Features\"));\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_1_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const feature_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(feature_r6.value, $event) || (feature_r6.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_1_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const feature_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.onCheckboxClick($event, feature_r6));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_1_ng_container_4_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(3);\n    const descTmp_r7 = i0.ɵɵreference(6);\n    i0.ɵɵclassProp(\"px-4\", !!feature_r6.parentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", feature_r6.name);\n    i0.ɵɵtwoWayProperty(\"ngModel\", feature_r6.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"htmlFor\", feature_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r6.displayName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", descTmp_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c1, feature_r6.description));\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"label\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_2_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const feature_r6 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(feature_r6.value, $event) || (feature_r6.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_2_ng_container_4_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(3);\n    const descTmp_r7 = i0.ɵɵreference(6);\n    i0.ɵɵclassProp(\"px-2\", !!feature_r6.parentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"htmlFor\", feature_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r6.displayName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", feature_r6.name);\n    i0.ɵɵtwoWayProperty(\"ngModel\", feature_r6.value);\n    i0.ɵɵproperty(\"abpFeatureManagementFreeText\", feature_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", descTmp_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(9, _c1, feature_r6.description));\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", item_r10.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, (item_r10.displayText == null ? null : item_r10.displayText.resourceName) + \"::\" + (item_r10.displayText == null ? null : item_r10.displayText.name)), \" \");\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"label\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_Template_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const feature_r6 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(feature_r6.value, $event) || (feature_r6.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵrepeaterCreate(4, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_For_5_Template, 3, 4, \"option\", 29, _forTrack2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_ng_container_6_Template, 1, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const feature_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵnextContext(3);\n    const descTmp_r7 = i0.ɵɵreference(6);\n    i0.ɵɵclassProp(\"px-2\", !!feature_r6.parentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"htmlFor\", feature_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(feature_r6.displayName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", feature_r6.name);\n    i0.ɵɵtwoWayProperty(\"ngModel\", feature_r6.value);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(feature_r6.valueType.itemSource == null ? null : feature_r6.valueType.itemSource.items);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", descTmp_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c1, feature_r6.description));\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Conditional_0_Template, 7, 10, \"div\", 20);\n  }\n  if (rf & 2) {\n    const feature_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional((feature_r6.valueType.itemSource == null ? null : feature_r6.valueType.itemSource.items == null ? null : feature_r6.valueType.itemSource.items.length) ? 0 : -1);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const feature_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", feature_r6.displayName, \" \");\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"keyup.enter\", function FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Template_div_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.save());\n    });\n    i0.ɵɵtemplate(1, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_1_Template, 5, 10, \"div\", 19)(2, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_2_Template, 5, 11, \"div\", 20)(3, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_3_Template, 1, 1)(4, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Case_4_Template, 1, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_31_0;\n    const feature_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngStyle\", feature_r6.style);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional((tmp_31_0 = feature_r6.valueType == null ? null : feature_r6.valueType.name) === ctx_r1.valueTypes.ToggleStringValueType ? 1 : tmp_31_0 === ctx_r1.valueTypes.FreeTextStringValueType ? 2 : tmp_31_0 === ctx_r1.valueTypes.SelectionStringValueType ? 3 : 4);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"hr\", 16);\n    i0.ɵɵrepeaterCreate(3, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_For_4_Template, 5, 2, \"div\", 17, _forTrack1);\n  }\n  if (rf & 2) {\n    const group_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.selectedGroupDisplayName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1.features[group_r11.name]);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 11)(1, \"a\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_ng_template_3_Template, 5, 1, \"ng-template\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngbNavItem\", group_r11.displayName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(group_r11.displayName);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_ng_template_5_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const description_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(description_r12);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_ng_template_5_Conditional_0_Template, 2, 1, \"small\", 30);\n  }\n  if (rf & 2) {\n    const description_r12 = ctx.$implicit;\n    i0.ɵɵconditional(description_r12 ? 0 : -1);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"ul\", 10, 3);\n    i0.ɵɵtwoWayListener(\"activeIdChange\", function FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_Template_ul_activeIdChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedGroupDisplayName, $event) || (ctx_r1.selectedGroupDisplayName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵrepeaterCreate(3, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_For_4_Template, 4, 2, \"li\", 11, _forTrack0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_ng_template_5_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(7, \"div\", 12);\n    i0.ɵɵelement(8, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const nav_r13 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"activeId\", ctx_r1.selectedGroupDisplayName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1.groups);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngbNavOutlet\", nav_r13);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"AbpFeatureManagement::NoFeatureFoundMessage\"), \" \");\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_1_Template, 9, 2)(2, FeatureManagementComponent_Conditional_0_ng_template_3_Conditional_2_Template, 3, 3, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.groups.length ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1.groups.length ? 2 : -1);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_5_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"abp-button\", 34);\n    i0.ɵɵlistener(\"click\", function FeatureManagementComponent_Conditional_0_ng_template_5_Conditional_3_Template_abp_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.resetToDefault());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.modalBusy);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpFeatureManagement::ResetToDefault\"), \" \");\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_5_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"abp-button\", 35);\n    i0.ɵɵlistener(\"click\", function FeatureManagementComponent_Conditional_0_ng_template_5_Conditional_4_Template_abp_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.modalBusy);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"AbpFeatureManagement::Save\"), \" \");\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, FeatureManagementComponent_Conditional_0_ng_template_5_Conditional_3_Template, 3, 4, \"abp-button\", 32)(4, FeatureManagementComponent_Conditional_0_ng_template_5_Conditional_4_Template, 3, 4, \"abp-button\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"AbpFeatureManagement::Cancel\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r1.groups.length ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.groups.length ? 4 : -1);\n  }\n}\nfunction FeatureManagementComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"abp-modal\", 6);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function FeatureManagementComponent_Conditional_0_Template_abp_modal_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.visible, $event) || (ctx_r1.visible = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(1, FeatureManagementComponent_Conditional_0_ng_template_1_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, FeatureManagementComponent_Conditional_0_ng_template_3_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, FeatureManagementComponent_Conditional_0_ng_template_5_Template, 5, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.visible);\n    i0.ɵɵproperty(\"busy\", ctx_r1.modalBusy)(\"options\", i0.ɵɵpureFunction0(3, _c0));\n  }\n}\nconst _c2 = () => ({\n  value: \"T\"\n});\nconst _c3 = a0 => ({\n  value: a0\n});\nconst _c4 = a0 => ({\n  value: a0,\n  twoWay: true\n});\nconst _c5 = (a0, a1, a2) => ({\n  providerName: a0,\n  providerKey: a1,\n  visible: a2\n});\nconst _c6 = a0 => ({\n  visibleChange: a0\n});\nconst _c7 = (a0, a1) => ({\n  inputs: a0,\n  outputs: a1,\n  componentKey: \"FeatureManagement.FeatureManagementComponent\"\n});\nfunction FeatureManagementTabComponent_Conditional_7_abp_feature_management_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"abp-feature-management\", 5);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function FeatureManagementTabComponent_Conditional_7_abp_feature_management_0_Template_abp_feature_management_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.visibleFeatures, $event) || (ctx_r1.visibleFeatures = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.visibleFeatures);\n    i0.ɵɵproperty(\"providerKey\", ctx_r1.providerKey);\n  }\n}\nfunction FeatureManagementTabComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FeatureManagementTabComponent_Conditional_7_abp_feature_management_0_Template, 1, 2, \"abp-feature-management\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction2(12, _c7, i0.ɵɵpureFunction3(6, _c5, i0.ɵɵpureFunction0(1, _c2), i0.ɵɵpureFunction1(2, _c3, ctx_r1.providerKey), i0.ɵɵpureFunction1(4, _c4, ctx_r1.visibleFeatures)), i0.ɵɵpureFunction1(10, _c6, ctx_r1.onVisibleFeaturesChange)));\n  }\n}\nconst INPUT_TYPES = {\n  numeric: 'number',\n  default: 'text'\n};\nclass FreeTextInputDirective {\n  // eslint-disable-next-line @angular-eslint/no-input-rename\n  set feature(val) {\n    this._feature = val;\n    this.setInputType();\n  }\n  get feature() {\n    return this._feature;\n  }\n  setInputType() {\n    const validatorType = this.feature?.valueType?.validator?.name.toLowerCase();\n    this.type = INPUT_TYPES[validatorType] ?? INPUT_TYPES.default;\n  }\n  static {\n    this.ɵfac = function FreeTextInputDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FreeTextInputDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FreeTextInputDirective,\n      selectors: [[\"input\", \"abpFeatureManagementFreeText\", \"\"]],\n      hostVars: 1,\n      hostBindings: function FreeTextInputDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        feature: [0, \"abpFeatureManagementFreeText\", \"feature\"]\n      },\n      exportAs: [\"inputAbpFeatureManagementFreeText\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FreeTextInputDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: 'input[abpFeatureManagementFreeText]',\n      exportAs: 'inputAbpFeatureManagementFreeText'\n    }]\n  }], null, {\n    feature: [{\n      type: Input,\n      args: ['abpFeatureManagementFreeText']\n    }],\n    type: [{\n      type: HostBinding,\n      args: ['type']\n    }]\n  });\n})();\nvar ValueTypes;\n(function (ValueTypes) {\n  ValueTypes[\"ToggleStringValueType\"] = \"ToggleStringValueType\";\n  ValueTypes[\"FreeTextStringValueType\"] = \"FreeTextStringValueType\";\n  ValueTypes[\"SelectionStringValueType\"] = \"SelectionStringValueType\";\n})(ValueTypes || (ValueTypes = {}));\nclass FeatureManagementComponent {\n  constructor() {\n    this.track = inject(TrackByService);\n    this.toasterService = inject(ToasterService);\n    this.service = inject(FeaturesService);\n    this.configState = inject(ConfigStateService);\n    this.confirmationService = inject(ConfirmationService);\n    this.groups = [];\n    this.valueTypes = ValueTypes;\n    this.visibleChange = new EventEmitter();\n    this.modalBusy = false;\n  }\n  get visible() {\n    return this._visible;\n  }\n  set visible(value) {\n    if (this._visible === value) {\n      return;\n    }\n    this._visible = value;\n    this.visibleChange.emit(value);\n    if (value) {\n      this.openModal();\n      return;\n    }\n  }\n  openModal() {\n    if (!this.providerName) {\n      throw new Error('providerName is required.');\n    }\n    this.getFeatures();\n  }\n  getFeatures() {\n    this.service.get(this.providerName, this.providerKey).subscribe(res => {\n      if (!res.groups?.length) return;\n      this.groups = res.groups.map(({\n        name,\n        displayName\n      }) => ({\n        name,\n        displayName\n      }));\n      this.selectedGroupDisplayName = this.groups[0].displayName;\n      this.features = res.groups.reduce((acc, val) => ({\n        ...acc,\n        [val.name]: mapFeatures(val.features, document.body.dir)\n      }), {});\n    });\n  }\n  save() {\n    if (this.modalBusy) return;\n    const changedFeatures = [];\n    Object.keys(this.features).forEach(key => {\n      this.features[key].forEach(feature => {\n        if (feature.value !== feature.initialValue) changedFeatures.push({\n          name: feature.name,\n          value: `${feature.value}`\n        });\n      });\n    });\n    if (!changedFeatures.length) {\n      this.visible = false;\n      return;\n    }\n    this.modalBusy = true;\n    this.service.update(this.providerName, this.providerKey, {\n      features: changedFeatures\n    }).pipe(finalize(() => this.modalBusy = false)).subscribe(() => {\n      this.visible = false;\n      this.toasterService.success('AbpUi::SavedSuccessfully');\n      if (!this.providerKey) {\n        // to refresh host's features\n        this.configState.refreshAppState().subscribe();\n      }\n    });\n  }\n  resetToDefault() {\n    this.confirmationService.warn('AbpFeatureManagement::AreYouSureToResetToDefault', 'AbpFeatureManagement::AreYouSure').subscribe(status => {\n      if (status === Confirmation.Status.confirm) {\n        this.service.delete(this.providerName, this.providerKey).subscribe(() => {\n          this.toasterService.success('AbpFeatureManagement::ResetedToDefault');\n          this.visible = false;\n          if (!this.providerKey) {\n            // to refresh host's features\n            this.configState.refreshAppState().subscribe();\n          }\n        });\n      }\n    });\n  }\n  onCheckboxClick(val, feature) {\n    if (val) {\n      this.checkToggleAncestors(feature);\n    } else {\n      this.uncheckToggleDescendants(feature);\n    }\n  }\n  uncheckToggleDescendants(feature) {\n    this.findAllDescendantsOfByType(feature, ValueTypes.ToggleStringValueType).forEach(node => this.setFeatureValue(node, false));\n  }\n  checkToggleAncestors(feature) {\n    this.findAllAncestorsOfByType(feature, ValueTypes.ToggleStringValueType).forEach(node => this.setFeatureValue(node, true));\n  }\n  findAllAncestorsOfByType(feature, type) {\n    let parent = this.findParentByType(feature, type);\n    const ancestors = [];\n    while (parent) {\n      ancestors.push(parent);\n      parent = this.findParentByType(parent, type);\n    }\n    return ancestors;\n  }\n  findAllDescendantsOfByType(feature, type) {\n    const descendants = [];\n    const queue = [feature];\n    while (queue.length) {\n      const node = queue.pop();\n      const newDescendants = this.findChildrenByType(node, type);\n      descendants.push(...newDescendants);\n      queue.push(...newDescendants);\n    }\n    return descendants;\n  }\n  findParentByType(feature, type) {\n    return this.getCurrentGroup().find(f => f.valueType.name === type && f.name === feature.parentName);\n  }\n  findChildrenByType(feature, type) {\n    return this.getCurrentGroup().filter(f => f.valueType.name === type && f.parentName === feature.name);\n  }\n  getCurrentGroup() {\n    return this.features[this.selectedGroupDisplayName] ?? [];\n  }\n  setFeatureValue(feature, val) {\n    feature.value = val;\n  }\n  static {\n    this.ɵfac = function FeatureManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FeatureManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FeatureManagementComponent,\n      selectors: [[\"abp-feature-management\"]],\n      inputs: {\n        providerKey: \"providerKey\",\n        providerName: \"providerName\",\n        visible: \"visible\"\n      },\n      outputs: {\n        visibleChange: \"visibleChange\"\n      },\n      exportAs: [\"abpFeatureManagement\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [\"nav\", \"ngbNav\"], [\"descTmp\", \"\"], [3, \"visible\", \"busy\", \"options\"], [3, \"visibleChange\", \"visible\", \"busy\", \"options\"], [1, \"row\"], [1, \"col\"], [1, \"col-md-4\"], [\"ngbNav\", \"\", \"orientation\", \"vertical\", 1, \"nav-pills\", 3, \"activeIdChange\", \"activeId\"], [3, \"ngbNavItem\"], [1, \"col-md-8\"], [1, \"py-0\", 3, \"ngbNavOutlet\"], [\"ngbNavLink\", \"\"], [\"ngbNavContent\", \"\"], [1, \"mt-2\", \"mb-3\"], [1, \"mt-2\", 3, \"ngStyle\"], [1, \"mt-2\", 3, \"keyup.enter\", \"ngStyle\"], [1, \"form-check\", 3, \"px-4\"], [1, \"mb-3\", \"form-group\", 3, \"px-2\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [1, \"form-check-label\", 3, \"htmlFor\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"mb-3\", \"form-group\"], [1, \"form-label\", 3, \"htmlFor\"], [\"type\", \"text\", 1, \"form-control\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"abpFeatureManagementFreeText\"], [1, \"form-select\", 3, \"ngModelChange\", \"id\", \"ngModel\"], [3, \"ngValue\"], [1, \"d-block\", \"form-text\", \"text-muted\"], [\"abpClose\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-link\"], [\"buttonClass\", \"btn btn-outline-primary\", \"aria-hidden\", \"true\", 3, \"disabled\"], [\"iconClass\", \"fa fa-check\", \"aria-hidden\", \"true\", 3, \"disabled\"], [\"buttonClass\", \"btn btn-outline-primary\", \"aria-hidden\", \"true\", 3, \"click\", \"disabled\"], [\"iconClass\", \"fa fa-check\", \"aria-hidden\", \"true\", 3, \"click\", \"disabled\"]],\n      template: function FeatureManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, FeatureManagementComponent_Conditional_0_Template, 7, 4, \"abp-modal\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.visible ? 0 : -1);\n        }\n      },\n      dependencies: [ThemeSharedModule, i1.ButtonComponent, i1.ModalComponent, i1.ModalCloseDirective, LocalizationModule, i2.LocalizationPipe, FormsModule, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel, NgbNavModule, i4.NgbNavContent, i4.NgbNav, i4.NgbNavItem, i4.NgbNavItemRole, i4.NgbNavLink, i4.NgbNavLinkBase, i4.NgbNavOutlet, FreeTextInputDirective, NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FeatureManagementComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'abp-feature-management',\n      exportAs: 'abpFeatureManagement',\n      imports: [ThemeSharedModule, LocalizationModule, FormsModule, NgbNavModule, FreeTextInputDirective, NgTemplateOutlet],\n      template: \"@if (visible) {\\r\\n  <abp-modal [(visible)]=\\\"visible\\\" [busy]=\\\"modalBusy\\\" [options]=\\\"{ size: 'lg' }\\\">\\r\\n    <ng-template #abpHeader>\\r\\n      <h3>{{ 'AbpFeatureManagement::Features' | abpLocalization }}</h3>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template #abpBody>\\r\\n      <div class=\\\"row\\\">\\r\\n        @if (groups.length) {\\r\\n          <div class=\\\"col-md-4\\\">\\r\\n            <ul\\r\\n              ngbNav\\r\\n              #nav=\\\"ngbNav\\\"\\r\\n              [(activeId)]=\\\"selectedGroupDisplayName\\\"\\r\\n              class=\\\"nav-pills\\\"\\r\\n              orientation=\\\"vertical\\\"\\r\\n            >\\r\\n              @for (group of groups; track group.name) {\\r\\n                <li [ngbNavItem]=\\\"group.displayName\\\">\\r\\n                  <a ngbNavLink>{{ group.displayName }}</a>\\r\\n                  <ng-template ngbNavContent>\\r\\n                    <h4>{{ selectedGroupDisplayName }}</h4>\\r\\n                    <hr class=\\\"mt-2 mb-3\\\" />\\r\\n\\r\\n                    @for (feature of features[group.name]; track feature.id; let i = $index) {\\r\\n                      <div class=\\\"mt-2\\\" [ngStyle]=\\\"feature.style\\\" (keyup.enter)=\\\"save()\\\">\\r\\n                        @switch (feature.valueType?.name) {\\r\\n                          @case (valueTypes.ToggleStringValueType) {\\r\\n                            <div class=\\\"form-check\\\" [class.px-4]=\\\"!!feature.parentName\\\">\\r\\n                              <input\\r\\n                                class=\\\"form-check-input\\\"\\r\\n                                type=\\\"checkbox\\\"\\r\\n                                [id]=\\\"feature.name\\\"\\r\\n                                [(ngModel)]=\\\"feature.value\\\"\\r\\n                                (ngModelChange)=\\\"onCheckboxClick($event, feature)\\\"\\r\\n                              />\\r\\n\\r\\n                              <label class=\\\"form-check-label\\\" [htmlFor]=\\\"feature.name\\\">{{\\r\\n                                feature.displayName\\r\\n                              }}</label>\\r\\n                              <ng-container\\r\\n                                *ngTemplateOutlet=\\\"\\r\\n                                  descTmp;\\r\\n                                  context: { $implicit: feature.description }\\r\\n                                \\\"\\r\\n                              ></ng-container>\\r\\n                            </div>\\r\\n                          }\\r\\n                          @case (valueTypes.FreeTextStringValueType) {\\r\\n                            <div class=\\\"mb-3 form-group\\\" [class.px-2]=\\\"!!feature.parentName\\\">\\r\\n                              <label [htmlFor]=\\\"feature.name\\\" class=\\\"form-label\\\">{{\\r\\n                                feature.displayName\\r\\n                              }}</label>\\r\\n                              <input\\r\\n                                class=\\\"form-control\\\"\\r\\n                                type=\\\"text\\\"\\r\\n                                [id]=\\\"feature.name\\\"\\r\\n                                [(ngModel)]=\\\"feature.value\\\"\\r\\n                                [abpFeatureManagementFreeText]=\\\"feature\\\"\\r\\n                              />\\r\\n\\r\\n                              <ng-container\\r\\n                                *ngTemplateOutlet=\\\"\\r\\n                                  descTmp;\\r\\n                                  context: { $implicit: feature.description }\\r\\n                                \\\"\\r\\n                              ></ng-container>\\r\\n                            </div>\\r\\n                          }\\r\\n                          @case (valueTypes.SelectionStringValueType) {\\r\\n                            @if (feature.valueType.itemSource?.items?.length) {\\r\\n                              <div class=\\\"mb-3 form-group\\\" [class.px-2]=\\\"!!feature.parentName\\\">\\r\\n                                <label [htmlFor]=\\\"feature.name\\\" class=\\\"form-label\\\">{{\\r\\n                                  feature.displayName\\r\\n                                }}</label>\\r\\n                                <select\\r\\n                                  class=\\\"form-select\\\"\\r\\n                                  [id]=\\\"feature.name\\\"\\r\\n                                  [(ngModel)]=\\\"feature.value\\\"\\r\\n                                >\\r\\n                                  @for (\\r\\n                                    item of feature.valueType.itemSource?.items;\\r\\n                                    track item.value\\r\\n                                  ) {\\r\\n                                    <option [ngValue]=\\\"item.value\\\">\\r\\n                                      {{\\r\\n                                        item.displayText?.resourceName +\\r\\n                                          '::' +\\r\\n                                          item.displayText?.name | abpLocalization\\r\\n                                      }}\\r\\n                                    </option>\\r\\n                                  }\\r\\n                                </select>\\r\\n                                <ng-container\\r\\n                                  *ngTemplateOutlet=\\\"\\r\\n                                    descTmp;\\r\\n                                    context: { $implicit: feature.description }\\r\\n                                  \\\"\\r\\n                                ></ng-container>\\r\\n                              </div>\\r\\n                            }\\r\\n                          }\\r\\n                          @default {\\r\\n                            {{ feature.displayName }}\\r\\n                          }\\r\\n                        }\\r\\n                      </div>\\r\\n                    }\\r\\n                  </ng-template>\\r\\n                </li>\\r\\n              }\\r\\n            </ul>\\r\\n          </div>\\r\\n\\r\\n          <ng-template #descTmp let-description>\\r\\n            @if (description) {\\r\\n              <small class=\\\"d-block form-text text-muted\\\">{{ description }}</small>\\r\\n            }\\r\\n          </ng-template>\\r\\n\\r\\n          <div class=\\\"col-md-8\\\"><div class=\\\"py-0\\\" [ngbNavOutlet]=\\\"nav\\\"></div></div>\\r\\n        }\\r\\n\\r\\n        @if (!groups.length) {\\r\\n          <div class=\\\"col\\\">\\r\\n            {{ 'AbpFeatureManagement::NoFeatureFoundMessage' | abpLocalization }}\\r\\n          </div>\\r\\n        }\\r\\n      </div>\\r\\n    </ng-template>\\r\\n\\r\\n    <ng-template #abpFooter>\\r\\n      <button abpClose type=\\\"button\\\" class=\\\"btn btn-link\\\">\\r\\n        {{ 'AbpFeatureManagement::Cancel' | abpLocalization }}\\r\\n      </button>\\r\\n\\r\\n      @if (groups.length) {\\r\\n        <abp-button\\r\\n          buttonClass=\\\"btn btn-outline-primary\\\"\\r\\n          [disabled]=\\\"modalBusy\\\"\\r\\n          (click)=\\\"resetToDefault()\\\"\\r\\n          aria-hidden=\\\"true\\\"\\r\\n        >\\r\\n          {{ 'AbpFeatureManagement::ResetToDefault' | abpLocalization }}\\r\\n        </abp-button>\\r\\n      }\\r\\n\\r\\n      @if (groups.length) {\\r\\n        <abp-button\\r\\n          iconClass=\\\"fa fa-check\\\"\\r\\n          [disabled]=\\\"modalBusy\\\"\\r\\n          (click)=\\\"save()\\\"\\r\\n          aria-hidden=\\\"true\\\"\\r\\n        >\\r\\n          {{ 'AbpFeatureManagement::Save' | abpLocalization }}\\r\\n        </abp-button>\\r\\n      }\\r\\n    </ng-template>\\r\\n  </abp-modal>\\r\\n}\\r\\n\"\n    }]\n  }], null, {\n    providerKey: [{\n      type: Input\n    }],\n    providerName: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output\n    }]\n  });\n})();\nfunction mapFeatures(features, dir) {\n  const margin = `margin-${dir === 'rtl' ? 'right' : 'left'}.px`;\n  return features.map(feature => {\n    const value = feature.valueType?.name === ValueTypes.ToggleStringValueType ? (feature.value || '').toLowerCase() === 'true' : feature.value;\n    return {\n      ...feature,\n      value,\n      initialValue: value,\n      style: {\n        [margin]: feature.depth * 20\n      }\n    };\n  });\n}\nclass FeatureManagementTabComponent {\n  constructor() {\n    this.visibleFeatures = false;\n    this.onVisibleFeaturesChange = value => {\n      this.visibleFeatures = value;\n    };\n  }\n  openFeaturesModal() {\n    this.visibleFeatures = true;\n  }\n  static {\n    this.ɵfac = function FeatureManagementTabComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FeatureManagementTabComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: FeatureManagementTabComponent,\n      selectors: [[\"abp-feature-management-tab\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 7,\n      consts: [[1, \"pt-2\", \"text-wrap\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"me-1\", \"fa\", \"fa-cog\"], [\"providerName\", \"T\", 3, \"visible\", \"providerKey\"], [\"providerName\", \"T\", 3, \"visible\", \"providerKey\", \"visibleChange\", 4, \"abpReplaceableTemplate\"], [\"providerName\", \"T\", 3, \"visibleChange\", \"visible\", \"providerKey\"]],\n      template: function FeatureManagementTabComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵpipe(2, \"abpLocalization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function FeatureManagementTabComponent_Template_button_click_3_listener() {\n            return ctx.openFeaturesModal();\n          });\n          i0.ɵɵelement(4, \"i\", 2);\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"abpLocalization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, FeatureManagementTabComponent_Conditional_7_Template, 1, 15, \"abp-feature-management\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"AbpFeatureManagement::ManageHostFeaturesText\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 5, \"AbpFeatureManagement::ManageHostFeatures\"), \"\\n\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.visibleFeatures ? 7 : -1);\n        }\n      },\n      dependencies: [ReplaceableTemplateDirective, LocalizationModule, i2.LocalizationPipe, FeatureManagementComponent],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FeatureManagementTabComponent, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      selector: 'abp-feature-management-tab',\n      imports: [ReplaceableTemplateDirective, LocalizationModule, FeatureManagementComponent],\n      template: \"<p class=\\\"pt-2 text-wrap\\\">{{ 'AbpFeatureManagement::ManageHostFeaturesText' | abpLocalization }}</p>\\r\\n\\r\\n<button class=\\\"btn btn-primary\\\" type=\\\"button\\\" (click)=\\\"openFeaturesModal()\\\">\\r\\n  <i class=\\\"me-1 fa fa-cog\\\" aria-hidden=\\\"true\\\"></i>\\r\\n  {{ 'AbpFeatureManagement::ManageHostFeatures' | abpLocalization }}\\r\\n</button>\\r\\n@if (visibleFeatures) {\\r\\n  <abp-feature-management\\r\\n    *abpReplaceableTemplate=\\\"{\\r\\n      inputs: {\\r\\n        providerName: { value: 'T' },\\r\\n        providerKey: { value: providerKey },\\r\\n        visible: { value: visibleFeatures, twoWay: true }\\r\\n      },\\r\\n      outputs: { visibleChange: onVisibleFeaturesChange },\\r\\n      componentKey: 'FeatureManagement.FeatureManagementComponent'\\r\\n    }\\\"\\r\\n    [(visible)]=\\\"visibleFeatures\\\"\\r\\n    providerName=\\\"T\\\"\\r\\n    [providerKey]=\\\"providerKey\\\"\\r\\n  >\\r\\n  </abp-feature-management>\\r\\n}\\r\\n\"\n    }]\n  }], null, null);\n})();\nconst FEATURE_MANAGEMENT_SETTINGS_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureSettingTabs,\n  deps: [SettingTabsService],\n  multi: true\n}];\nfunction configureSettingTabs(settingtabs) {\n  return () => {\n    settingtabs.add([{\n      name: \"AbpFeatureManagement::Permission:FeatureManagement\" /* eFeatureManagementTabNames.FeatureManagement */,\n      order: 100,\n      requiredPolicy: 'FeatureManagement.ManageHostFeatures',\n      component: FeatureManagementTabComponent\n    }]);\n  };\n}\nfunction provideFeatureManagementConfig() {\n  return makeEnvironmentProviders([FEATURE_MANAGEMENT_SETTINGS_PROVIDERS]);\n}\nconst exported = [FeatureManagementComponent, FreeTextInputDirective, FeatureManagementTabComponent];\n/**\n * @deprecated FeatureManagementModule is deprecated .\n * @description use `provideFeatureManagementConfig` *function* for config settings.\n * You can import directives and pipes directly whichs were belongs to FeatureManagementModule are switched to standalone.\n */\nclass FeatureManagementModule {\n  static forRoot() {\n    return {\n      ngModule: FeatureManagementModule,\n      providers: [provideFeatureManagementConfig()]\n    };\n  }\n  static {\n    this.ɵfac = function FeatureManagementModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FeatureManagementModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FeatureManagementModule,\n      imports: [CoreModule, ThemeSharedModule, NgbNavModule, FeatureManagementComponent, FreeTextInputDirective, FeatureManagementTabComponent],\n      exports: [FeatureManagementComponent, FreeTextInputDirective, FeatureManagementTabComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule, ThemeSharedModule, NgbNavModule, FeatureManagementComponent, FeatureManagementTabComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FeatureManagementModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CoreModule, ThemeSharedModule, NgbNavModule, ...exported],\n      exports: [...exported]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FEATURE_MANAGEMENT_SETTINGS_PROVIDERS, FeatureManagementComponent, FeatureManagementModule, FeatureManagementTabComponent, FreeTextInputDirective, INPUT_TYPES, configureSettingTabs, provideFeatureManagementConfig };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,IAAM,MAAM,OAAO;AAAA,EACjB,MAAM;AACR;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,kBAAqB,YAAY,GAAG,GAAG,gCAAgC,CAAC;AAAA,EAC7E;AACF;AACA,SAAS,8HAA8H,IAAI,KAAK;AAC9I,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,SAAS,EAAE;AAC9C,IAAG,iBAAiB,iBAAiB,SAAS,8IAA8I,QAAQ;AAClM,MAAG,cAAc,GAAG;AACpB,YAAM,aAAgB,cAAc,EAAE;AACtC,MAAG,mBAAmB,WAAW,OAAO,MAAM,MAAM,WAAW,QAAQ;AACvE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,WAAW,iBAAiB,SAAS,8IAA8I,QAAQ;AAC5L,MAAG,cAAc,GAAG;AACpB,YAAM,aAAgB,cAAc,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,UAAU,CAAC;AAAA,IAClE,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,+HAA+H,GAAG,GAAG,gBAAgB,EAAE;AACxK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,cAAc,CAAC;AAClB,UAAM,aAAgB,YAAY,CAAC;AACnC,IAAG,YAAY,QAAQ,CAAC,CAAC,WAAW,UAAU;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,WAAW,IAAI;AACnC,IAAG,iBAAiB,WAAW,WAAW,KAAK;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,WAAW,IAAI;AACxC,IAAG,UAAU;AACb,IAAG,kBAAkB,WAAW,WAAW;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,UAAU,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,WAAW,WAAW,CAAC;AAAA,EAC7H;AACF;AACA,SAAS,8HAA8H,IAAI,KAAK;AAC9I,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,SAAS,EAAE;AAC9C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,iBAAiB,iBAAiB,SAAS,8IAA8I,QAAQ;AAClM,MAAG,cAAc,GAAG;AACpB,YAAM,aAAgB,cAAc,EAAE;AACtC,MAAG,mBAAmB,WAAW,OAAO,MAAM,MAAM,WAAW,QAAQ;AACvE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,+HAA+H,GAAG,GAAG,gBAAgB,EAAE;AACxK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,cAAc,CAAC;AAClB,UAAM,aAAgB,YAAY,CAAC;AACnC,IAAG,YAAY,QAAQ,CAAC,CAAC,WAAW,UAAU;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,WAAW,IAAI;AACxC,IAAG,UAAU;AACb,IAAG,kBAAkB,WAAW,WAAW;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,WAAW,IAAI;AACnC,IAAG,iBAAiB,WAAW,WAAW,KAAK;AAC/C,IAAG,WAAW,gCAAgC,UAAU;AACxD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,UAAU,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,WAAW,WAAW,CAAC;AAAA,EAC7H;AACF;AACA,SAAS,mIAAmI,IAAI,KAAK;AACnJ,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,WAAW,SAAS,KAAK;AACvC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,SAAS,eAAe,OAAO,OAAO,SAAS,YAAY,gBAAgB,QAAQ,SAAS,eAAe,OAAO,OAAO,SAAS,YAAY,KAAK,GAAG,GAAG;AAAA,EAC5M;AACF;AACA,SAAS,4IAA4I,IAAI,KAAK;AAC5J,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,6HAA6H,IAAI,KAAK;AAC7I,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,SAAS,EAAE;AAC9C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,iBAAiB,iBAAiB,SAAS,6JAA6J,QAAQ;AACjN,MAAG,cAAc,GAAG;AACpB,YAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,MAAG,mBAAmB,WAAW,OAAO,MAAM,MAAM,WAAW,QAAQ;AACvE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,iBAAiB,GAAG,oIAAoI,GAAG,GAAG,UAAU,IAAI,UAAU;AACzL,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,6IAA6I,GAAG,GAAG,gBAAgB,EAAE;AACtL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,CAAC,EAAE;AACvC,IAAG,cAAc,CAAC;AAClB,UAAM,aAAgB,YAAY,CAAC;AACnC,IAAG,YAAY,QAAQ,CAAC,CAAC,WAAW,UAAU;AAC9C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,WAAW,IAAI;AACxC,IAAG,UAAU;AACb,IAAG,kBAAkB,WAAW,WAAW;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,MAAM,WAAW,IAAI;AACnC,IAAG,iBAAiB,WAAW,WAAW,KAAK;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,UAAU,cAAc,OAAO,OAAO,WAAW,UAAU,WAAW,KAAK;AACpG,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,UAAU,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,WAAW,WAAW,CAAC;AAAA,EAC7H;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8HAA8H,GAAG,IAAI,OAAO,EAAE;AAAA,EACjK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,eAAe,WAAW,UAAU,cAAc,OAAO,OAAO,WAAW,UAAU,WAAW,SAAS,OAAO,OAAO,WAAW,UAAU,WAAW,MAAM,UAAU,IAAI,EAAE;AAAA,EAClL;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAC/H,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,aAAgB,cAAc,EAAE;AACtC,IAAG,mBAAmB,KAAK,WAAW,aAAa,GAAG;AAAA,EACxD;AACF;AACA,SAAS,wGAAwG,IAAI,KAAK;AACxH,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,eAAe,SAAS,qIAAqI;AACzK,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,WAAW,GAAG,gHAAgH,GAAG,IAAI,OAAO,EAAE,EAAE,GAAG,gHAAgH,GAAG,IAAI,OAAO,EAAE,EAAE,GAAG,gHAAgH,GAAG,CAAC,EAAE,GAAG,gHAAgH,GAAG,CAAC;AACxgB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,aAAa,IAAI;AACvB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,WAAW,KAAK;AACzC,IAAG,UAAU;AACb,IAAG,eAAe,WAAW,WAAW,aAAa,OAAO,OAAO,WAAW,UAAU,UAAU,OAAO,WAAW,wBAAwB,IAAI,aAAa,OAAO,WAAW,0BAA0B,IAAI,aAAa,OAAO,WAAW,2BAA2B,IAAI,CAAC;AAAA,EAC9Q;AACF;AACA,SAAS,kGAAkG,IAAI,KAAK;AAClH,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,MAAM,EAAE;AACxB,IAAG,iBAAiB,GAAG,yGAAyG,GAAG,GAAG,OAAO,IAAI,UAAU;AAAA,EAC7J;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,EAAE;AACrC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,wBAAwB;AACpD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,SAAS,UAAU,IAAI,CAAC;AAAA,EAC/C;AACF;AACA,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE;AACzC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,mGAAmG,GAAG,GAAG,eAAe,EAAE;AAC3I,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,cAAc,UAAU,WAAW;AACjD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,UAAU,WAAW;AAAA,EAC5C;AACF;AACA,SAAS,0GAA0G,IAAI,KAAK;AAC1H,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kBAAqB,cAAc,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,kBAAkB,eAAe;AAAA,EACtC;AACF;AACA,SAAS,4FAA4F,IAAI,KAAK;AAC5G,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2GAA2G,GAAG,GAAG,SAAS,EAAE;AAAA,EAC/I;AACA,MAAI,KAAK,GAAG;AACV,UAAM,kBAAkB,IAAI;AAC5B,IAAG,cAAc,kBAAkB,IAAI,EAAE;AAAA,EAC3C;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC;AAC7C,IAAG,iBAAiB,kBAAkB,SAAS,2GAA2G,QAAQ;AAChK,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,MAAG,mBAAmB,OAAO,0BAA0B,MAAM,MAAM,OAAO,2BAA2B;AACrG,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,iBAAiB,GAAG,qFAAqF,GAAG,GAAG,MAAM,IAAI,UAAU;AACtI,IAAG,aAAa,EAAE;AAClB,IAAG,WAAW,GAAG,6FAA6F,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACrK,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,YAAY,CAAC;AAChC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,iBAAiB,YAAY,OAAO,wBAAwB;AAC/D,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,OAAO,MAAM;AAC3B,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,gBAAgB,OAAO;AAAA,EACvC;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,6CAA6C,GAAG,GAAG;AAAA,EACrG;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,+EAA+E,GAAG,CAAC,EAAE,GAAG,+EAA+E,GAAG,GAAG,OAAO,CAAC;AACtM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,OAAO,SAAS,IAAI,EAAE;AAC9C,IAAG,UAAU;AACb,IAAG,cAAc,CAAC,OAAO,OAAO,SAAS,IAAI,EAAE;AAAA,EACjD;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,cAAc,EAAE;AACrC,IAAG,WAAW,SAAS,SAAS,4GAA4G;AAC1I,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,SAAS;AAC1C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,sCAAsC,GAAG,GAAG;AAAA,EAC9F;AACF;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,cAAc,EAAE;AACrC,IAAG,WAAW,SAAS,SAAS,4GAA4G;AAC1I,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,KAAK,CAAC;AAAA,IACrC,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,SAAS;AAC1C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,4BAA4B,GAAG,GAAG;AAAA,EACpF;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,cAAc,EAAE,EAAE,GAAG,+EAA+E,GAAG,GAAG,cAAc,EAAE;AAAA,EAClO;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,8BAA8B,GAAG,GAAG;AACpF,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,OAAO,OAAO,SAAS,IAAI,EAAE;AAC9C,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,OAAO,SAAS,IAAI,EAAE;AAAA,EAChD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,aAAa,CAAC;AACnC,IAAG,iBAAiB,iBAAiB,SAAS,qFAAqF,QAAQ;AACzI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,MAAG,mBAAmB,OAAO,SAAS,MAAM,MAAM,OAAO,UAAU;AACnE,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,iEAAiE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,iEAAiE,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACnY,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,iBAAiB,WAAW,OAAO,OAAO;AAC7C,IAAG,WAAW,QAAQ,OAAO,SAAS,EAAE,WAAc,gBAAgB,GAAG,GAAG,CAAC;AAAA,EAC/E;AACF;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,MAAM,CAAC,IAAI,IAAI,QAAQ;AAAA,EAC3B,cAAc;AAAA,EACd,aAAa;AAAA,EACb,SAAS;AACX;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,eAAe;AACjB;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,cAAc;AAChB;AACA,SAAS,8EAA8E,IAAI,KAAK;AAC9F,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,0BAA0B,CAAC;AAChD,IAAG,iBAAiB,iBAAiB,SAAS,8HAA8H,QAAQ;AAClL,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,MAAG,mBAAmB,OAAO,iBAAiB,MAAM,MAAM,OAAO,kBAAkB;AACnF,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,iBAAiB,WAAW,OAAO,eAAe;AACrD,IAAG,WAAW,eAAe,OAAO,WAAW;AAAA,EACjD;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+EAA+E,GAAG,GAAG,0BAA0B,CAAC;AAAA,EACnI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA6B,gBAAgB,IAAI,KAAQ,gBAAgB,GAAG,KAAQ,gBAAgB,GAAG,GAAG,GAAM,gBAAgB,GAAG,KAAK,OAAO,WAAW,GAAM,gBAAgB,GAAG,KAAK,OAAO,eAAe,CAAC,GAAM,gBAAgB,IAAI,KAAK,OAAO,uBAAuB,CAAC,CAAC;AAAA,EAC9R;AACF;AACA,IAAM,cAAc;AAAA,EAClB,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA;AAAA,EAE3B,IAAI,QAAQ,KAAK;AACf,SAAK,WAAW;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,UAAM,gBAAgB,KAAK,SAAS,WAAW,WAAW,KAAK,YAAY;AAC3E,SAAK,OAAO,YAAY,aAAa,KAAK,YAAY;AAAA,EACxD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,gCAAgC,EAAE,CAAC;AAAA,MACzD,UAAU;AAAA,MACV,cAAc,SAAS,oCAAoC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,QAAQ,IAAI,IAAI;AAAA,QACpC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS,CAAC,GAAG,gCAAgC,SAAS;AAAA,MACxD;AAAA,MACA,UAAU,CAAC,mCAAmC;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAI;AAAA,CACH,SAAUA,aAAY;AACrB,EAAAA,YAAW,uBAAuB,IAAI;AACtC,EAAAA,YAAW,yBAAyB,IAAI;AACxC,EAAAA,YAAW,0BAA0B,IAAI;AAC3C,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,QAAQ,OAAO,cAAc;AAClC,SAAK,iBAAiB,OAAO,cAAc;AAC3C,SAAK,UAAU,OAAO,eAAe;AACrC,SAAK,cAAc,OAAO,kBAAkB;AAC5C,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,SAAS,CAAC;AACf,SAAK,aAAa;AAClB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,aAAa,OAAO;AAC3B;AAAA,IACF;AACA,SAAK,WAAW;AAChB,SAAK,cAAc,KAAK,KAAK;AAC7B,QAAI,OAAO;AACT,WAAK,UAAU;AACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,cAAc;AACtB,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,WAAW,EAAE,UAAU,SAAO;AACrE,UAAI,CAAC,IAAI,QAAQ,OAAQ;AACzB,WAAK,SAAS,IAAI,OAAO,IAAI,CAAC;AAAA,QAC5B;AAAA,QACA;AAAA,MACF,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF,EAAE;AACF,WAAK,2BAA2B,KAAK,OAAO,CAAC,EAAE;AAC/C,WAAK,WAAW,IAAI,OAAO,OAAO,CAAC,KAAK,QAAS,iCAC5C,MAD4C;AAAA,QAE/C,CAAC,IAAI,IAAI,GAAG,YAAY,IAAI,UAAU,SAAS,KAAK,GAAG;AAAA,MACzD,IAAI,CAAC,CAAC;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,QAAI,KAAK,UAAW;AACpB,UAAM,kBAAkB,CAAC;AACzB,WAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,SAAO;AACxC,WAAK,SAAS,GAAG,EAAE,QAAQ,aAAW;AACpC,YAAI,QAAQ,UAAU,QAAQ,aAAc,iBAAgB,KAAK;AAAA,UAC/D,MAAM,QAAQ;AAAA,UACd,OAAO,GAAG,QAAQ,KAAK;AAAA,QACzB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,QAAI,CAAC,gBAAgB,QAAQ;AAC3B,WAAK,UAAU;AACf;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,QAAQ,OAAO,KAAK,cAAc,KAAK,aAAa;AAAA,MACvD,UAAU;AAAA,IACZ,CAAC,EAAE,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,EAAE,UAAU,MAAM;AAC9D,WAAK,UAAU;AACf,WAAK,eAAe,QAAQ,0BAA0B;AACtD,UAAI,CAAC,KAAK,aAAa;AAErB,aAAK,YAAY,gBAAgB,EAAE,UAAU;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,SAAK,oBAAoB,KAAK,oDAAoD,kCAAkC,EAAE,UAAU,YAAU;AACxI,UAAI,WAAW,aAAa,OAAO,SAAS;AAC1C,aAAK,QAAQ,OAAO,KAAK,cAAc,KAAK,WAAW,EAAE,UAAU,MAAM;AACvE,eAAK,eAAe,QAAQ,wCAAwC;AACpE,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,aAAa;AAErB,iBAAK,YAAY,gBAAgB,EAAE,UAAU;AAAA,UAC/C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,KAAK,SAAS;AAC5B,QAAI,KAAK;AACP,WAAK,qBAAqB,OAAO;AAAA,IACnC,OAAO;AACL,WAAK,yBAAyB,OAAO;AAAA,IACvC;AAAA,EACF;AAAA,EACA,yBAAyB,SAAS;AAChC,SAAK,2BAA2B,SAAS,WAAW,qBAAqB,EAAE,QAAQ,UAAQ,KAAK,gBAAgB,MAAM,KAAK,CAAC;AAAA,EAC9H;AAAA,EACA,qBAAqB,SAAS;AAC5B,SAAK,yBAAyB,SAAS,WAAW,qBAAqB,EAAE,QAAQ,UAAQ,KAAK,gBAAgB,MAAM,IAAI,CAAC;AAAA,EAC3H;AAAA,EACA,yBAAyB,SAAS,MAAM;AACtC,QAAI,SAAS,KAAK,iBAAiB,SAAS,IAAI;AAChD,UAAM,YAAY,CAAC;AACnB,WAAO,QAAQ;AACb,gBAAU,KAAK,MAAM;AACrB,eAAS,KAAK,iBAAiB,QAAQ,IAAI;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,SAAS,MAAM;AACxC,UAAM,cAAc,CAAC;AACrB,UAAM,QAAQ,CAAC,OAAO;AACtB,WAAO,MAAM,QAAQ;AACnB,YAAM,OAAO,MAAM,IAAI;AACvB,YAAM,iBAAiB,KAAK,mBAAmB,MAAM,IAAI;AACzD,kBAAY,KAAK,GAAG,cAAc;AAClC,YAAM,KAAK,GAAG,cAAc;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,SAAS,MAAM;AAC9B,WAAO,KAAK,gBAAgB,EAAE,KAAK,OAAK,EAAE,UAAU,SAAS,QAAQ,EAAE,SAAS,QAAQ,UAAU;AAAA,EACpG;AAAA,EACA,mBAAmB,SAAS,MAAM;AAChC,WAAO,KAAK,gBAAgB,EAAE,OAAO,OAAK,EAAE,UAAU,SAAS,QAAQ,EAAE,eAAe,QAAQ,IAAI;AAAA,EACtG;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,SAAS,KAAK,wBAAwB,KAAK,CAAC;AAAA,EAC1D;AAAA,EACA,gBAAgB,SAAS,KAAK;AAC5B,YAAQ,QAAQ;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,MACtC,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,sBAAsB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,OAAO,QAAQ,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,WAAW,QAAQ,SAAS,GAAG,CAAC,GAAG,iBAAiB,WAAW,QAAQ,SAAS,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,UAAU,IAAI,eAAe,YAAY,GAAG,aAAa,GAAG,kBAAkB,UAAU,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,cAAc,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,GAAG,QAAQ,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,eAAe,SAAS,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,cAAc,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,QAAQ,YAAY,GAAG,oBAAoB,GAAG,iBAAiB,MAAM,SAAS,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,QAAQ,YAAY,GAAG,CAAC,GAAG,cAAc,GAAG,SAAS,GAAG,CAAC,QAAQ,QAAQ,GAAG,gBAAgB,GAAG,iBAAiB,MAAM,WAAW,8BAA8B,GAAG,CAAC,GAAG,eAAe,GAAG,iBAAiB,MAAM,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,WAAW,aAAa,YAAY,GAAG,CAAC,YAAY,IAAI,QAAQ,UAAU,GAAG,OAAO,UAAU,GAAG,CAAC,eAAe,2BAA2B,eAAe,QAAQ,GAAG,UAAU,GAAG,CAAC,aAAa,eAAe,eAAe,QAAQ,GAAG,UAAU,GAAG,CAAC,eAAe,2BAA2B,eAAe,QAAQ,GAAG,SAAS,UAAU,GAAG,CAAC,aAAa,eAAe,eAAe,QAAQ,GAAG,SAAS,UAAU,CAAC;AAAA,MAC75C,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,aAAa,CAAC;AAAA,QAC1F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,IAAI,UAAU,IAAI,EAAE;AAAA,QACvC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,mBAAsB,iBAAoB,gBAAmB,qBAAqB,oBAAuB,kBAAkB,aAAgB,gBAAmB,yBAA4B,sBAAyB,8BAAiC,4BAA+B,iBAAoB,SAAS,cAAiB,eAAkB,QAAW,YAAe,gBAAmB,YAAe,gBAAmB,cAAc,wBAAwB,gBAAgB;AAAA,MACve,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC,mBAAmB,oBAAoB,aAAa,cAAc,wBAAwB,gBAAgB;AAAA,MACpH,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,YAAY,UAAU,KAAK;AAClC,QAAM,SAAS,UAAU,QAAQ,QAAQ,UAAU,MAAM;AACzD,SAAO,SAAS,IAAI,aAAW;AAC7B,UAAM,QAAQ,QAAQ,WAAW,SAAS,WAAW,yBAAyB,QAAQ,SAAS,IAAI,YAAY,MAAM,SAAS,QAAQ;AACtI,WAAO,iCACF,UADE;AAAA,MAEL;AAAA,MACA,cAAc;AAAA,MACd,OAAO;AAAA,QACL,CAAC,MAAM,GAAG,QAAQ,QAAQ;AAAA,MAC5B;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,kBAAkB;AACvB,SAAK,0BAA0B,WAAS;AACtC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,MAC1C,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,QAAQ,WAAW,GAAG,CAAC,QAAQ,UAAU,GAAG,OAAO,eAAe,GAAG,OAAO,GAAG,CAAC,eAAe,QAAQ,GAAG,QAAQ,MAAM,QAAQ,GAAG,CAAC,gBAAgB,KAAK,GAAG,WAAW,aAAa,GAAG,CAAC,gBAAgB,KAAK,GAAG,WAAW,eAAe,iBAAiB,GAAG,wBAAwB,GAAG,CAAC,gBAAgB,KAAK,GAAG,iBAAiB,WAAW,aAAa,CAAC;AAAA,MACzW,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,iBAAiB;AAC9B,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,WAAW,SAAS,SAAS,iEAAiE;AAC/F,mBAAO,IAAI,kBAAkB;AAAA,UAC/B,CAAC;AACD,UAAG,UAAU,GAAG,KAAK,CAAC;AACtB,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,iBAAiB;AAC9B,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,sDAAsD,GAAG,IAAI,0BAA0B,CAAC;AAAA,QAC3G;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,kBAAqB,YAAY,GAAG,GAAG,8CAA8C,CAAC;AACzF,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,0CAA0C,GAAG,IAAI;AACjG,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,kBAAkB,IAAI,EAAE;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,8BAA8B,oBAAuB,kBAAkB,0BAA0B;AAAA,MAChH,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,SAAS,CAAC,8BAA8B,oBAAoB,0BAA0B;AAAA,MACtF,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wCAAwC,CAAC;AAAA,EAC7C,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,kBAAkB;AAAA,EACzB,OAAO;AACT,CAAC;AACD,SAAS,qBAAqB,aAAa;AACzC,SAAO,MAAM;AACX,gBAAY,IAAI,CAAC;AAAA,MACf,MAAM;AAAA,MACN,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,iCAAiC;AACxC,SAAO,yBAAyB,CAAC,qCAAqC,CAAC;AACzE;AACA,IAAM,WAAW,CAAC,4BAA4B,wBAAwB,6BAA6B;AAMnG,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,+BAA+B,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY,mBAAmB,cAAc,4BAA4B,wBAAwB,6BAA6B;AAAA,MACxI,SAAS,CAAC,4BAA4B,wBAAwB,6BAA6B;AAAA,IAC7F,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY,mBAAmB,cAAc,4BAA4B,6BAA6B;AAAA,IAClH,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,mBAAmB,cAAc,GAAG,QAAQ;AAAA,MAClE,SAAS,CAAC,GAAG,QAAQ;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ValueTypes"]}