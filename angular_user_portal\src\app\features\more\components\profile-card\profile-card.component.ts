import { AuthService, SessionStateService } from '@abp/ng.core';
import { AsyncPipe, NgClass } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { ProfileImageComponent } from '@shared/components/profile-image/profile-image.component';
import { logout } from '@shared/functions/logout';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-profile-card',
  standalone: true,
  templateUrl: './profile-card.component.html',
  imports: [MatIconModule, NgClass, ProfileImageComponent, LanguagePipe, RouterLink, AsyncPipe],
})
export class ProfileCardComponent {
  appInfoIcon = '/assets/images/svg/app-info.svg';
  phoneIcon = '/assets/images/svg/phone.svg';
  languageIcon = '/assets/images/svg/language.svg';
  logoutIcon = '/assets/images/svg/logout.svg';

  private auth = inject(AuthService);
  session = inject(SessionStateService);

  switchLanguage(lang: string) {
    this.session.setLanguage(lang);
  }

  logout() {
    logout();
  }
}
