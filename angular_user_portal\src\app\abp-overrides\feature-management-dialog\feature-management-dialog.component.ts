import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { FeatureDto, FeaturesService } from '@abp/ng.feature-management/proxy';
import { NgComponentOutlet } from '@angular/common';
import { Component, computed, DestroyRef, inject, OnDestroy, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatCheckbox } from '@angular/material/checkbox';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle
} from '@angular/material/dialog';
import { MatDivider } from '@angular/material/divider';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatListItem, MatNavList } from '@angular/material/list';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatOption, MatSelect } from '@angular/material/select';
import { AlertService, LanguagePipe, LOADING } from '@ttwr-framework/ngx-main-visuals';
import { map, tap } from 'rxjs';
import { EXTRA_FEATURES } from './extra-features.token';

@Component({
  selector: 'app-feature-management-dialog',
  standalone: true,
  imports: [
    LocalizationModule,
    MatDialogContent,
    MatDialogTitle,
    MatProgressSpinner,
    MatNavList,
    MatListItem,
    MatCheckbox,
    MatFormField,
    MatLabel,
    MatSelect,
    MatOption,
    MatInput,
    MatDivider,
    ReactiveFormsModule,
    LanguagePipe,
    MatButton,
    MatDialogActions,
    MatDialogClose,
    MatIcon,
    NgComponentOutlet,
  ],
  templateUrl: './feature-management-dialog.component.html',
  styleUrl: './feature-management-dialog.component.scss',
})
export class FeatureManagementDialogComponent implements OnDestroy {
  private features = inject(FeaturesService);
  private providerKey = inject<string | undefined>(MAT_DIALOG_DATA);
  private fb = inject(NonNullableFormBuilder);
  private loading = inject(LOADING);
  private alert = inject(AlertService);
  private localization = inject(LocalizationService);
  private ref = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  protected extraFeatures = inject(EXTRA_FEATURES);

  private featuresData = signal<FeatureWithTypeAndControl[]>([]);
  protected activeGroup = signal<string | null>(null);
  protected featuresGroups = toSignal(
    this.features.get('T', this.providerKey as any).pipe(
      map(res => res.groups),
      map(groups =>
        groups.map(group => ({
          ...group,
          features: group.features.map(feature => {
            const featureType: FeatureType =
              feature.valueType.name === 'ToggleStringValueType'
                ? 'boolean'
                : feature.valueType.name === 'SelectionStringValueType'
                ? 'select'
                : feature.valueType.name === 'FreeTextStringValueType' &&
                  feature.valueType.validator.name === 'NUMERIC'
                ? 'number'
                : feature.valueType.name === 'FreeTextStringValueType'
                ? 'text'
                : 'custom';

            const value = !feature.value
              ? feature.value
              : featureType === 'boolean'
              ? feature.value.toLowerCase() === 'true'
              : featureType === 'number'
              ? Number(feature.value)
              : feature.value;

            return {
              ...feature,
              control: this.fb.control(value),
              featureType,
            };
          }),
        }))
      ),
      tap(groups => {
        this.activeGroup.set(groups[0]?.name ?? null);
        this.featuresData.set(
          groups.map(g => g.features).reduce((pre, curr) => [...pre, ...curr], [])
        );
      })
    )
  );
  protected featuresMap = computed(() => {
    const groups = this.featuresGroups();
    const map = new Map<string, FeatureWithTypeAndControl[]>();

    groups?.forEach(group => {
      map.set(group.name!, group.features as any);
    });

    return map;
  });
  protected activeGroupDisplayName = computed(
    () => this.featuresGroups()?.find(g => g.name === this.activeGroup())?.displayName
  );

  save() {
    if (this.loading()) return;

    this.loading.set(true);
    this.features
      .update('T', this.providerKey as any, {
        features: this.featuresData().map(feature => ({
          name: feature.name,
          value: String(feature.control.value),
        })),
      })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        error: err => {
          this.loading.set(false);

          const message = this.localization.instant(err.error?.error?.message);
          if (message) {
            this.alert.error(message);
          }
        },
        next: () => {
          this.ref.close();
        },
      });
  }

  ngOnDestroy() {
    this.loading.set(false);
  }
}

type FeatureType = 'boolean' | 'select' | 'number' | 'text' | 'custom';

export interface FeatureWithTypeAndControl extends FeatureDto {
  featureType: FeatureType;
  control: FormControl<any>;
}
