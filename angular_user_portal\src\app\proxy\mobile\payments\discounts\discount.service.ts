import type { SubscriptionDurationDiscountDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DiscountService {
  apiName = 'Default';
  

  getSubscriptionDurationDiscounts = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, SubscriptionDurationDiscountDto[]>({
      method: 'GET',
      url: '/api/app/discount/subscriptionDurationDiscounts',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
