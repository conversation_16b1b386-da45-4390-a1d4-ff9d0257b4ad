mat-sidenav-container {
  height: 100dvh;
  padding-top: var(--mat-toolbar-standard-height);
}

::ng-deep {
  .mat-mdc-menu-panel {
    max-width: none !important;
  }
  .mat-mdc-menu-content {
    padding: 0px !important;
  }
  .mat-mdc-menu-item-text {
    font-weight: 400 !important;
  }
}
mat-toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;

  .logo {
    background-image: var(--lpx-logo-icon-white);
    background-repeat: no-repeat;
    background-size: auto 32px;
    background-position: left 7px;
    display: block;
    height: 46px;
    width: 46px;
  }

  h1 {
    margin: 0 1rem;
  }

  > * {
    color: inherit;
  }
}

main {
  // margin: 0 1.5rem 1.5rem;

  height: 100%;
  app-breadcrumb {
    display: block;
    margin: 1rem 0 1.5rem;
  }
}
