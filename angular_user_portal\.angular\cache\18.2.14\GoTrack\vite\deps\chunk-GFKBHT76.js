import {
  RestService
} from "./chunk-3CXWNYMM.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-QGPYGS5J.js";
import {
  __spreadValues
} from "./chunk-ZTELYOIP.js";

// node_modules/@abp/ng.setting-management/fesm2022/abp-ng.setting-management-proxy.mjs
var index$1 = Object.freeze({
  __proto__: null
});
var index = Object.freeze({
  __proto__: null,
  Abp: index$1
});
var EmailSettingsService = class _EmailSettingsService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "SettingManagement";
    this.get = (config) => this.restService.request({
      method: "GET",
      url: "/api/setting-management/emailing"
    }, __spreadValues({
      apiName: this.apiName
    }, config));
    this.sendTestEmail = (input, config) => this.restService.request({
      method: "POST",
      url: "/api/setting-management/emailing/send-test-email",
      body: input
    }, __spreadValues({
      apiName: this.apiName
    }, config));
    this.update = (input, config) => this.restService.request({
      method: "POST",
      url: "/api/setting-management/emailing",
      body: input
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
  static {
    this.ɵfac = function EmailSettingsService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _EmailSettingsService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _EmailSettingsService,
      factory: _EmailSettingsService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(EmailSettingsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();
var TimeZoneSettingsService = class _TimeZoneSettingsService {
  constructor(restService) {
    this.restService = restService;
    this.apiName = "SettingManagement";
    this.get = (config) => this.restService.request({
      method: "GET",
      responseType: "text",
      url: "/api/setting-management/timezone"
    }, __spreadValues({
      apiName: this.apiName
    }, config));
    this.getTimezones = (config) => this.restService.request({
      method: "GET",
      url: "/api/setting-management/timezone/timezones"
    }, __spreadValues({
      apiName: this.apiName
    }, config));
    this.update = (timezone, config) => this.restService.request({
      method: "POST",
      url: "/api/setting-management/timezone",
      params: {
        timezone
      }
    }, __spreadValues({
      apiName: this.apiName
    }, config));
  }
  static {
    this.ɵfac = function TimeZoneSettingsService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _TimeZoneSettingsService)(ɵɵinject(RestService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _TimeZoneSettingsService,
      factory: _TimeZoneSettingsService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TimeZoneSettingsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RestService
  }], null);
})();

export {
  index,
  EmailSettingsService,
  TimeZoneSettingsService
};
//# sourceMappingURL=chunk-GFKBHT76.js.map
