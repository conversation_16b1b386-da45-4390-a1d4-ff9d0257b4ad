import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { CreateIncreaseUserCountRequestDto, IncreaseUserCountRequestDto } from '../../increase-user-count-requests/dtos/models';
import type { CreateAccountRequestPaymentDto } from '../models';
import type { BillDto } from '../../../payments/bills/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class IncreaseUserCountRequestService {
  apiName = 'Default';
  

  create = (createIncreaseUserCountRequestDto: CreateIncreaseUserCountRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/increaseUserCountRequest',
      body: createIncreaseUserCountRequestDto,
    },
    { apiName: this.apiName,...config });
  

  createPayment = (input: CreateAccountRequestPaymentDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/increaseUserCountRequest/payment',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  createTempBill = (input: CreateIncreaseUserCountRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BillDto>({
      method: 'POST',
      url: '/api/app/increaseUserCountRequest/tempBill',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, IncreaseUserCountRequestDto>({
      method: 'GET',
      url: `/api/app/increaseUserCountRequest/${id}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
