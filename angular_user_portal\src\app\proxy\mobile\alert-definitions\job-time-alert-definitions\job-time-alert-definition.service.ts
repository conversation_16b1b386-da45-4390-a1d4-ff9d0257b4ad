import type { CreateJobTimeAlertDefinitionDto, JobTimeAlertDefinitionDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class JobTimeAlertDefinitionService {
  apiName = 'Default';
  

  create = (input: CreateJobTimeAlertDefinitionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/jobTimeAlertDefinition',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, JobTimeAlertDefinitionDto>({
      method: 'GET',
      url: `/api/app/jobTimeAlertDefinition/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<JobTimeAlertDefinitionDto>>({
      method: 'GET',
      url: '/api/app/jobTimeAlertDefinition',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
