<div class="relative">
  <mat-icon class="absolute top-4 left-4" [routerLink]="['/track-accounts']"
    >keyboard_arrow_left</mat-icon
  >
  <h2 class="text-center bg-white rounded-xl shadow-lg p-4 text-black">
    {{ 'renew subscription' | i18n }}
  </h2>
</div>
<div class="flex flex-col items-center min-h-screen mt-4">
  <mat-card
    class="p-2 md:p-6 rounded-lg shadow-lg transition-all duration-500 card-blue w-10/12 max-w-md"
  >
    <form [formGroup]="form" class="p-4" (ngSubmit)="onSubmit()">
      <div>

        <div class="">
          <mat-label>
            {{ 'UserPortal:subscriptionPlan' | i18n }}
          </mat-label>
          <mat-form-field class="w-full">
            <mat-select formControlName="subscriptionPlan">
              @for (item of plans(); track $index) {
              <mat-option [value]="item.key">{{ item.localizedName }}</mat-option>
              }
            </mat-select>
            <mat-error>
              <app-validation [errors]="form.controls.subscriptionPlan" />
            </mat-error>
          </mat-form-field>
        </div>

        <div class="flex justify-end my-2">
          <a class="text-main_blue hover:text-main_perple" (click)="goToPlans()"   
          >{{
            'UserPortal:viewPlans' | i18n
          }}</a>
        </div>

        <div class="">
          <mat-label>
            {{ 'UserPortal:subscriptionDurationInMonths' | i18n }}
          </mat-label>
          <div class="flex gap-2">
            <mat-form-field class="w-full">
              <mat-select formControlName="subscriptionDurationInMonths">
                @for (item of [1,2,3,4,5,6,7,8,9,10,11,12]; track $index) {
                <mat-option [value]="item">
                  <span>{{ item }} {{ 'month' | i18n }}</span>
                  @if (discounts$().get(item)) {
                  <span class="text-main_blood_red">
                    {{ 'UserPortal:discount' | i18n }}( {{ discounts$().get(item).isPercentage
                      ? discounts$().get(item).value * 100
                      : discounts$().get(item).value }}
                    {{ discounts$().get(item).isPercentage ? '%' : ('SP' | i18n) }})
                  </span>
                  }
                </mat-option>
                }
              </mat-select>
              <mat-error>
                <app-validation [errors]="form.controls.subscriptionDurationInMonths" />
              </mat-error>
            </mat-form-field>
            <button (click)="getDiscounts()" mat-mini-fab>
              <mat-icon>autorenew</mat-icon>
            </button>
          </div>
        </div>

        @if (form.controls.subscriptionPlan.value!=null) {
        <div class="">
          <mat-label>
            {{ 'UserPortal:userCount' | i18n }} {{ 'you have' | i18n }} ({{
              subscription().userCount
            }}
            {{ 'observers' | i18n }})
          </mat-label>
          <mat-form-field class="w-full">
            <input type="number" matInput [value]="form.controls.userCount.value" [readonly]="true" />
          </mat-form-field>
        </div>
        @if (form.controls.userCount.value<subscription().userCount) {
        <div class="mb-2 text-main_blood_red text-center">
          {{ 'UserPortal:decreaseUsers' | i18n }}
        </div>
        } @if (form.controls.removeUsers.value.length>0) {

        <div class="mb-2 text-main_gold text-center">
          {{ 'UserPortal:removedUsers' | i18n }} ({{ form.controls.removeUsers.value.length }})
        </div>
        }
        <div class="mb-2 text-center">
          <a class="font-semibold text-main_blue cursor-pointer" (click)="openObserversList()">
            {{ 'UserPortal:SelectObserversToRemove' | i18n }}</a
          >
        </div>
        }

        <div class="mb-4">
          <mat-label>
            {{ 'UserPortal:trackAccountSms' | i18n }}
          </mat-label>
          <mat-radio-group
            [ngModel]="withSms()"
            [ngModelOptions]="{ standalone: true }"
            (ngModelChange)="withSms.set($event)"
            class="flex gap-4"
          >
            <mat-radio-button [value]="false">
              {{ 'UserPortal:No' | i18n }}
            </mat-radio-button>
            <mat-radio-button [value]="true">
              {{ 'UserPortal:Yes' | i18n }}
            </mat-radio-button>
          </mat-radio-group>
        </div>

        @if (withSms()) {
        <div class="">
          <mat-label>
            {{ 'UserPortal:smsBundleId' | i18n }}
          </mat-label>
          <mat-form-field class="w-full">
            <mat-select formControlName="smsBundleId" [required]="withSms()">
              @for (item of smsBundles(); track $index) {
              <mat-option [value]="item.id">
                {{ item.name }} ( {{ item.messagesCount }} {{ 'message' | i18n }} ) {{ item.price }}
                {{ 'SP' | i18n }}
              </mat-option>
              }
            </mat-select>
            <mat-error>
              <app-validation [errors]="form.controls.smsBundleId" />
            </mat-error>
          </mat-form-field>
        </div>
        }
        <div class="mb-4">
          <mat-label>
            {{ 'UserPortal:promoCode' | i18n }}
          </mat-label>
          <mat-form-field class="w-full">
            <input type="text" [placeholder]="'UserPortal:promoCode' | i18n" matInput formControlName="promoCode" />
          </mat-form-field>
        </div>

        <hr />
        <div class="mb-4">
          <div class="flex items-center">
            <button
              class="p-2 px-4 text-white rounded-lg bg-main_perple me-4"
              (click)="addVehiclesDialog()"
            >
              +
            </button>
            <div class="text-main_dark_blue">{{ 'UserPortal:add new vihicle' | i18n }}</div>
          </div>
        </div>
        <hr />
        <div class="mb-4">
          @for (item of form.controls.newVehicles.value; track $index) {
          <div
            class="p-4 rounded-lg border-2 shadow-xl text-main_gray bg-white border-main_gray border-opacity-35"
          >
            <div class="flex justify-between items-center">
              <div class="text-main_dark_blue">
                {{ 'UserPortal:Vehicle' | i18n }} {{ $index + 1 }}
              </div>
              <mat-icon [matMenuTriggerFor]="menu">more_vert</mat-icon>
              <mat-menu #menu>
                <button mat-menu-item (click)="remove($index)">
                  <mat-icon>delete</mat-icon>
                  <span>{{ 'delete' | i18n }}</span>
                </button>
              </mat-menu>
            </div>
            <div class="my-2">
              <span class="font-medium">{{ 'UserPortal:licensePlateSubClass' | i18n }}:</span>
              <span class="text-main_red ml-2">{{ item.licensePlateSubClass }}</span>
            </div>
            <div class="my-2">
              <span class="font-medium">{{ 'UserPortal:licensePlateSerial' | i18n }}:</span>
              <span class="text-main_red ml-2">{{ item.licensePlateSerial }}</span>
            </div>
            <div class="my-2">
              <span class="font-medium">{{ 'UserPortal:consumptionRate' | i18n }}:</span>
              <span class="text-main_red ml-2">
                {{ item.consumptionRate }}{{ 'UserPortal:Km' | i18n }}/{{ totalCapacity
                }}{{ 'UserPortal:L' | i18n }}
              </span>
            </div>
            <div class="flex items-center my-2">
              <span class="font-medium">{{ 'UserPortal:color' | i18n }}:</span>
              <div
                class="inline-block mx-2 rounded-full size-6"
                [ngStyle]="{ background: item.color }"
              ></div>
            </div>
          </div>
          }
        </div>
      </div>

      <div class="flex justify-center mt-6">
        <button mat-button mat-flat-button type="submit" [disabled]="!form.valid">
          {{ 'UserPortal:confirm' | i18n }}
        </button>
      </div>
    </form>
  </mat-card>
</div>
