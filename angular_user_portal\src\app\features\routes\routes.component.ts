import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';

import { DynamicTabsComponent } from '../../shared/components/dynamic-tabs/dynamic-tabs.component';
import { PreDefinedRoutesComponent } from './components/pre-defined-routes/pre-defined-routes.component';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'app-routes',
  standalone: true,
  templateUrl: `./routes.component.html`,
  imports: [MatTabsModule, MatIconModule, DynamicTabsComponent, LanguagePipe],
})
export class RoutesComponent {
  description: string = 'UserPortal:routesBanner';
  tabs = [
    {
      label: 'UserPortal:lines',
      iconActive: 'assets/images/svg/lines.svg',
      iconInactive: 'assets/images/svg/lines.svg',
      component: PreDefinedRoutesComponent,
    },
  ];
}
