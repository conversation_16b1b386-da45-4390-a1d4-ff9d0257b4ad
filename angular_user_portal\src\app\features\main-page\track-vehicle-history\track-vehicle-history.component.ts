import { isNumber, LocalizationModule } from '@abp/ng.core';
import { DatePipe, KeyValuePipe } from '@angular/common';
import { Component, computed, inject, input, signal, WritableSignal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatFormField, MatSelectModule } from '@angular/material/select';
import { RouterLink } from '@angular/router';
import { MonitoringService } from '@proxy/mobile/monitoring';
import { WarpGtsHistoryDto } from '@proxy/mobile/monitoring/dtos/device-history';
import { LiveLocationDto } from '@proxy/mobile/monitoring/dtos/live-locations';
import { VehicleReportService } from '@proxy/mobile/reports';
import { VehicleService } from '@proxy/mobile/vehicles';
import { VehicleDto } from '@proxy/mobile/vehicles/dtos';
import { car_svg } from '@shared';
import { openDateRangeDialog } from '@shared/components/date-range-dialog/date-range-dialog.component';
import { CustomLine, CustomMarker, MapComponent } from '@shared/components/map/map.component';
import { hexToColor } from '@shared/functions/hex-to-color';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { latLng, LatLngTuple, Layer, MapOptions } from 'leaflet';
import { combineLatest, interval, map, of, startWith, switchMap, tap } from 'rxjs';

@Component({
  selector: 'app-track-vehicle-history',
  templateUrl: './track-vehicle-history.component.html',
  standalone: true,
  imports: [
    LocalizationModule,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatButtonModule,
    MatIcon,
    RouterLink,
    MatSelectModule,
    MatFormField,
    KeyValuePipe,
    MapComponent,
  ],
  styles: `
  :host {
  --mdc-outlined-text-field-container-shape: 10px;
  --mat-form-field-container-vertical-padding: 10px;
  --mat-form-field-container-height: 44px;
  --mdc-filled-button-container-shape: 10px;
  --mdc-filled-button-container-color: #7164e4;
  }
  `,
})
export class TrackVehicleHistoryComponent {
  datePipe = inject(DatePipe);
  dialog = inject(MatDialog);

  times = {
    hour: 60 * 60 * 1000,
    day: 24 * 60 * 60 * 1000,
  };
  selectedTime = new FormControl<number>(this.times.hour);

  selectedTime$ = toSignal(this.selectedTime.valueChanges);

  from = computed(() => {
    if (!isNumber(this.selectedTime$())) {
      return '';
    }
    if (this.selectedTime$() > 60 * 60 * 1000) {
      return this.datePipe.transform(
        new Date(+new Date() - this.selectedTime.value).toISOString(),
        'yyyy-MM-dd'
      );
    }
    return this.datePipe.transform(
      new Date(+new Date() - this.selectedTime.value).toISOString(),
      'HH:mm a  dd/MM '
    );
  });
  to = computed(() => {
    if (!isNumber(this.selectedTime$())) {
      return '';
    }
    if (this.selectedTime$() > 60 * 60 * 1000) {
      return this.datePipe.transform(new Date().toISOString(), 'yyyy-MM-dd');
    }
    return this.datePipe.transform(new Date().toISOString(), 'HH:mm a  dd/MM ');
  });

  destance = signal<string>('0 Km');
  info = [
    {
      icon: 'calendar_today',
      name: 'UserPortal:From',
      key: this.from,
    },
    { icon: 'directions_car', name: 'UserPortal:distance', key: this.destance },
    {
      icon: 'calendar_today',
      name: 'UserPortal:To',
      key: this.to,
    },
  ];

  vehicleService = inject(VehicleService);
  monitoringService = inject(MonitoringService);
  vehicleReportService = inject(VehicleReportService);

  selectedVehcileHistory$: WritableSignal<WarpGtsHistoryDto | any> = signal({});

  id = input<string | null>(null);

  interval$ = interval(5000).pipe(startWith(null));

  vehicle$ = signal<VehicleDto | any>({});

  node$ = signal<Layer[]>([]);
  line$ = signal<Layer[]>([]);

  options$ = signal<MapOptions>({});

  monitorInfo$;

  constructor() {
    this.monitorInfo$ = combineLatest([
      toObservable(this.id),
      this.selectedTime.valueChanges.pipe(startWith(this.times.hour)),
    ]).pipe(
      switchMap(val => {
        if ((val[0], val[1]))
          return combineLatest([
            this.report(val[0], new Date(+new Date() - val[1]), new Date()).pipe(
              tap(val => {
                this.destance.set(val.distance + 'KM');
              })
            ),
            this.monitoringService
              .getVehicleHistory({
                onlySpeed: true,
                vehicleId: val[0],
                fromDate: new Date(+new Date() - val[1]).toISOString(),
                toDate: new Date().toISOString(),
              })
              .pipe(
                map(history => {
                  if (history && history.length > 0) {
                    const nodes: any[] = history[0].values.map(v => {
                      return [+v.latitude, +v.longitude];
                    });
                    this.line$.set([CustomLine({ line: nodes, color: '#ff0000' })]);
                    this.options$.set({ zoom: 12, center: latLng(nodes[0]) });
                    this.selectedVehcileHistory$.set(history);
                  }
                })
              ),
          ]);
        else {
          return of(null);
        }
      })
    );
  }
  async ngOnInit() {
    await this.vehicleService.get(this.id()).subscribe(res => {
      res.colorHex = hexToColor(res.colorHex);
      this.vehicle$.set(res);
    });
    this.monitorInfo$.subscribe();
  }

  async addMarker(latitude: number, longitude: number, info: LiveLocationDto) {
    this.node$.update(val => {
      return [
        ...val,
        CustomMarker({
          icon: car_svg(hexToColor(this.vehicle$().colorHex)),
          latlang: [latitude, longitude] as LatLngTuple,
        }),
      ];
    });
  }

  report(vehicleId: string, from: Date = new Date(), to: Date = new Date()) {
    if (from && to && from.toISOString() && to.toISOString())
      return this.vehicleReportService.getVehicleDistanceAverageAndMaxSpeedReport({
        vehicleId: vehicleId,
        ignoreSpeedUnder: 0,
        fromDate: from.toISOString(),
        toDate: to.toISOString(),
      });
  }

  openDateDialog() {
    openDateRangeDialog(this.dialog, {
      fromDate: new Date(+new Date() - this.selectedTime.value),
      toDate: new Date(),
    })
      .pipe(
        tap(val => {
          if (val) this.selectedTime.setValue(+val.toDate - +val.fromDate);
        })
      )
      .subscribe();
  }
}
