{"version": 3, "sources": ["../../../../../../node_modules/@abp/ng.account.core/fesm2022/abp-ng.account.core.mjs", "../../../../../../node_modules/@abp/ng.theme.basic/fesm2022/abp-ng.theme.basic.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { map, finalize } from 'rxjs/operators';\nimport { ActivatedRoute } from '@angular/router';\nimport * as i2 from '@abp/ng.core';\nimport * as i1 from '@abp/ng.theme.shared';\nclass AuthWrapperService {\n  get enableLocalLogin$() {\n    return this.configState.getSetting$('Abp.Account.EnableLocalLogin').pipe(map(value => value?.toLowerCase() !== 'false'));\n  }\n  get isTenantBoxVisibleForCurrentRoute() {\n    return this.getMostInnerChild().data.tenantBoxVisible ?? true;\n  }\n  get isTenantBoxVisible() {\n    return this.isTenantBoxVisibleForCurrentRoute && this.multiTenancy.isTenantBoxVisible;\n  }\n  constructor(multiTenancy, configState, injector) {\n    this.multiTenancy = multiTenancy;\n    this.configState = configState;\n    this.isMultiTenancyEnabled$ = this.configState.getDeep$('multiTenancy.isEnabled');\n    this.tenantBoxKey = 'Account.TenantBoxComponent';\n    this.route = injector.get(ActivatedRoute);\n  }\n  getMostInnerChild() {\n    let child = this.route.snapshot;\n    let depth = 0;\n    const depthLimit = 10;\n    while (child.firstChild && depth < depthLimit) {\n      child = child.firstChild;\n      depth++;\n    }\n    return child;\n  }\n  static {\n    this.ɵfac = function AuthWrapperService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthWrapperService)(i0.ɵɵinject(i2.MultiTenancyService), i0.ɵɵinject(i2.ConfigStateService), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthWrapperService,\n      factory: AuthWrapperService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthWrapperService, [{\n    type: Injectable\n  }], () => [{\n    type: i2.MultiTenancyService\n  }, {\n    type: i2.ConfigStateService\n  }, {\n    type: i0.Injector\n  }], null);\n})();\nclass TenantBoxService {\n  constructor(toasterService, tenantService, sessionState, configState) {\n    this.toasterService = toasterService;\n    this.tenantService = tenantService;\n    this.sessionState = sessionState;\n    this.configState = configState;\n    this.currentTenant$ = this.sessionState.getTenant$();\n  }\n  onSwitch() {\n    const tenant = this.sessionState.getTenant();\n    this.name = tenant?.name || '';\n    this.isModalVisible = true;\n  }\n  save() {\n    if (!this.name) {\n      this.setTenant(null);\n      this.isModalVisible = false;\n      return;\n    }\n    this.modalBusy = true;\n    this.tenantService.findTenantByName(this.name).pipe(finalize(() => this.modalBusy = false)).subscribe(({\n      success,\n      tenantId: id,\n      ...tenant\n    }) => {\n      if (!success) {\n        this.showError();\n        return;\n      }\n      this.setTenant({\n        ...tenant,\n        id,\n        isAvailable: true\n      });\n      this.isModalVisible = false;\n    });\n  }\n  setTenant(tenant) {\n    this.sessionState.setTenant(tenant);\n    this.configState.refreshAppState();\n  }\n  showError() {\n    this.toasterService.error('AbpUiMultiTenancy::GivenTenantIsNotAvailable', 'AbpUi::Error', {\n      messageLocalizationParams: [this.name || '']\n    });\n  }\n  static {\n    this.ɵfac = function TenantBoxService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TenantBoxService)(i0.ɵɵinject(i1.ToasterService), i0.ɵɵinject(i2.AbpTenantService), i0.ɵɵinject(i2.SessionStateService), i0.ɵɵinject(i2.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TenantBoxService,\n      factory: TenantBoxService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TenantBoxService, [{\n    type: Injectable\n  }], () => [{\n    type: i1.ToasterService\n  }, {\n    type: i2.AbpTenantService\n  }, {\n    type: i2.SessionStateService\n  }, {\n    type: i2.ConfigStateService\n  }], null);\n})();\n\n/*\n * Public API Surface of account-core\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AuthWrapperService, TenantBoxService };\n", "import * as i0 from '@angular/core';\nimport { Injectable, Component, ViewChildren, Input, ViewEncapsulation, inject, Inject, ChangeDetectionStrategy, InjectionToken, APP_INITIALIZER, Injector, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport * as i1 from '@abp/ng.core';\nimport { SubscriptionService, NAVIGATE_TO_MANAGE_PROFILE, LazyLoadService, LOADING_STRATEGY, DomInsertionService, ReplaceableComponentsService, CONTENT_STRATEGY, AuthService, noop, CoreModule } from '@abp/ng.core';\nimport { fromEvent } from 'rxjs';\nimport { debounceTime, map } from 'rxjs/operators';\nimport * as i2 from '@angular/common';\nimport * as i3 from '@angular/router';\nimport * as i1$1 from '@abp/ng.theme.shared';\nimport { slideFromBottom, collapseWithMargin, DocumentDirHandlerService, NavItemsService, UserMenuService, ThemeSharedModule } from '@abp/ng.theme.shared';\nimport * as i4 from '@ng-bootstrap/ng-bootstrap';\nimport { NgbCollapseModule, NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';\nimport * as i1$2 from '@abp/ng.account.core';\nimport { TenantBoxService, AuthWrapperService } from '@abp/ng.account.core';\nimport * as i2$1 from '@angular/forms';\nimport { ValidationErrorComponent as ValidationErrorComponent$1, VALIDATION_ERROR_TEMPLATE, VALIDATION_TARGET_SELECTOR, VALIDATION_INVALID_CLASSES, NgxValidateCoreModule } from '@ngx-validate/core';\nfunction LogoComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.appInfo.logoUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.appInfo.name);\n  }\n}\nfunction LogoComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.appInfo.name, \" \");\n  }\n}\nfunction NavItemsComponent_For_2_ng_container_0_li_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 4);\n    i0.ɵɵpipe(1, \"toInjector\");\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngComponentOutlet\", item_r1.component)(\"ngComponentOutletInjector\", i0.ɵɵpipeBind1(1, 2, item_r1));\n  }\n}\nfunction NavItemsComponent_For_2_ng_container_0_li_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function NavItemsComponent_For_2_ng_container_0_li_1_Conditional_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r1 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(item_r1.action ? item_r1.action() : null);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r1.html, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NavItemsComponent_For_2_ng_container_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 3);\n    i0.ɵɵtemplate(1, NavItemsComponent_For_2_ng_container_0_li_1_Conditional_1_Template, 2, 4, \"ng-container\", 4)(2, NavItemsComponent_For_2_ng_container_0_li_1_Conditional_2_Template, 1, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.component ? 1 : 2);\n  }\n}\nfunction NavItemsComponent_For_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavItemsComponent_For_2_ng_container_0_li_1_Template, 3, 1, \"li\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpPermission\", item_r1.requiredPolicy);\n  }\n}\nfunction NavItemsComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NavItemsComponent_For_2_ng_container_0_Template, 2, 1, \"ng-container\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"abpVisible\", !item_r1.visible || item_r1.visible(item_r1));\n  }\n}\nconst _c0 = [\"childrenContainer\"];\nconst _c1 = a0 => ({\n  $implicit: a0\n});\nconst _c2 = a0 => [a0];\nfunction RoutesComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 9);\n  }\n  if (rf & 2) {\n    const route_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const defaultLink_r3 = i0.ɵɵreference(5);\n    const dropdownLink_r4 = i0.ɵɵreference(7);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.isDropdown(route_r1) ? dropdownLink_r4 : defaultLink_r3)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, route_r1));\n  }\n}\nfunction RoutesComponent_ng_template_4_li_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n  if (rf & 2) {\n    const route_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", route_r5.iconClass);\n  }\n}\nfunction RoutesComponent_ng_template_4_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 11)(1, \"a\", 12);\n    i0.ɵɵtemplate(2, RoutesComponent_ng_template_4_li_0_Conditional_2_Template, 1, 1, \"i\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const route_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(5, _c2, route_r5.path));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(route_r5.iconClass ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, route_r5.name), \" \");\n  }\n}\nfunction RoutesComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_4_li_0_Template, 5, 7, \"li\", 10);\n  }\n  if (rf & 2) {\n    const route_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"abpPermission\", route_r5.requiredPolicy);\n  }\n}\nfunction RoutesComponent_ng_template_6_Conditional_0_li_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n  if (rf & 2) {\n    const route_r7 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", route_r7.iconClass);\n  }\n}\nfunction RoutesComponent_ng_template_6_Conditional_0_li_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction RoutesComponent_ng_template_6_Conditional_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 16);\n    i0.ɵɵlistener(\"click\", function RoutesComponent_ng_template_6_Conditional_0_li_0_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const route_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.rootDropdownExpand[route_r7.name] ? (ctx_r1.rootDropdownExpand[route_r7.name] = false) : (ctx_r1.rootDropdownExpand[route_r7.name] = true));\n    });\n    i0.ɵɵelementStart(1, \"a\", 17);\n    i0.ɵɵtemplate(2, RoutesComponent_ng_template_6_Conditional_0_li_0_Conditional_2_Template, 1, 1, \"i\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18, 5);\n    i0.ɵɵlistener(\"click\", function RoutesComponent_ng_template_6_Conditional_0_li_0_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      $event.preventDefault();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(7, RoutesComponent_ng_template_6_Conditional_0_li_0_ng_container_7_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const route_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const forTemplate_r8 = i0.ɵɵreference(9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(route_r7.iconClass ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, route_r7.name), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-block\", ctx_r1.smallScreen && ctx_r1.rootDropdownExpand[route_r7.name]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", forTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c1, route_r7));\n  }\n}\nfunction RoutesComponent_ng_template_6_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_6_Conditional_0_li_0_Template, 8, 10, \"li\", 15);\n  }\n  if (rf & 2) {\n    const route_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"abpPermission\", route_r7.requiredPolicy);\n  }\n}\nfunction RoutesComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_6_Conditional_0_Template, 1, 1, \"li\", 14);\n  }\n  if (rf & 2) {\n    const route_r7 = ctx.$implicit;\n    i0.ɵɵconditional((route_r7.children == null ? null : route_r7.children.length) ? 0 : -1);\n  }\n}\nfunction RoutesComponent_ng_template_8_For_1_ng_template_0_Template(rf, ctx) {}\nfunction RoutesComponent_ng_template_8_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_8_For_1_ng_template_0_Template, 0, 0, \"ng-template\", 9);\n  }\n  if (rf & 2) {\n    const child_r9 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const defaultChild_r10 = i0.ɵɵreference(11);\n    const dropdownChild_r11 = i0.ɵɵreference(13);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", (child_r9.children == null ? null : child_r9.children.length) ? dropdownChild_r11 : defaultChild_r10)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c1, child_r9));\n  }\n}\nfunction RoutesComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, RoutesComponent_ng_template_8_For_1_Template, 1, 4, null, 9, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const route_r12 = ctx.$implicit;\n    i0.ɵɵrepeater(route_r12.children);\n  }\n}\nfunction RoutesComponent_ng_template_10_Conditional_0_div_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n  if (rf & 2) {\n    const child_r14 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r14.iconClass);\n  }\n}\nfunction RoutesComponent_ng_template_10_Conditional_0_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function RoutesComponent_ng_template_10_Conditional_0_div_0_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtemplate(2, RoutesComponent_ng_template_10_Conditional_0_div_0_Conditional_2_Template, 1, 1, \"i\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const child_r14 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(5, _c2, child_r14.path));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(child_r14.iconClass ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, child_r14.name), \"\");\n  }\n}\nfunction RoutesComponent_ng_template_10_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_10_Conditional_0_div_0_Template, 5, 7, \"div\", 21);\n  }\n  if (rf & 2) {\n    const child_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"abpPermission\", child_r14.requiredPolicy);\n  }\n}\nfunction RoutesComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_10_Conditional_0_Template, 1, 1, \"div\", 20);\n  }\n  if (rf & 2) {\n    const child_r14 = ctx.$implicit;\n    i0.ɵɵconditional(child_r14.path ? 0 : -1);\n  }\n}\nfunction RoutesComponent_ng_template_12_div_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n  if (rf & 2) {\n    const child_r15 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r15.iconClass);\n  }\n}\nfunction RoutesComponent_ng_template_12_div_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction RoutesComponent_ng_template_12_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24, 6)(2, \"div\", 25)(3, \"a\", 26);\n    i0.ɵɵtemplate(4, RoutesComponent_ng_template_12_div_0_Conditional_4_Template, 1, 1, \"i\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 27, 7);\n    i0.ɵɵtemplate(9, RoutesComponent_ng_template_12_div_0_ng_container_9_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dropdownSubmenu_r16 = i0.ɵɵreference(1);\n    const child_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const forTemplate_r8 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"autoClose\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"dropdown-toggle\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpEllipsisEnabled\", !ctx_r1.smallScreen);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(child_r15.iconClass ? 4 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 10, child_r15.name), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-block\", ctx_r1.smallScreen && dropdownSubmenu_r16.isOpen());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", forTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c1, child_r15));\n  }\n}\nfunction RoutesComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RoutesComponent_ng_template_12_div_0_Template, 10, 14, \"div\", 23);\n  }\n  if (rf & 2) {\n    const child_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"abpPermission\", child_r15.requiredPolicy);\n  }\n}\nconst _c3 = a0 => ({\n  \"alert-dismissible fade show\": a0\n});\nfunction PageAlertContainerComponent_For_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"h4\", 2);\n    i0.ɵɵpipe(1, \"abpSafeHtml\");\n    i0.ɵɵpipe(2, \"abpLocalization\");\n  }\n  if (rf & 2) {\n    const alert_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(2, 3, i0.ɵɵpipeBind1(1, 1, alert_r1.title), alert_r1.titleLocalizationParams), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PageAlertContainerComponent_For_1_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function PageAlertContainerComponent_For_1_Conditional_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ɵ$index_1_r3 = i0.ɵɵnextContext().$index;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.service.remove(ɵ$index_1_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PageAlertContainerComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, PageAlertContainerComponent_For_1_Conditional_1_Template, 3, 6, \"h4\", 2);\n    i0.ɵɵelement(2, \"span\", 3);\n    i0.ɵɵpipe(3, \"abpSafeHtml\");\n    i0.ɵɵpipe(4, \"abpLocalization\");\n    i0.ɵɵtemplate(5, PageAlertContainerComponent_For_1_Conditional_5_Template, 1, 0, \"button\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const alert_r1 = ctx.$implicit;\n    i0.ɵɵclassMapInterpolate1(\"alert alert-\", alert_r1.type, \" fade show\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c3, alert_r1.dismissible));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(alert_r1.title ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(4, 9, i0.ɵɵpipeBind1(3, 7, alert_r1.message), alert_r1.messageLocalizationParams), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(alert_r1.dismissible ? 5 : -1);\n  }\n}\nconst _c4 = () => ({\n  name: \"\"\n});\nfunction TenantBoxComponent_Conditional_0_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\");\n    i0.ɵɵtext(1, \"Switch Tenant\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TenantBoxComponent_Conditional_0_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 12);\n    i0.ɵɵlistener(\"ngSubmit\", function TenantBoxComponent_Conditional_0_ng_template_19_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.service.save());\n    });\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14)(3, \"label\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"input\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TenantBoxComponent_Conditional_0_ng_template_19_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.service.name, $event) || (ctx_r1.service.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"abpLocalization\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"AbpUiMultiTenancy::Name\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.service.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"AbpUiMultiTenancy::SwitchTenantHint\"));\n  }\n}\nfunction TenantBoxComponent_Conditional_0_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"abp-button\", 18);\n    i0.ɵɵlistener(\"click\", function TenantBoxComponent_Conditional_0_ng_template_21_Template_abp_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.service.save());\n    });\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"abpLocalization\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const currentTenant_r5 = i0.ɵɵnextContext();\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, \"AbpTenantManagement::Cancel\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", (currentTenant_r5 == null ? null : currentTenant_r5.name) === ctx_r1.service.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 5, \"AbpTenantManagement::Save\"));\n  }\n}\nfunction TenantBoxComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"span\", 7);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"br\");\n    i0.ɵɵelementStart(8, \"h6\", 8)(9, \"i\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"abpLocalization\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 9)(13, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function TenantBoxComponent_Conditional_0_Template_a_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.service.onSwitch());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"abpLocalization\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(16, \"abp-modal\", 11);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function TenantBoxComponent_Conditional_0_Template_abp_modal_visibleChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.service.isModalVisible, $event) || (ctx_r1.service.isModalVisible = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(17, TenantBoxComponent_Conditional_0_ng_template_17_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(19, TenantBoxComponent_Conditional_0_ng_template_19_Template, 10, 7, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(21, TenantBoxComponent_Conditional_0_ng_template_21_Template, 7, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 5, \"AbpUiMultiTenancy::Tenant\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx.name || i0.ɵɵpipeBind1(11, 7, \"AbpUiMultiTenancy::NotSelected\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 9, \"AbpUiMultiTenancy::Switch\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.service.isModalVisible);\n    i0.ɵɵproperty(\"busy\", ctx_r1.service.modalBusy);\n  }\n}\nconst _c5 = [\"*\"];\nconst _c6 = a0 => ({\n  componentKey: a0\n});\nfunction AuthWrapperComponent_Conditional_2_abp_tenant_box_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-tenant-box\");\n  }\n}\nfunction AuthWrapperComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AuthWrapperComponent_Conditional_2_abp_tenant_box_0_Template, 1, 0, \"abp-tenant-box\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction1(1, _c6, ctx_r0.service.tenantBoxKey));\n  }\n}\nfunction AuthWrapperComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 6);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AuthWrapperComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"AbpAccount::InvalidLoginRequest\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"AbpAccount::ThereAreNoLoginSchemesConfiguredForThisClient\"), \" \");\n  }\n}\nconst _c7 = a0 => ({\n  value: a0\n});\nconst _c8 = a0 => ({\n  smallScreen: a0\n});\nconst _c9 = (a0, a1) => ({\n  componentKey: a0,\n  inputs: a1\n});\nfunction AccountLayoutComponent_abp_logo_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-logo\");\n  }\n}\nfunction AccountLayoutComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccountLayoutComponent_Conditional_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccountLayoutComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AccountLayoutComponent_Conditional_7_ng_container_1_Template, 1, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const navigations_r3 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"@collapseWithMargin\", ctx_r1.service.isCollapsed ? \"collapsed\" : \"expanded\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", navigations_r3);\n  }\n}\nfunction AccountLayoutComponent_ng_template_8_abp_routes_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-routes\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"smallScreen\", ctx_r1.service.smallScreen);\n  }\n}\nfunction AccountLayoutComponent_ng_template_8_abp_nav_items_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-nav-items\");\n  }\n}\nfunction AccountLayoutComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccountLayoutComponent_ng_template_8_abp_routes_0_Template, 1, 1, \"abp-routes\", 9)(1, AccountLayoutComponent_ng_template_8_abp_nav_items_1_Template, 1, 0, \"abp-nav-items\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction2(6, _c9, ctx_r1.service.routesComponentKey, i0.ɵɵpureFunction1(4, _c8, i0.ɵɵpureFunction1(2, _c7, ctx_r1.service.smallScreen))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction1(9, _c6, ctx_r1.service.navItemsComponentKey));\n  }\n}\nfunction AccountLayoutComponent_abp_auth_wrapper_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"abp-auth-wrapper\");\n    i0.ɵɵelement(1, \"router-outlet\", null, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApplicationLayoutComponent_abp_logo_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-logo\");\n  }\n}\nfunction ApplicationLayoutComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ApplicationLayoutComponent_Conditional_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ApplicationLayoutComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ApplicationLayoutComponent_Conditional_7_ng_container_1_Template, 1, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const navigations_r3 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"@collapseWithMargin\", ctx_r1.service.isCollapsed ? \"collapsed\" : \"expanded\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", navigations_r3);\n  }\n}\nfunction ApplicationLayoutComponent_ng_template_8_abp_routes_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-routes\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"smallScreen\", ctx_r1.service.smallScreen);\n  }\n}\nfunction ApplicationLayoutComponent_ng_template_8_abp_nav_items_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"abp-nav-items\");\n  }\n}\nfunction ApplicationLayoutComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ApplicationLayoutComponent_ng_template_8_abp_routes_0_Template, 1, 1, \"abp-routes\", 9)(1, ApplicationLayoutComponent_ng_template_8_abp_nav_items_1_Template, 1, 0, \"abp-nav-items\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction2(6, _c9, ctx_r1.service.routesComponentKey, i0.ɵɵpureFunction1(4, _c8, i0.ɵɵpureFunction1(2, _c7, ctx_r1.service.smallScreen))));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction1(9, _c6, ctx_r1.service.navItemsComponentKey));\n  }\n}\nfunction CurrentUserComponent_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\")(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"\\\\\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx);\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 8);\n    i0.ɵɵpipe(1, \"toInjector\");\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngComponentOutlet\", item_r1.component)(\"ngComponentOutletInjector\", i0.ɵɵpipeBind1(1, 2, item_r1));\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r1 = i0.ɵɵnextContext(4).$implicit;\n      return i0.ɵɵresetView(item_r1.action ? item_r1.action() : null);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r1.html, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(6).$implicit;\n    i0.ɵɵclassMapInterpolate1(\"me-1 \", item_r1.textTemplate.icon, \"\");\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Conditional_0_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const item_r1 = i0.ɵɵnextContext(5).$implicit;\n      return i0.ɵɵresetView(item_r1.action ? item_r1.action() : null);\n    });\n    i0.ɵɵtemplate(1, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Conditional_0_Conditional_1_Template, 1, 3, \"i\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(5).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.textTemplate.icon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, item_r1.textTemplate.text), \"\");\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Conditional_0_Template, 4, 4, \"a\", 11);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵconditional(item_r1.textTemplate ? 0 : -1);\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_0_Template, 1, 1, \"div\", 9)(1, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵconditional(item_r1.html ? 0 : 1);\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 7);\n    i0.ɵɵtemplate(1, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_1_Template, 2, 4, \"ng-container\", 8)(2, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Conditional_2_Template, 2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.component ? 1 : 2);\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CurrentUserComponent_Conditional_0_For_10_ng_container_0_li_1_Template, 3, 1, \"li\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"abpPermission\", item_r1.requiredPolicy);\n  }\n}\nfunction CurrentUserComponent_Conditional_0_For_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CurrentUserComponent_Conditional_0_For_10_ng_container_0_Template, 2, 1, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"abpVisible\", !item_r1.visible || item_r1.visible(item_r1));\n  }\n}\nfunction CurrentUserComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"a\", 3);\n    i0.ɵɵtemplate(3, CurrentUserComponent_Conditional_0_Conditional_3_Template, 4, 1, \"small\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 4);\n    i0.ɵɵrepeaterCreate(9, CurrentUserComponent_Conditional_0_For_10_Template, 1, 1, \"ng-container\", null, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const currentUserDropdown_r4 = i0.ɵɵreference(1);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional((tmp_2_0 = (tmp_2_0 = i0.ɵɵpipeBind1(4, 4, ctx_r4.selectedTenant$)) == null ? null : tmp_2_0.name) ? 3 : -1, tmp_2_0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_3_0 = i0.ɵɵpipeBind1(7, 6, ctx_r4.currentUser$)) == null ? null : tmp_3_0.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-block\", ctx_r4.smallScreen && currentUserDropdown_r4.isOpen());\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(i0.ɵɵpipeBind1(11, 8, ctx_r4.userMenu.items$));\n  }\n}\nfunction CurrentUserComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 14);\n    i0.ɵɵlistener(\"click\", function CurrentUserComponent_Conditional_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToLogin());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"AbpAccount::Login\"), \" \");\n  }\n}\nfunction LanguagesComponent_Conditional_0_For_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵlistener(\"click\", function LanguagesComponent_Conditional_0_For_7_Template_a_click_0_listener() {\n      const lang_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onChangeLang(lang_r2.cultureName || \"\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lang_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(lang_r2 == null ? null : lang_r2.displayName);\n  }\n}\nfunction LanguagesComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"a\", 2);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 3);\n    i0.ɵɵrepeaterCreate(6, LanguagesComponent_Conditional_0_For_7_Template, 2, 1, \"a\", 4, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵpipe(8, \"async\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const languageDropdown_r4 = i0.ɵɵreference(1);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 3, ctx_r2.defaultLanguage$), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-block\", ctx_r2.smallScreen && languageDropdown_r4.isOpen());\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(i0.ɵɵpipeBind1(8, 5, ctx_r2.dropdownLanguages$));\n  }\n}\nfunction ValidationErrorComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"abpLocalization\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, error_r1.message, error_r1.interpoliteParams), \" \");\n  }\n}\nclass LayoutService {\n  constructor(subscription, cdRef, routerEvents) {\n    this.subscription = subscription;\n    this.cdRef = cdRef;\n    this.isCollapsed = true;\n    this.logoComponentKey = \"Theme.LogoComponent\" /* eThemeBasicComponents.Logo */;\n    this.routesComponentKey = \"Theme.RoutesComponent\" /* eThemeBasicComponents.Routes */;\n    this.navItemsComponentKey = \"Theme.NavItemsComponent\" /* eThemeBasicComponents.NavItems */;\n    subscription.addOne(routerEvents.getNavigationEvents(\"End\"), () => {\n      this.isCollapsed = true;\n    });\n  }\n  checkWindowWidth() {\n    const isSmallScreen = window.innerWidth < 992;\n    if (isSmallScreen && this.smallScreen === false) {\n      this.isCollapsed = false;\n      setTimeout(() => {\n        this.isCollapsed = true;\n      }, 100);\n    }\n    this.smallScreen = isSmallScreen;\n    this.cdRef.detectChanges();\n  }\n  subscribeWindowSize() {\n    this.checkWindowWidth();\n    const resize$ = fromEvent(window, 'resize').pipe(debounceTime(150));\n    this.subscription.addOne(resize$, () => this.checkWindowWidth());\n  }\n  static {\n    this.ɵfac = function LayoutService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LayoutService)(i0.ɵɵinject(i1.SubscriptionService), i0.ɵɵinject(i0.ChangeDetectorRef), i0.ɵɵinject(i1.RouterEvents));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LayoutService,\n      factory: LayoutService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutService, [{\n    type: Injectable\n  }], () => [{\n    type: i1.SubscriptionService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.RouterEvents\n  }], null);\n})();\nclass LogoComponent {\n  get appInfo() {\n    return this.environment.getEnvironment().application;\n  }\n  constructor(environment) {\n    this.environment = environment;\n  }\n  static {\n    this.ɵfac = function LogoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LogoComponent)(i0.ɵɵdirectiveInject(i1.EnvironmentService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LogoComponent,\n      selectors: [[\"abp-logo\"]],\n      decls: 3,\n      vars: 1,\n      consts: [[\"routerLink\", \"/\", 1, \"navbar-brand\"], [\"width\", \"100%\", \"height\", \"auto\", 3, \"src\", \"alt\"]],\n      template: function LogoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"a\", 0);\n          i0.ɵɵtemplate(1, LogoComponent_Conditional_1_Template, 1, 2, \"img\", 1)(2, LogoComponent_Conditional_2_Template, 1, 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.appInfo.logoUrl ? 1 : 2);\n        }\n      },\n      dependencies: [i3.RouterLink],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LogoComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-logo',\n      template: `\n    <a class=\"navbar-brand\" routerLink=\"/\">\n      @if (appInfo.logoUrl) {\n        <img\n          [src]=\"appInfo.logoUrl\"\n          [alt]=\"appInfo.name\"\n          width=\"100%\"\n          height=\"auto\"\n        />\n      } @else {\n        {{ appInfo.name }}\n      }\n    </a>\n  `\n    }]\n  }], () => [{\n    type: i1.EnvironmentService\n  }], null);\n})();\nclass NavItemsComponent {\n  constructor(navItems) {\n    this.navItems = navItems;\n    this.trackByFn = (_, element) => element.id;\n  }\n  static {\n    this.ɵfac = function NavItemsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavItemsComponent)(i0.ɵɵdirectiveInject(i1$1.NavItemsService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NavItemsComponent,\n      selectors: [[\"abp-nav-items\"]],\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"navbar-nav\"], [4, \"abpVisible\"], [\"class\", \"nav-item d-flex align-items-center\", 4, \"abpPermission\"], [1, \"nav-item\", \"d-flex\", \"align-items-center\"], [3, \"ngComponentOutlet\", \"ngComponentOutletInjector\"], [3, \"innerHTML\"], [3, \"click\", \"innerHTML\"]],\n      template: function NavItemsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 0);\n          i0.ɵɵrepeaterCreate(1, NavItemsComponent_For_2_Template, 1, 1, \"ng-container\", null, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(i0.ɵɵpipeBind1(3, 0, ctx.navItems.items$));\n        }\n      },\n      dependencies: [i2.NgComponentOutlet, i1.PermissionDirective, i1$1.AbpVisibleDirective, i2.AsyncPipe, i1.ToInjectorPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavItemsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-nav-items',\n      template: \"<ul class=\\\"navbar-nav\\\">\\r\\n  @for (item of navItems.items$ | async; track $index) {\\r\\n    <ng-container *abpVisible=\\\"!item.visible || item.visible(item)\\\">\\r\\n      <li class=\\\"nav-item d-flex align-items-center\\\" *abpPermission=\\\"item.requiredPolicy\\\">\\r\\n        @if (item.component) {\\r\\n          <ng-container\\r\\n            [ngComponentOutlet]=\\\"item.component\\\"\\r\\n            [ngComponentOutletInjector]=\\\"item | toInjector\\\"\\r\\n          ></ng-container>\\r\\n        } @else {\\r\\n          <div [innerHTML]=\\\"item.html\\\" (click)=\\\"item.action ? item.action() : null\\\"></div>\\r\\n        }\\r\\n      </li>\\r\\n    </ng-container>\\r\\n  }\\r\\n</ul>\\r\\n\"\n    }]\n  }], () => [{\n    type: i1$1.NavItemsService\n  }], null);\n})();\nclass RoutesComponent {\n  constructor(routesService, renderer) {\n    this.routesService = routesService;\n    this.renderer = renderer;\n    this.rootDropdownExpand = {};\n    this.trackByFn = (_, item) => item.name;\n  }\n  isDropdown(node) {\n    return !node?.isLeaf || this.routesService.hasChildren(node.name);\n  }\n  closeDropdown() {\n    this.childrenContainers.forEach(({\n      nativeElement\n    }) => {\n      this.renderer.addClass(nativeElement, 'd-none');\n      setTimeout(() => this.renderer.removeClass(nativeElement, 'd-none'), 0);\n    });\n  }\n  static {\n    this.ɵfac = function RoutesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoutesComponent)(i0.ɵɵdirectiveInject(i1.RoutesService), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: RoutesComponent,\n      selectors: [[\"abp-routes\"]],\n      viewQuery: function RoutesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.childrenContainers = _t);\n        }\n      },\n      inputs: {\n        smallScreen: \"smallScreen\"\n      },\n      decls: 14,\n      vars: 2,\n      consts: [[\"defaultLink\", \"\"], [\"dropdownLink\", \"\"], [\"forTemplate\", \"\"], [\"defaultChild\", \"\"], [\"dropdownChild\", \"\"], [\"routeContainer\", \"\"], [\"dropdownSubmenu\", \"ngbDropdown\"], [\"childrenContainer\", \"\"], [1, \"navbar-nav\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"nav-item\", 4, \"abpPermission\"], [1, \"nav-item\"], [1, \"nav-link\", 3, \"routerLink\"], [3, \"ngClass\"], [\"display\", \"static\", 1, \"nav-item\", \"dropdown\"], [\"class\", \"nav-item dropdown\", \"display\", \"static\", 3, \"click\", 4, \"abpPermission\"], [\"display\", \"static\", 1, \"nav-item\", \"dropdown\", 3, \"click\"], [\"data-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", \"href\", \"javascript:void(0)\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"dropdown-menu\", \"border-0\", \"shadow-sm\", 3, \"click\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"dropdown-submenu\"], [\"class\", \"dropdown-submenu\", 4, \"abpPermission\"], [1, \"dropdown-item\", 3, \"click\", \"routerLink\"], [\"class\", \"dropdown-submenu\", \"ngbDropdown\", \"\", \"placement\", \"right-top\", 3, \"autoClose\", 4, \"abpPermission\"], [\"ngbDropdown\", \"\", \"placement\", \"right-top\", 1, \"dropdown-submenu\", 3, \"autoClose\"], [\"ngbDropdownToggle\", \"\"], [\"abpEllipsis\", \"210px\", \"role\", \"button\", 1, \"btn\", \"d-block\", \"text-start\", \"dropdown-toggle\", 3, \"abpEllipsisEnabled\"], [1, \"dropdown-menu\", \"dropdown-menu-start\", \"border-0\", \"shadow-sm\"]],\n      template: function RoutesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ul\", 8);\n          i0.ɵɵrepeaterCreate(1, RoutesComponent_For_2_Template, 1, 4, \"ng-container\", 9, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵtemplate(4, RoutesComponent_ng_template_4_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(6, RoutesComponent_ng_template_6_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(8, RoutesComponent_ng_template_8_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(10, RoutesComponent_ng_template_10_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(12, RoutesComponent_ng_template_12_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(i0.ɵɵpipeBind1(3, 0, ctx.routesService.visible$));\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgTemplateOutlet, i3.RouterLink, i1.PermissionDirective, i4.NgbDropdown, i4.NgbDropdownToggle, i2.AsyncPipe, i1.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutesComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-routes',\n      template: \"<ul class=\\\"navbar-nav\\\">\\r\\n  @for (route of routesService.visible$ | async; track $index) {\\r\\n    <ng-container\\r\\n      [ngTemplateOutlet]=\\\"isDropdown(route) ? dropdownLink : defaultLink\\\"\\r\\n      [ngTemplateOutletContext]=\\\"{ $implicit: route }\\\"\\r\\n    >\\r\\n    </ng-container>\\r\\n  }\\r\\n\\r\\n  <ng-template #defaultLink let-route>\\r\\n    <li class=\\\"nav-item\\\" *abpPermission=\\\"route.requiredPolicy\\\">\\r\\n      <a class=\\\"nav-link\\\" [routerLink]=\\\"[route.path]\\\">\\r\\n        @if (route.iconClass) {\\r\\n          <i [ngClass]=\\\"route.iconClass\\\"></i>\\r\\n        }\\r\\n        {{ route.name | abpLocalization }}\\r\\n      </a>\\r\\n    </li>\\r\\n  </ng-template>\\r\\n\\r\\n  <ng-template #dropdownLink let-route>\\r\\n    @if (route.children?.length) {\\r\\n      <li\\r\\n        class=\\\"nav-item dropdown\\\"\\r\\n        display=\\\"static\\\"\\r\\n        *abpPermission=\\\"route.requiredPolicy\\\"\\r\\n        (click)=\\\"\\r\\n          rootDropdownExpand[route.name]\\r\\n            ? (rootDropdownExpand[route.name] = false)\\r\\n            : (rootDropdownExpand[route.name] = true)\\r\\n        \\\"\\r\\n      >\\r\\n        <a\\r\\n          class=\\\"nav-link dropdown-toggle\\\"\\r\\n          data-toggle=\\\"dropdown\\\"\\r\\n          aria-haspopup=\\\"true\\\"\\r\\n          aria-expanded=\\\"false\\\"\\r\\n          href=\\\"javascript:void(0)\\\"\\r\\n        >\\r\\n          @if (route.iconClass) {\\r\\n            <i [ngClass]=\\\"route.iconClass\\\"></i>\\r\\n          }\\r\\n          {{ route.name | abpLocalization }}\\r\\n        </a>\\r\\n        <div\\r\\n          #routeContainer\\r\\n          class=\\\"dropdown-menu border-0 shadow-sm\\\"\\r\\n          (click)=\\\"$event.preventDefault(); $event.stopPropagation()\\\"\\r\\n          [class.d-block]=\\\"smallScreen && rootDropdownExpand[route.name]\\\"\\r\\n        >\\r\\n          <ng-container\\r\\n            *ngTemplateOutlet=\\\"forTemplate; context: { $implicit: route }\\\"\\r\\n          ></ng-container>\\r\\n        </div>\\r\\n      </li>\\r\\n    }\\r\\n  </ng-template>\\r\\n\\r\\n  <ng-template #forTemplate let-route>\\r\\n    @for (child of route.children; track $index) {\\r\\n      <ng-template\\r\\n        [ngTemplateOutlet]=\\\"child.children?.length ? dropdownChild : defaultChild\\\"\\r\\n        [ngTemplateOutletContext]=\\\"{ $implicit: child }\\\"\\r\\n      ></ng-template>\\r\\n    }\\r\\n  </ng-template>\\r\\n\\r\\n  <ng-template #defaultChild let-child>\\r\\n    @if (child.path) {\\r\\n      <div class=\\\"dropdown-submenu\\\" *abpPermission=\\\"child.requiredPolicy\\\">\\r\\n        <a class=\\\"dropdown-item\\\" [routerLink]=\\\"[child.path]\\\" (click)=\\\"closeDropdown()\\\">\\r\\n          @if (child.iconClass) {\\r\\n            <i [ngClass]=\\\"child.iconClass\\\"></i>\\r\\n          }\\r\\n          {{ child.name | abpLocalization }}</a\\r\\n        >\\r\\n      </div>\\r\\n    }\\r\\n  </ng-template>\\r\\n\\r\\n  <ng-template #dropdownChild let-child>\\r\\n    <div\\r\\n      class=\\\"dropdown-submenu\\\"\\r\\n      ngbDropdown\\r\\n      #dropdownSubmenu=\\\"ngbDropdown\\\"\\r\\n      placement=\\\"right-top\\\"\\r\\n      [autoClose]=\\\"true\\\"\\r\\n      *abpPermission=\\\"child.requiredPolicy\\\"\\r\\n    >\\r\\n      <div ngbDropdownToggle [class.dropdown-toggle]=\\\"false\\\">\\r\\n        <a\\r\\n          abpEllipsis=\\\"210px\\\"\\r\\n          [abpEllipsisEnabled]=\\\"!smallScreen\\\"\\r\\n          role=\\\"button\\\"\\r\\n          class=\\\"btn d-block text-start dropdown-toggle\\\"\\r\\n        >\\r\\n          @if (child.iconClass) {\\r\\n            <i [ngClass]=\\\"child.iconClass\\\"></i>\\r\\n          }\\r\\n          {{ child.name | abpLocalization }}\\r\\n        </a>\\r\\n      </div>\\r\\n      <div\\r\\n        #childrenContainer\\r\\n        class=\\\"dropdown-menu dropdown-menu-start border-0 shadow-sm\\\"\\r\\n        [class.d-block]=\\\"smallScreen && dropdownSubmenu.isOpen()\\\"\\r\\n      >\\r\\n        <ng-container *ngTemplateOutlet=\\\"forTemplate; context: { $implicit: child }\\\"></ng-container>\\r\\n      </div>\\r\\n    </div>\\r\\n  </ng-template>\\r\\n</ul>\\r\\n\"\n    }]\n  }], () => [{\n    type: i1.RoutesService\n  }, {\n    type: i0.Renderer2\n  }], {\n    smallScreen: [{\n      type: Input\n    }],\n    childrenContainers: [{\n      type: ViewChildren,\n      args: ['childrenContainer']\n    }]\n  });\n})();\nclass PageAlertContainerComponent {\n  constructor(service) {\n    this.service = service;\n  }\n  static {\n    this.ɵfac = function PageAlertContainerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PageAlertContainerComponent)(i0.ɵɵdirectiveInject(i1$1.PageAlertService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PageAlertContainerComponent,\n      selectors: [[\"abp-page-alert-container\"]],\n      decls: 3,\n      vars: 2,\n      consts: [[\"role\", \"alert\", 3, \"class\", \"ngClass\"], [\"role\", \"alert\", 3, \"ngClass\"], [1, \"alert-heading\", 3, \"innerHTML\"], [3, \"innerHTML\"], [\"type\", \"button\", \"data-dismiss\", \"alert\", \"aria-label\", \"Close\", 1, \"btn-close\"], [\"type\", \"button\", \"data-dismiss\", \"alert\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"]],\n      template: function PageAlertContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, PageAlertContainerComponent_For_1_Template, 6, 14, \"div\", 0, i0.ɵɵrepeaterTrackByIndex);\n          i0.ɵɵpipe(2, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(i0.ɵɵpipeBind1(2, 0, ctx.service.alerts$));\n        }\n      },\n      dependencies: [i2.NgClass, i2.AsyncPipe, i1.LocalizationPipe, i1.SafeHtmlPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PageAlertContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-page-alert-container',\n      encapsulation: ViewEncapsulation.None,\n      template: \"@for (alert of service.alerts$ | async; track i; let i = $index) {\\r\\n  <div\\r\\n    class=\\\"alert alert-{{ alert.type }} fade show\\\"\\r\\n    [ngClass]=\\\"{ 'alert-dismissible fade show': alert.dismissible }\\\"\\r\\n    role=\\\"alert\\\"\\r\\n  >\\r\\n    @if (alert.title) {\\r\\n      <h4\\r\\n        class=\\\"alert-heading\\\"\\r\\n        [innerHTML]=\\\"alert.title | abpSafeHtml | abpLocalization: alert.titleLocalizationParams\\\"\\r\\n      ></h4>\\r\\n    }\\r\\n    <span\\r\\n      [innerHTML]=\\\"alert.message | abpSafeHtml | abpLocalization: alert.messageLocalizationParams\\\"\\r\\n    ></span>\\r\\n    @if (alert.dismissible) {\\r\\n      <button\\r\\n        type=\\\"button\\\"\\r\\n        class=\\\"btn-close\\\"\\r\\n        data-dismiss=\\\"alert\\\"\\r\\n        aria-label=\\\"Close\\\"\\r\\n        (click)=\\\"service.remove(i)\\\"\\r\\n      ></button>\\r\\n    }\\r\\n  </div>\\r\\n}\\r\\n\"\n    }]\n  }], () => [{\n    type: i1$1.PageAlertService\n  }], null);\n})();\nclass TenantBoxComponent {\n  constructor(service) {\n    this.service = service;\n  }\n  static {\n    this.ɵfac = function TenantBoxComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TenantBoxComponent)(i0.ɵɵdirectiveInject(i1$2.TenantBoxService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TenantBoxComponent,\n      selectors: [[\"abp-tenant-box\"]],\n      features: [i0.ɵɵProvidersFeature([TenantBoxService])],\n      decls: 2,\n      vars: 4,\n      consts: [[\"abpHeader\", \"\"], [\"abpBody\", \"\"], [\"abpFooter\", \"\"], [1, \"card\", \"shadow-sm\", \"rounded\", \"mb-3\"], [1, \"card-body\", \"px-5\"], [1, \"row\"], [1, \"col\"], [1, \"text-uppercase\", \"text-muted\", 2, \"font-size\", \"0.8em\"], [1, \"m-0\", \"d-inline-block\"], [1, \"col-auto\"], [\"id\", \"AbpTenantSwitchLink\", \"href\", \"javascript:void(0);\", 1, \"btn\", \"btn-sm\", \"mt-3\", \"btn-outline-primary\", 3, \"click\"], [3, \"visibleChange\", \"visible\", \"busy\"], [3, \"ngSubmit\"], [1, \"mt-2\"], [1, \"mb-3\", \"form-group\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"tenant\", \"autofocus\", \"\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"abpClose\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-outline-primary\"], [\"type\", \"abp-button\", \"iconClass\", \"fa fa-check\", 3, \"click\", \"disabled\"]],\n      template: function TenantBoxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TenantBoxComponent_Conditional_0_Template, 23, 11);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          let tmp_0_0;\n          i0.ɵɵconditional((tmp_0_0 = i0.ɵɵpipeBind1(1, 1, ctx.service.currentTenant$) || i0.ɵɵpureFunction0(3, _c4)) ? 0 : -1, tmp_0_0);\n        }\n      },\n      dependencies: [i2$1.ɵNgNoValidate, i2$1.DefaultValueAccessor, i2$1.NgControlStatus, i2$1.NgControlStatusGroup, i2$1.NgModel, i2$1.NgForm, i1.AutofocusDirective, i1$1.ButtonComponent, i1$1.ModalComponent, i1$1.ModalCloseDirective, i2.AsyncPipe, i1.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TenantBoxComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-tenant-box',\n      providers: [TenantBoxService],\n      template: \"@if ((service.currentTenant$ | async) || { name: '' }; as currentTenant) {\\r\\n  <div class=\\\"card shadow-sm rounded mb-3\\\">\\r\\n    <div class=\\\"card-body px-5\\\">\\r\\n      <div class=\\\"row\\\">\\r\\n        <div class=\\\"col\\\">\\r\\n          <span style=\\\"font-size: 0.8em\\\" class=\\\"text-uppercase text-muted\\\">{{\\r\\n            'AbpUiMultiTenancy::Tenant' | abpLocalization\\r\\n          }}</span\\r\\n          ><br />\\r\\n          <h6 class=\\\"m-0 d-inline-block\\\">\\r\\n            <i>{{ currentTenant.name || ('AbpUiMultiTenancy::NotSelected' | abpLocalization) }}</i>\\r\\n          </h6>\\r\\n        </div>\\r\\n        <div class=\\\"col-auto\\\">\\r\\n          <a\\r\\n            id=\\\"AbpTenantSwitchLink\\\"\\r\\n            href=\\\"javascript:void(0);\\\"\\r\\n            class=\\\"btn btn-sm mt-3 btn-outline-primary\\\"\\r\\n            (click)=\\\"service.onSwitch()\\\"\\r\\n            >{{ 'AbpUiMultiTenancy::Switch' | abpLocalization }}</a\\r\\n          >\\r\\n        </div>\\r\\n      </div>\\r\\n    </div>\\r\\n  </div>\\r\\n\\r\\n  <abp-modal [(visible)]=\\\"service.isModalVisible\\\" [busy]=\\\"service.modalBusy\\\">\\r\\n    <ng-template #abpHeader>\\r\\n      <h5>Switch Tenant</h5>\\r\\n    </ng-template>\\r\\n    <ng-template #abpBody>\\r\\n      <form (ngSubmit)=\\\"service.save()\\\">\\r\\n        <div class=\\\"mt-2\\\">\\r\\n          <div class=\\\"mb-3 form-group\\\">\\r\\n            <label for=\\\"name\\\" class=\\\"form-label\\\">{{\\r\\n              'AbpUiMultiTenancy::Name' | abpLocalization\\r\\n            }}</label>\\r\\n            <input\\r\\n              [(ngModel)]=\\\"service.name\\\"\\r\\n              type=\\\"text\\\"\\r\\n              id=\\\"name\\\"\\r\\n              name=\\\"tenant\\\"\\r\\n              class=\\\"form-control\\\"\\r\\n              autofocus\\r\\n            />\\r\\n          </div>\\r\\n          <p>{{ 'AbpUiMultiTenancy::SwitchTenantHint' | abpLocalization }}</p>\\r\\n        </div>\\r\\n      </form>\\r\\n    </ng-template>\\r\\n    <ng-template #abpFooter>\\r\\n      <button abpClose type=\\\"button\\\" class=\\\"btn btn-outline-primary\\\">\\r\\n        {{ 'AbpTenantManagement::Cancel' | abpLocalization }}\\r\\n      </button>\\r\\n      <abp-button\\r\\n        type=\\\"abp-button\\\"\\r\\n        iconClass=\\\"fa fa-check\\\"\\r\\n        (click)=\\\"service.save()\\\"\\r\\n        [disabled]=\\\"currentTenant?.name === service.name\\\"\\r\\n      >\\r\\n        <span>{{ 'AbpTenantManagement::Save' | abpLocalization }}</span>\\r\\n      </abp-button>\\r\\n    </ng-template>\\r\\n  </abp-modal>\\r\\n}\\r\\n\"\n    }]\n  }], () => [{\n    type: i1$2.TenantBoxService\n  }], null);\n})();\nclass AuthWrapperComponent {\n  constructor(service) {\n    this.service = service;\n  }\n  static {\n    this.ɵfac = function AuthWrapperComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthWrapperComponent)(i0.ɵɵdirectiveInject(i1$2.AuthWrapperService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AuthWrapperComponent,\n      selectors: [[\"abp-auth-wrapper\"]],\n      features: [i0.ɵɵProvidersFeature([AuthWrapperService])],\n      ngContentSelectors: _c5,\n      decls: 8,\n      vars: 6,\n      consts: [[1, \"row\"], [1, \"mx-auto\", \"col\", \"col-md-5\"], [1, \"abp-account-container\"], [1, \"card\", \"mt-3\", \"shadow-sm\", \"rounded\"], [1, \"alert\", \"alert-warning\"], [4, \"abpReplaceableTemplate\"], [1, \"card-body\", \"p-5\"]],\n      template: function AuthWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, AuthWrapperComponent_Conditional_2_Template, 1, 3, \"abp-tenant-box\");\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementStart(4, \"div\", 2);\n          i0.ɵɵtemplate(5, AuthWrapperComponent_Conditional_5_Template, 3, 0, \"div\", 3);\n          i0.ɵɵpipe(6, \"async\");\n          i0.ɵɵtemplate(7, AuthWrapperComponent_Conditional_7_Template, 6, 6, \"div\", 4);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(i0.ɵɵpipeBind1(3, 2, ctx.service.isMultiTenancyEnabled$) && ctx.service.isTenantBoxVisible ? 2 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(i0.ɵɵpipeBind1(6, 4, ctx.service.enableLocalLogin$) ? 5 : 7);\n        }\n      },\n      dependencies: [i1.ReplaceableTemplateDirective, TenantBoxComponent, i2.AsyncPipe, i1.LocalizationPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthWrapperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-auth-wrapper',\n      providers: [AuthWrapperService],\n      template: \"<div class=\\\"row\\\">\\r\\n  <div class=\\\"mx-auto col col-md-5\\\">\\r\\n    @if ((service.isMultiTenancyEnabled$ | async) && service.isTenantBoxVisible) {\\r\\n      <abp-tenant-box\\r\\n        *abpReplaceableTemplate=\\\"{ componentKey: service.tenantBoxKey }\\\"\\r\\n      ></abp-tenant-box>\\r\\n    }\\r\\n\\r\\n    <div class=\\\"abp-account-container\\\">\\r\\n      @if (service.enableLocalLogin$ | async) {\\r\\n        <div class=\\\"card mt-3 shadow-sm rounded\\\">\\r\\n          <div class=\\\"card-body p-5\\\">\\r\\n            <ng-content></ng-content>\\r\\n          </div>\\r\\n        </div>\\r\\n      } @else {\\r\\n        <div class=\\\"alert alert-warning\\\">\\r\\n          <strong>{{ 'AbpAccount::InvalidLoginRequest' | abpLocalization }}</strong>\\r\\n          {{ 'AbpAccount::ThereAreNoLoginSchemesConfiguredForThisClient' | abpLocalization }}\\r\\n        </div>\\r\\n      }\\r\\n    </div>\\r\\n  </div>\\r\\n</div>\\r\\n\"\n    }]\n  }], () => [{\n    type: i1$2.AuthWrapperService\n  }], null);\n})();\nclass AccountLayoutComponent {\n  // required for dynamic component\n  static {\n    this.type = \"account\" /* eLayoutType.account */;\n  }\n  constructor(service) {\n    this.service = service;\n    this.authWrapperKey = 'Account.AuthWrapperComponent';\n  }\n  ngAfterViewInit() {\n    this.service.subscribeWindowSize();\n  }\n  static {\n    this.ɵfac = function AccountLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AccountLayoutComponent)(i0.ɵɵdirectiveInject(LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AccountLayoutComponent,\n      selectors: [[\"abp-layout-account\"]],\n      features: [i0.ɵɵProvidersFeature([LayoutService, SubscriptionService])],\n      decls: 13,\n      vars: 11,\n      consts: [[\"navigations\", \"\"], [\"outlet\", \"outlet\"], [\"id\", \"main-navbar\", 1, \"navbar\", \"navbar-expand-lg\", \"navbar-dark\", \"bg-dark\", \"shadow-sm\", \"flex-column\", \"flex-md-row\", \"mb-4\", 2, \"min-height\", \"4rem\"], [1, \"container\"], [4, \"abpReplaceableTemplate\"], [\"type\", \"button\", 1, \"navbar-toggler\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [\"id\", \"main-navbar-collapse\", 1, \"navbar-collapse\"], [4, \"ngTemplateOutlet\"], [\"class\", \"mx-auto\", 3, \"smallScreen\", 4, \"abpReplaceableTemplate\"], [1, \"mx-auto\", 3, \"smallScreen\"]],\n      template: function AccountLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nav\", 2)(1, \"div\", 3);\n          i0.ɵɵtemplate(2, AccountLayoutComponent_abp_logo_2_Template, 1, 0, \"abp-logo\", 4);\n          i0.ɵɵelementStart(3, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function AccountLayoutComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.service.isCollapsed = !ctx.service.isCollapsed);\n          });\n          i0.ɵɵelement(4, \"span\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 7);\n          i0.ɵɵtemplate(6, AccountLayoutComponent_ng_container_6_Template, 1, 0, \"ng-container\", 8)(7, AccountLayoutComponent_Conditional_7_Template, 2, 2, \"div\")(8, AccountLayoutComponent_ng_template_8_Template, 2, 11, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 3);\n          i0.ɵɵelement(11, \"abp-page-alert-container\");\n          i0.ɵɵtemplate(12, AccountLayoutComponent_abp_auth_wrapper_12_Template, 3, 0, \"abp-auth-wrapper\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const navigations_r3 = i0.ɵɵreference(9);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction1(7, _c6, ctx.service.logoComponentKey));\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-expanded\", !ctx.service.isCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"overflow-hidden\", ctx.service.smallScreen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", !ctx.service.smallScreen ? navigations_r3 : null);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.service.smallScreen ? 7 : -1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction1(9, _c6, ctx.authWrapperKey));\n        }\n      },\n      dependencies: [i2.NgTemplateOutlet, i3.RouterOutlet, i1.ReplaceableTemplateDirective, LogoComponent, NavItemsComponent, RoutesComponent, PageAlertContainerComponent, AuthWrapperComponent],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccountLayoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-layout-account',\n      providers: [LayoutService, SubscriptionService],\n      template: \"<nav\\r\\n  class=\\\"navbar navbar-expand-lg navbar-dark bg-dark shadow-sm flex-column flex-md-row mb-4\\\"\\r\\n  id=\\\"main-navbar\\\"\\r\\n  style=\\\"min-height: 4rem\\\"\\r\\n>\\r\\n  <div class=\\\"container\\\">\\r\\n    <abp-logo *abpReplaceableTemplate=\\\"{ componentKey: service.logoComponentKey }\\\"></abp-logo>\\r\\n    <button\\r\\n      class=\\\"navbar-toggler\\\"\\r\\n      type=\\\"button\\\"\\r\\n      [attr.aria-expanded]=\\\"!service.isCollapsed\\\"\\r\\n      (click)=\\\"service.isCollapsed = !service.isCollapsed\\\"\\r\\n    >\\r\\n      <span class=\\\"navbar-toggler-icon\\\"></span>\\r\\n    </button>\\r\\n    <div\\r\\n      class=\\\"navbar-collapse\\\"\\r\\n      [class.overflow-hidden]=\\\"service.smallScreen\\\"\\r\\n      id=\\\"main-navbar-collapse\\\"\\r\\n    >\\r\\n      <ng-container *ngTemplateOutlet=\\\"!service.smallScreen ? navigations : null\\\"></ng-container>\\r\\n\\r\\n      @if (service.smallScreen) {\\r\\n        <div\\r\\n          [@collapseWithMargin]=\\\"service.isCollapsed ? 'collapsed' : 'expanded'\\\"\\r\\n        >\\r\\n          <ng-container *ngTemplateOutlet=\\\"navigations\\\"></ng-container>\\r\\n        </div>\\r\\n      }\\r\\n\\r\\n      <ng-template #navigations>\\r\\n        <abp-routes\\r\\n          *abpReplaceableTemplate=\\\"{\\r\\n            componentKey: service.routesComponentKey,\\r\\n            inputs: {\\r\\n              smallScreen: { value: service.smallScreen }\\r\\n            }\\r\\n          }\\\"\\r\\n          class=\\\"mx-auto\\\"\\r\\n          [smallScreen]=\\\"service.smallScreen\\\"\\r\\n        ></abp-routes>\\r\\n\\r\\n        <abp-nav-items\\r\\n          *abpReplaceableTemplate=\\\"{\\r\\n            componentKey: service.navItemsComponentKey\\r\\n          }\\\"\\r\\n        ></abp-nav-items>\\r\\n      </ng-template>\\r\\n    </div>\\r\\n  </div>\\r\\n</nav>\\r\\n\\r\\n<!-- [@slideFromBottom]=\\\"outlet.isActivated && outlet.activatedRoute?.routeConfig?.path\\\" TODO: throws ExpressionChangedAfterItHasBeenCheck when animation is active. It should be fixed -->\\r\\n<div class=\\\"container\\\">\\r\\n  <abp-page-alert-container></abp-page-alert-container>\\r\\n  <abp-auth-wrapper\\r\\n    *abpReplaceableTemplate=\\\"{\\r\\n      componentKey: authWrapperKey\\r\\n    }\\\"\\r\\n  >\\r\\n    <router-outlet #outlet=\\\"outlet\\\"></router-outlet>\\r\\n  </abp-auth-wrapper>\\r\\n</div>\\r\\n\"\n    }]\n  }], () => [{\n    type: LayoutService\n  }], null);\n})();\nclass ApplicationLayoutComponent {\n  constructor() {\n    this.service = inject(LayoutService);\n  }\n  // required for dynamic component\n  static {\n    this.type = \"application\" /* eLayoutType.application */;\n  }\n  ngAfterViewInit() {\n    this.service.subscribeWindowSize();\n  }\n  static {\n    this.ɵfac = function ApplicationLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApplicationLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ApplicationLayoutComponent,\n      selectors: [[\"abp-layout-application\"]],\n      features: [i0.ɵɵProvidersFeature([LayoutService, SubscriptionService])],\n      decls: 14,\n      vars: 8,\n      consts: [[\"navigations\", \"\"], [\"outlet\", \"outlet\"], [\"id\", \"main-navbar\", 1, \"navbar\", \"navbar-expand-lg\", \"navbar-dark\", \"bg-dark\", \"shadow-sm\", \"flex-column\", \"flex-md-row\", \"mb-4\", 2, \"min-height\", \"4rem\"], [1, \"container\"], [4, \"abpReplaceableTemplate\"], [\"type\", \"button\", 1, \"navbar-toggler\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [\"id\", \"main-navbar-collapse\", 1, \"navbar-collapse\"], [4, \"ngTemplateOutlet\"], [\"class\", \"mx-auto\", 3, \"smallScreen\", 4, \"abpReplaceableTemplate\"], [1, \"mx-auto\", 3, \"smallScreen\"]],\n      template: function ApplicationLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nav\", 2)(1, \"div\", 3);\n          i0.ɵɵtemplate(2, ApplicationLayoutComponent_abp_logo_2_Template, 1, 0, \"abp-logo\", 4);\n          i0.ɵɵelementStart(3, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ApplicationLayoutComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.service.isCollapsed = !ctx.service.isCollapsed);\n          });\n          i0.ɵɵelement(4, \"span\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 7);\n          i0.ɵɵtemplate(6, ApplicationLayoutComponent_ng_container_6_Template, 1, 0, \"ng-container\", 8)(7, ApplicationLayoutComponent_Conditional_7_Template, 2, 2, \"div\")(8, ApplicationLayoutComponent_ng_template_8_Template, 2, 11, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 3);\n          i0.ɵɵelement(11, \"abp-page-alert-container\")(12, \"router-outlet\", null, 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const navigations_r3 = i0.ɵɵreference(9);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"abpReplaceableTemplate\", i0.ɵɵpureFunction1(6, _c6, ctx.service.logoComponentKey));\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-expanded\", !ctx.service.isCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"overflow-hidden\", ctx.service.smallScreen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", !ctx.service.smallScreen ? navigations_r3 : null);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.service.smallScreen ? 7 : -1);\n        }\n      },\n      dependencies: [i2.NgTemplateOutlet, i3.RouterOutlet, i1.ReplaceableTemplateDirective, LogoComponent, NavItemsComponent, RoutesComponent, PageAlertContainerComponent],\n      encapsulation: 2,\n      data: {\n        animation: [slideFromBottom, collapseWithMargin]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ApplicationLayoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-layout-application',\n      animations: [slideFromBottom, collapseWithMargin],\n      providers: [LayoutService, SubscriptionService],\n      template: \"<nav\\r\\n  class=\\\"navbar navbar-expand-lg navbar-dark bg-dark shadow-sm flex-column flex-md-row mb-4\\\"\\r\\n  id=\\\"main-navbar\\\"\\r\\n  style=\\\"min-height: 4rem\\\"\\r\\n>\\r\\n  <div class=\\\"container\\\">\\r\\n    <abp-logo *abpReplaceableTemplate=\\\"{ componentKey: service.logoComponentKey }\\\"></abp-logo>\\r\\n    <button\\r\\n      class=\\\"navbar-toggler\\\"\\r\\n      type=\\\"button\\\"\\r\\n      [attr.aria-expanded]=\\\"!service.isCollapsed\\\"\\r\\n      (click)=\\\"service.isCollapsed = !service.isCollapsed\\\"\\r\\n    >\\r\\n      <span class=\\\"navbar-toggler-icon\\\"></span>\\r\\n    </button>\\r\\n    <div\\r\\n      class=\\\"navbar-collapse\\\"\\r\\n      [class.overflow-hidden]=\\\"service.smallScreen\\\"\\r\\n      id=\\\"main-navbar-collapse\\\"\\r\\n    >\\r\\n      <ng-container *ngTemplateOutlet=\\\"!service.smallScreen ? navigations : null\\\"></ng-container>\\r\\n\\r\\n      @if (service.smallScreen) {\\r\\n        <div [@collapseWithMargin]=\\\"service.isCollapsed ? 'collapsed' : 'expanded'\\\">\\r\\n          <ng-container *ngTemplateOutlet=\\\"navigations\\\"></ng-container>\\r\\n        </div>\\r\\n      }\\r\\n\\r\\n      <ng-template #navigations>\\r\\n        <abp-routes\\r\\n          *abpReplaceableTemplate=\\\"{\\r\\n            componentKey: service.routesComponentKey,\\r\\n            inputs: {\\r\\n              smallScreen: { value: service.smallScreen }\\r\\n            }\\r\\n          }\\\"\\r\\n          class=\\\"mx-auto\\\"\\r\\n          [smallScreen]=\\\"service.smallScreen\\\"\\r\\n        ></abp-routes>\\r\\n\\r\\n        <abp-nav-items\\r\\n          *abpReplaceableTemplate=\\\"{\\r\\n            componentKey: service.navItemsComponentKey\\r\\n          }\\\"\\r\\n        ></abp-nav-items>\\r\\n      </ng-template>\\r\\n    </div>\\r\\n  </div>\\r\\n</nav>\\r\\n\\r\\n<!-- [@slideFromBottom]=\\\"outlet.isActivated && outlet.activatedRoute?.routeConfig?.path\\\" TODO: throws ExpressionChangedAfterItHasBeenCheck when animation is active. It should be fixed -->\\r\\n<div class=\\\"container\\\">\\r\\n  <abp-page-alert-container></abp-page-alert-container>\\r\\n\\r\\n  <router-outlet #outlet=\\\"outlet\\\"></router-outlet>\\r\\n</div>\\r\\n\"\n    }]\n  }], null, null);\n})();\nclass EmptyLayoutComponent {\n  static {\n    this.type = \"empty\" /* eLayoutType.empty */;\n  }\n  static {\n    this.ɵfac = function EmptyLayoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EmptyLayoutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: EmptyLayoutComponent,\n      selectors: [[\"abp-layout-empty\"]],\n      decls: 1,\n      vars: 0,\n      template: function EmptyLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EmptyLayoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-layout-empty',\n      template: `\n    <router-outlet></router-outlet>\n  `\n    }]\n  }], null, null);\n})();\nclass CurrentUserComponent {\n  get smallScreen() {\n    return window.innerWidth < 992;\n  }\n  constructor(navigateToManageProfile, userMenu, authService, configState, sessionState) {\n    this.navigateToManageProfile = navigateToManageProfile;\n    this.userMenu = userMenu;\n    this.authService = authService;\n    this.configState = configState;\n    this.sessionState = sessionState;\n    this.currentUser$ = this.configState.getOne$('currentUser');\n    this.selectedTenant$ = this.sessionState.getTenant$();\n    this.trackByFn = (_, element) => element.id;\n  }\n  navigateToLogin() {\n    this.authService.navigateToLogin();\n  }\n  logout() {\n    this.authService.logout().subscribe();\n  }\n  static {\n    this.ɵfac = function CurrentUserComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CurrentUserComponent)(i0.ɵɵdirectiveInject(NAVIGATE_TO_MANAGE_PROFILE), i0.ɵɵdirectiveInject(i1$1.UserMenuService), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1.ConfigStateService), i0.ɵɵdirectiveInject(i1.SessionStateService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CurrentUserComponent,\n      selectors: [[\"abp-current-user\"]],\n      decls: 3,\n      vars: 3,\n      consts: [[\"currentUserDropdown\", \"ngbDropdown\"], [\"ngbDropdown\", \"\", \"display\", \"static\", 1, \"dropdown\"], [\"role\", \"button\", 1, \"nav-link\", \"pointer\"], [\"ngbDropdownToggle\", \"\", \"href\", \"javascript:void(0)\", \"role\", \"button\", \"id\", \"dropdownMenuLink\", \"data-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"nav-link\"], [\"aria-labelledby\", \"dropdownMenuLink\", 1, \"dropdown-menu\", \"dropdown-menu-end\", \"border-0\", \"shadow-sm\"], [4, \"abpVisible\"], [\"class\", \"nav-item d-flex align-items-center\", 4, \"abpPermission\"], [1, \"nav-item\", \"d-flex\", \"align-items-center\"], [3, \"ngComponentOutlet\", \"ngComponentOutletInjector\"], [3, \"innerHTML\"], [3, \"click\", \"innerHTML\"], [1, \"dropdown-item\", \"pointer\"], [1, \"dropdown-item\", \"pointer\", 3, \"click\"], [3, \"class\"], [\"role\", \"button\", 1, \"nav-link\", \"pointer\", 3, \"click\"]],\n      template: function CurrentUserComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CurrentUserComponent_Conditional_0_Template, 12, 10, \"div\", 1);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, CurrentUserComponent_Conditional_2_Template, 3, 3, \"a\", 2);\n        }\n        if (rf & 2) {\n          let tmp_0_0;\n          i0.ɵɵconditional(((tmp_0_0 = i0.ɵɵpipeBind1(1, 1, ctx.currentUser$)) == null ? null : tmp_0_0.isAuthenticated) ? 0 : 2);\n        }\n      },\n      dependencies: [i2.NgComponentOutlet, i1.PermissionDirective, i1$1.AbpVisibleDirective, i4.NgbDropdown, i4.NgbDropdownToggle, i2.AsyncPipe, i1.LocalizationPipe, i1.ToInjectorPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CurrentUserComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-current-user',\n      template: \"@if ((currentUser$ | async)?.isAuthenticated) {\\r\\n  <div\\r\\n    ngbDropdown\\r\\n    class=\\\"dropdown\\\"\\r\\n    #currentUserDropdown=\\\"ngbDropdown\\\"\\r\\n    display=\\\"static\\\"\\r\\n  >\\r\\n    <a\\r\\n      ngbDropdownToggle\\r\\n      class=\\\"nav-link\\\"\\r\\n      href=\\\"javascript:void(0)\\\"\\r\\n      role=\\\"button\\\"\\r\\n      id=\\\"dropdownMenuLink\\\"\\r\\n      data-toggle=\\\"dropdown\\\"\\r\\n      aria-haspopup=\\\"true\\\"\\r\\n      aria-expanded=\\\"false\\\"\\r\\n    >\\r\\n      @if ((selectedTenant$ | async)?.name; as tenantName) {\\r\\n        <small\\r\\n          ><i>{{ tenantName }}</i\\r\\n          >\\\\</small\\r\\n        >\\r\\n      }\\r\\n      <strong>{{ (currentUser$ | async)?.userName }}</strong>\\r\\n    </a>\\r\\n    <div\\r\\n      class=\\\"dropdown-menu dropdown-menu-end border-0 shadow-sm\\\"\\r\\n      aria-labelledby=\\\"dropdownMenuLink\\\"\\r\\n      [class.d-block]=\\\"smallScreen && currentUserDropdown.isOpen()\\\"\\r\\n    >\\r\\n      @for (item of userMenu.items$ | async; track $index) {\\r\\n        <ng-container *abpVisible=\\\"!item.visible || item.visible(item)\\\">\\r\\n          <li class=\\\"nav-item d-flex align-items-center\\\" *abpPermission=\\\"item.requiredPolicy\\\">\\r\\n            @if (item.component) {\\r\\n              <ng-container\\r\\n                [ngComponentOutlet]=\\\"item.component\\\"\\r\\n                [ngComponentOutletInjector]=\\\"item | toInjector\\\"\\r\\n              ></ng-container>\\r\\n            } @else {\\r\\n              @if (item.html) {\\r\\n                <div [innerHTML]=\\\"item.html\\\" (click)=\\\"item.action ? item.action() : null\\\"></div>\\r\\n              } @else {\\r\\n                @if (item.textTemplate) {\\r\\n                  <a (click)=\\\"item.action ? item.action() : null\\\" class=\\\"dropdown-item pointer\\\">\\r\\n                    @if (item.textTemplate.icon) {\\r\\n                      <i class=\\\"me-1 {{ item.textTemplate.icon }}\\\"></i>\\r\\n                    }\\r\\n                    {{ item.textTemplate.text | abpLocalization }}</a\\r\\n                  >\\r\\n                }\\r\\n              }\\r\\n            }\\r\\n          </li>\\r\\n        </ng-container>\\r\\n      }\\r\\n    </div>\\r\\n  </div>\\r\\n} @else {\\r\\n    <a role=\\\"button\\\" class=\\\"nav-link pointer\\\" (click)=\\\"navigateToLogin()\\\">\\r\\n      {{ 'AbpAccount::Login' | abpLocalization }}\\r\\n    </a>\\r\\n}\\r\\n\"\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NAVIGATE_TO_MANAGE_PROFILE]\n    }]\n  }, {\n    type: i1$1.UserMenuService\n  }, {\n    type: i1.AuthService\n  }, {\n    type: i1.ConfigStateService\n  }, {\n    type: i1.SessionStateService\n  }], null);\n})();\nclass LanguagesComponent {\n  get smallScreen() {\n    return window.innerWidth < 992;\n  }\n  get defaultLanguage$() {\n    return this.languages$.pipe(map(languages => languages?.find(lang => lang.cultureName === this.selectedLangCulture)?.displayName || ''));\n  }\n  get dropdownLanguages$() {\n    return this.languages$.pipe(map(languages => languages?.filter(lang => lang.cultureName !== this.selectedLangCulture) || []));\n  }\n  get selectedLangCulture() {\n    return this.sessionState.getLanguage();\n  }\n  constructor(sessionState, configState) {\n    this.sessionState = sessionState;\n    this.configState = configState;\n    this.languages$ = this.configState.getDeep$('localization.languages');\n  }\n  onChangeLang(cultureName) {\n    this.sessionState.setLanguage(cultureName);\n  }\n  static {\n    this.ɵfac = function LanguagesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LanguagesComponent)(i0.ɵɵdirectiveInject(i1.SessionStateService), i0.ɵɵdirectiveInject(i1.ConfigStateService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: LanguagesComponent,\n      selectors: [[\"abp-languages\"]],\n      decls: 2,\n      vars: 3,\n      consts: [[\"languageDropdown\", \"ngbDropdown\"], [\"ngbDropdown\", \"\", \"display\", \"static\", 1, \"dropdown\"], [\"ngbDropdownToggle\", \"\", \"href\", \"javascript:void(0)\", \"role\", \"button\", \"id\", \"dropdownMenuLink\", \"data-toggle\", \"dropdown\", \"aria-haspopup\", \"true\", \"aria-expanded\", \"false\", 1, \"nav-link\"], [\"aria-labelledby\", \"dropdownMenuLink\", 1, \"dropdown-menu\", \"dropdown-menu-end\", \"border-0\", \"shadow-sm\"], [\"href\", \"javascript:void(0)\", 1, \"dropdown-item\"], [\"href\", \"javascript:void(0)\", 1, \"dropdown-item\", 3, \"click\"]],\n      template: function LanguagesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LanguagesComponent_Conditional_0_Template, 9, 7, \"div\", 1);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          let tmp_0_0;\n          i0.ɵɵconditional((((tmp_0_0 = i0.ɵɵpipeBind1(1, 1, ctx.dropdownLanguages$)) == null ? null : tmp_0_0.length) || 0) > 0 ? 0 : -1);\n        }\n      },\n      dependencies: [i4.NgbDropdown, i4.NgbDropdownToggle, i2.AsyncPipe],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LanguagesComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-languages',\n      template: `\n    @if (((dropdownLanguages$ | async)?.length || 0) > 0) {\n      <div class=\"dropdown\" ngbDropdown #languageDropdown=\"ngbDropdown\" display=\"static\">\n        <a\n          ngbDropdownToggle\n          class=\"nav-link\"\n          href=\"javascript:void(0)\"\n          role=\"button\"\n          id=\"dropdownMenuLink\"\n          data-toggle=\"dropdown\"\n          aria-haspopup=\"true\"\n          aria-expanded=\"false\"\n        >\n          {{ defaultLanguage$ | async }}\n        </a>\n        <div\n          class=\"dropdown-menu dropdown-menu-end border-0 shadow-sm\"\n          aria-labelledby=\"dropdownMenuLink\"\n          [class.d-block]=\"smallScreen && languageDropdown.isOpen()\"\n        >\n          @for (lang of dropdownLanguages$ | async; track $index) {\n            <a\n              href=\"javascript:void(0)\"\n              class=\"dropdown-item\"\n              (click)=\"onChangeLang(lang.cultureName || '')\"\n              >{{ lang?.displayName }}</a\n            >\n          }\n        </div>\n      </div>\n    }\n  `\n    }]\n  }], () => [{\n    type: i1.SessionStateService\n  }, {\n    type: i1.ConfigStateService\n  }], null);\n})();\nclass ValidationErrorComponent extends ValidationErrorComponent$1 {\n  get abpErrors() {\n    if (!this.errors || !this.errors.length) return [];\n    return this.errors.map(error => {\n      if (!error.message) return error;\n      const index = error.message.indexOf('[');\n      if (index > -1) {\n        return {\n          ...error,\n          message: error.message.slice(0, index),\n          interpoliteParams: error.message.slice(index + 1, error.message.length - 1).split(',')\n        };\n      }\n      return error;\n    });\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵValidationErrorComponent_BaseFactory;\n      return function ValidationErrorComponent_Factory(__ngFactoryType__) {\n        return (ɵValidationErrorComponent_BaseFactory || (ɵValidationErrorComponent_BaseFactory = i0.ɵɵgetInheritedFactory(ValidationErrorComponent)))(__ngFactoryType__ || ValidationErrorComponent);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ValidationErrorComponent,\n      selectors: [[\"abp-validation-error\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"invalid-feedback\"]],\n      template: function ValidationErrorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, ValidationErrorComponent_For_1_Template, 3, 4, \"div\", 0, i0.ɵɵrepeaterTrackByIndex);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx.abpErrors);\n        }\n      },\n      dependencies: [i1.LocalizationPipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ValidationErrorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'abp-validation-error',\n      template: `\n    @for (error of abpErrors; track $index) {\n      <div class=\"invalid-feedback\" >\n        {{ error.message | abpLocalization: error.interpoliteParams }}\n      </div>\n    }\n  `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, null);\n})();\nconst LAZY_STYLES = new InjectionToken('LAZY_STYLES');\nconst BOOTSTRAP = 'bootstrap-{{dir}}.min.css';\nclass LazyStyleHandler {\n  set dir(dir) {\n    if (dir === this._dir) return;\n    this.switchCSS(dir);\n    this._dir = dir;\n  }\n  get dir() {\n    return this._dir;\n  }\n  constructor(injector) {\n    this._dir = 'ltr';\n    this.loaded = new Map();\n    this.setStyles(injector);\n    this.setLazyLoad(injector);\n    this.listenToDirectionChanges(injector);\n  }\n  getHrefFromLink(link) {\n    if (!link) return '';\n    const a = document.createElement('a');\n    a.href = link.href;\n    return a.pathname.replace(/^\\//, '');\n  }\n  getLoadedBootstrap() {\n    const href = createLazyStyleHref(BOOTSTRAP, this.dir);\n    const selector = `[href*=\"${href.replace(/\\.css$/, '')}\"]`;\n    const link = document.querySelector(selector);\n    return {\n      href,\n      link\n    };\n  }\n  listenToDirectionChanges(injector) {\n    const docDirHandler = injector.get(DocumentDirHandlerService);\n    // will always listen, no need to unsubscribe\n    docDirHandler.dir$.subscribe(dir => {\n      this.dir = dir;\n    });\n  }\n  setLazyLoad(injector) {\n    this.lazyLoad = injector.get(LazyLoadService);\n    const {\n      href,\n      link\n    } = this.getLoadedBootstrap();\n    this.lazyLoad.loaded.set(href, link);\n  }\n  setStyles(injector) {\n    this.styles = injector.get(LAZY_STYLES, [BOOTSTRAP]);\n  }\n  switchCSS(dir) {\n    this.styles.forEach(style => {\n      const oldHref = createLazyStyleHref(style, this.dir);\n      const newHref = createLazyStyleHref(style, dir);\n      const link = this.loaded.get(newHref);\n      const href = this.getHrefFromLink(link) || newHref;\n      const strategy = LOADING_STRATEGY.PrependAnonymousStyleToHead(href);\n      this.lazyLoad.load(strategy).subscribe(() => {\n        const oldLink = this.lazyLoad.loaded.get(oldHref);\n        this.loaded.delete(newHref);\n        this.loaded.set(oldHref, oldLink);\n        const newLink = this.lazyLoad.loaded.get(href);\n        this.lazyLoad.loaded.delete(href);\n        this.lazyLoad.loaded.set(newHref, newLink);\n        this.lazyLoad.remove(oldHref);\n      });\n    });\n  }\n  static {\n    this.ɵfac = function LazyStyleHandler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LazyStyleHandler)(i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LazyStyleHandler,\n      factory: LazyStyleHandler.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LazyStyleHandler, [{\n    type: Injectable\n  }], () => [{\n    type: i0.Injector\n  }], null);\n})();\nfunction createLazyStyleHref(style, dir) {\n  return style.replace(/{{\\s*dir\\s*}}/g, dir);\n}\nfunction initLazyStyleHandler(injector) {\n  return () => new LazyStyleHandler(injector);\n}\nconst BASIC_THEME_NAV_ITEM_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureNavItems,\n  deps: [NavItemsService],\n  multi: true\n}];\nfunction configureNavItems(navItems) {\n  return () => {\n    navItems.addItems([{\n      id: \"Theme.LanguagesComponent\" /* eThemeBasicComponents.Languages */,\n      order: 100,\n      component: LanguagesComponent\n    }, {\n      id: \"Theme.CurrentUserComponent\" /* eThemeBasicComponents.CurrentUser */,\n      order: 100,\n      component: CurrentUserComponent\n    }]);\n  };\n}\nvar styles = `\n.content-header-title {\n    font-size: 24px;\n}\n.entry-row {\n    margin-bottom: 15px;\n}\n#main-navbar-tools a.dropdown-toggle {\n    text-decoration: none;\n    color: #fff;\n}\n.navbar .dropdown-submenu {\n    position: relative;\n}\n.navbar .dropdown-menu {\n    margin: 0;\n    padding: 0;\n}\n.navbar .dropdown-menu a {\n    font-size: .9em;\n    padding: 10px 15px;\n    display: block;\n    min-width: 210px;\n    text-align: left;\n    border-radius: 0.25rem;\n    min-height: 44px;\n}\n[dir=rtl] .navbar .dropdown-menu a {\n    text-align: right!important;\n}\n.navbar .dropdown-submenu a::after {\n    transform: rotate(-90deg);\n    position: absolute;\n    right: 16px;\n    top: 18px;\n}\n[dir=rtl] .navbar .dropdown-submenu a::after {\n    transform: rotate(90deg);\n    left: 16px;\n    right: auto;\n    top: 20px;\n}\n.navbar .dropdown-submenu .dropdown-menu {\n    top: 0;\n    left: 100%;\n}\n/* work around for rtl. Track https://github.com/ng-bootstrap/ng-bootstrap/issues/4100 issue */\n[dir=rtl] .navbar .dropdown-submenu .dropdown-menu {\n    top: 0;\n    right: 100%;\n}\n.card-header .btn {\n    padding: 2px 6px;\n}\n.card-header h5 {\n    margin: 0;\n}\n.container > .card {\n    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;\n}\n@media screen and (min-width: 992px) {\n    .navbar .dropdown:hover > .dropdown-menu {\n        display: block;\n    }\n\n    .navbar .dropdown-submenu:hover > .dropdown-menu {\n        display: block;\n    }\n}\n.input-validation-error {\n    border-color: #dc3545;\n}\n.field-validation-error {\n    font-size: 0.8em;\n}\n.ui-table .ui-table-tbody > tr.empty-row > div.empty-row-content {\n    border: 1px solid #c8c8c8;\n  }\n.abp-loading {\n    background: rgba(0, 0, 0, 0.05);\n}\n.modal-backdrop {\nbackground-color: rgba(0, 0, 0, 0.6);\n}\n\n.confirmation .confirmation-backdrop {\n\t background: rgba(0, 0, 0, 0.7) !important;\n}\n .confirmation .confirmation-dialog {\n\t border: none;\n\t border-radius: 10px;\n\t background-color: #fff;\n\t box-shadow: 0 0 10px -5px rgba(0, 0, 0, 0.5);\n}\n .confirmation .confirmation-dialog .icon-container .icon {\n\t stroke: #fff;\n\t color: #fff;\n}\n .confirmation .confirmation-dialog .icon-container.info .icon {\n\t stroke: #2f96b4;\n\t color: #2f96b4;\n}\n .confirmation .confirmation-dialog .icon-container.success .icon {\n\t stroke: #51a351;\n\t color: #51a351;\n}\n .confirmation .confirmation-dialog .icon-container.warning .icon {\n\t stroke: #f89406;\n\t color: #f89406;\n}\n .confirmation .confirmation-dialog .icon-container.error .icon {\n\t stroke: #bd362f;\n\t color: #bd362f;\n}\n .confirmation .confirmation-dialog .content .title {\n\t color: #222;\n}\n .confirmation .confirmation-dialog .content .message {\n\t color: #777;\n}\n .confirmation .confirmation-dialog .footer {\n\t background: transparent;\n}\n .confirmation .confirmation-dialog .footer .confirmation-button {\n\t background-color: #eee;\n\t color: #777;\n}\n .confirmation .confirmation-dialog .footer .confirmation-button:hover, .confirmation .confirmation-dialog .footer .confirmation-button:focus, .confirmation .confirmation-dialog .footer .confirmation-button:active {\n\t background-color: #bbb;\n}\n .confirmation .confirmation-dialog .footer .confirmation-button--confirm {\n\t background-color: #2f96b4;\n\t color: #fff;\n}\n .confirmation .confirmation-dialog .footer .confirmation-button--confirm:hover {\n\t background-color: #2e819b;\n}\n.ui-table .pagination-wrapper {\n    background-color: #f4f4f4;\n    border: 1px solid #c8c8c8;\n}\n.bordered .datatable-body-row {\n    border-top: 1px solid #eee;\n    margin-top: -1px;\n}\n.breadcrumb {\n    background-color: transparent;\n    padding: 0.27rem;\n}\n\n.abp-md-form {\n    max-width: 540px;\n  }\n`;\nconst BASIC_THEME_STYLES_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureStyles,\n  deps: [DomInsertionService, ReplaceableComponentsService],\n  multi: true\n}];\nfunction configureStyles(domInsertion, replaceableComponents) {\n  return () => {\n    domInsertion.insertContent(CONTENT_STRATEGY.AppendStyleToHead(styles));\n    initLayouts(replaceableComponents);\n  };\n}\nfunction initLayouts(replaceableComponents) {\n  replaceableComponents.add({\n    key: \"Theme.ApplicationLayoutComponent\" /* eThemeBasicComponents.ApplicationLayout */,\n    component: ApplicationLayoutComponent\n  });\n  replaceableComponents.add({\n    key: \"Theme.AccountLayoutComponent\" /* eThemeBasicComponents.AccountLayout */,\n    component: AccountLayoutComponent\n  });\n  replaceableComponents.add({\n    key: \"Theme.EmptyLayoutComponent\" /* eThemeBasicComponents.EmptyLayout */,\n    component: EmptyLayoutComponent\n  });\n}\nconst BASIC_THEME_USER_MENU_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: configureUserMenu,\n  deps: [Injector],\n  multi: true\n}];\nfunction configureUserMenu(injector) {\n  const userMenu = injector.get(UserMenuService);\n  const authService = injector.get(AuthService);\n  const navigateToManageProfile = injector.get(NAVIGATE_TO_MANAGE_PROFILE);\n  return () => {\n    userMenu.addItems([{\n      id: \"UserMenu.MyAccount\" /* eUserMenuItems.MyAccount */,\n      order: 100,\n      textTemplate: {\n        text: 'AbpAccount::MyAccount',\n        icon: 'fa fa-cog'\n      },\n      action: () => navigateToManageProfile()\n    }, {\n      id: \"UserMenu.Logout\" /* eUserMenuItems.Logout */,\n      order: 101,\n      textTemplate: {\n        text: 'AbpUi::Logout',\n        icon: 'fa fa-power-off'\n      },\n      action: () => {\n        authService.logout().subscribe();\n      }\n    }]);\n  };\n}\nfunction provideThemeBasicConfig() {\n  return makeEnvironmentProviders([BASIC_THEME_NAV_ITEM_PROVIDERS, BASIC_THEME_USER_MENU_PROVIDERS, BASIC_THEME_STYLES_PROVIDERS, {\n    provide: VALIDATION_ERROR_TEMPLATE,\n    useValue: ValidationErrorComponent\n  }, {\n    provide: VALIDATION_TARGET_SELECTOR,\n    useValue: '.form-group'\n  }, {\n    provide: VALIDATION_INVALID_CLASSES,\n    useValue: 'is-invalid'\n  }, LazyStyleHandler, {\n    provide: APP_INITIALIZER,\n    useFactory: noop,\n    multi: true,\n    deps: [LazyStyleHandler]\n  }]);\n}\nconst LAYOUTS = [ApplicationLayoutComponent, AccountLayoutComponent, EmptyLayoutComponent];\nclass BaseThemeBasicModule {\n  static {\n    this.ɵfac = function BaseThemeBasicModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseThemeBasicModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BaseThemeBasicModule,\n      declarations: [ApplicationLayoutComponent, AccountLayoutComponent, EmptyLayoutComponent, ValidationErrorComponent, LogoComponent, NavItemsComponent, RoutesComponent, CurrentUserComponent, LanguagesComponent, PageAlertContainerComponent, TenantBoxComponent, AuthWrapperComponent],\n      imports: [CoreModule, ThemeSharedModule, NgbCollapseModule, NgbDropdownModule, NgxValidateCoreModule],\n      exports: [ApplicationLayoutComponent, AccountLayoutComponent, EmptyLayoutComponent, ValidationErrorComponent, LogoComponent, NavItemsComponent, RoutesComponent, CurrentUserComponent, LanguagesComponent, PageAlertContainerComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CoreModule, ThemeSharedModule, NgbCollapseModule, NgbDropdownModule, NgxValidateCoreModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseThemeBasicModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [...LAYOUTS, ValidationErrorComponent, LogoComponent, NavItemsComponent, RoutesComponent, CurrentUserComponent, LanguagesComponent, PageAlertContainerComponent, TenantBoxComponent, AuthWrapperComponent],\n      exports: [...LAYOUTS, ValidationErrorComponent, LogoComponent, NavItemsComponent, RoutesComponent, CurrentUserComponent, LanguagesComponent, PageAlertContainerComponent],\n      imports: [CoreModule, ThemeSharedModule, NgbCollapseModule, NgbDropdownModule, NgxValidateCoreModule]\n    }]\n  }], null, null);\n})();\nclass ThemeBasicModule {\n  /**\n   * @deprecated forRoot method is deprecated, use `provideThemeBasicConfig` *function* for config settings.\n   */\n  static forRoot() {\n    return {\n      ngModule: ThemeBasicModule,\n      providers: [provideThemeBasicConfig()]\n    };\n  }\n  static {\n    this.ɵfac = function ThemeBasicModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThemeBasicModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ThemeBasicModule,\n      imports: [BaseThemeBasicModule],\n      exports: [BaseThemeBasicModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BaseThemeBasicModule, BaseThemeBasicModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThemeBasicModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BaseThemeBasicModule],\n      imports: [BaseThemeBasicModule]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of theme-basic\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AccountLayoutComponent, ApplicationLayoutComponent, AuthWrapperComponent, BASIC_THEME_NAV_ITEM_PROVIDERS, BASIC_THEME_STYLES_PROVIDERS, BASIC_THEME_USER_MENU_PROVIDERS, BOOTSTRAP, BaseThemeBasicModule, CurrentUserComponent, EmptyLayoutComponent, LAYOUTS, LAZY_STYLES, LanguagesComponent, LazyStyleHandler, LogoComponent, NavItemsComponent, PageAlertContainerComponent, RoutesComponent, TenantBoxComponent, ThemeBasicModule, ValidationErrorComponent, configureNavItems, configureStyles, configureUserMenu, createLazyStyleHref, initLazyStyleHandler, provideThemeBasicConfig };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,IAAI,oBAAoB;AACtB,WAAO,KAAK,YAAY,YAAY,8BAA8B,EAAE,KAAK,IAAI,WAAS,OAAO,YAAY,MAAM,OAAO,CAAC;AAAA,EACzH;AAAA,EACA,IAAI,oCAAoC;AACtC,WAAO,KAAK,kBAAkB,EAAE,KAAK,oBAAoB;AAAA,EAC3D;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,qCAAqC,KAAK,aAAa;AAAA,EACrE;AAAA,EACA,YAAY,cAAc,aAAa,UAAU;AAC/C,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,yBAAyB,KAAK,YAAY,SAAS,wBAAwB;AAChF,SAAK,eAAe;AACpB,SAAK,QAAQ,SAAS,IAAI,cAAc;AAAA,EAC1C;AAAA,EACA,oBAAoB;AAClB,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAI,QAAQ;AACZ,UAAM,aAAa;AACnB,WAAO,MAAM,cAAc,QAAQ,YAAY;AAC7C,cAAQ,MAAM;AACd;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,SAAY,mBAAmB,GAAM,SAAY,kBAAkB,GAAM,SAAY,QAAQ,CAAC;AAAA,IACxJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,gBAAgB,eAAe,cAAc,aAAa;AACpE,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK,aAAa,WAAW;AAAA,EACrD;AAAA,EACA,WAAW;AACT,UAAM,SAAS,KAAK,aAAa,UAAU;AAC3C,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,UAAU,IAAI;AACnB,WAAK,iBAAiB;AACtB;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,cAAc,iBAAiB,KAAK,IAAI,EAAE,KAAK,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,EAAE,UAAU,CAAC,OAIjG;AAJiG,mBACrG;AAAA;AAAA,QACA,UAAU;AAAA,MA9EhB,IA4E2G,IAGlG,mBAHkG,IAGlG;AAAA,QAFH;AAAA,QACA;AAAA;AAGA,UAAI,CAAC,SAAS;AACZ,aAAK,UAAU;AACf;AAAA,MACF;AACA,WAAK,UAAU,iCACV,SADU;AAAA,QAEb;AAAA,QACA,aAAa;AAAA,MACf,EAAC;AACD,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,UAAU,QAAQ;AAChB,SAAK,aAAa,UAAU,MAAM;AAClC,SAAK,YAAY,gBAAgB;AAAA,EACnC;AAAA,EACA,YAAY;AACV,SAAK,eAAe,MAAM,gDAAgD,gBAAgB;AAAA,MACxF,2BAA2B,CAAC,KAAK,QAAQ,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAY,cAAc,GAAM,SAAY,gBAAgB,GAAM,SAAY,mBAAmB,GAAM,SAAY,kBAAkB,CAAC;AAAA,IAC9L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;;;AC9GH,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,OAAO,QAAQ,SAAY,aAAa,EAAE,OAAO,OAAO,QAAQ,IAAI;AAAA,EAC3F;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAK,OAAO,QAAQ,MAAM,GAAG;AAAA,EACrD;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,qBAAqB,QAAQ,SAAS,EAAE,6BAAgC,YAAY,GAAG,GAAG,OAAO,CAAC;AAAA,EAClH;AACF;AACA,SAAS,mEAAmE,IAAI,KAAK;AACnF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,0FAA0F;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,aAAU,YAAY,QAAQ,SAAS,QAAQ,OAAO,IAAI,IAAI;AAAA,IAChE,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,aAAa,QAAQ,MAAS,cAAc;AAAA,EAC5D;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,oEAAoE,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,oEAAoE,GAAG,GAAG,OAAO,CAAC;AACnM,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,UAAU;AACb,IAAG,cAAc,QAAQ,YAAY,IAAI,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,MAAM,CAAC;AACpF,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,WAAW,iBAAiB,QAAQ,cAAc;AAAA,EACvD;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC3F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,cAAc,CAAC,QAAQ,WAAW,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAC1E;AACF;AACA,IAAM,MAAM,CAAC,mBAAmB;AAChC,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,IAAM,MAAM,QAAM,CAAC,EAAE;AACrB,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,SAAY,cAAc;AAChC,UAAM,iBAAoB,YAAY,CAAC;AACvC,UAAM,kBAAqB,YAAY,CAAC;AACxC,IAAG,WAAW,oBAAoB,OAAO,WAAW,QAAQ,IAAI,kBAAkB,cAAc,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,QAAQ,CAAC;AAAA,EACnK;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,EAAE;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,WAAW,WAAW,SAAS,SAAS;AAAA,EAC7C;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE;AACzC,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,KAAK,EAAE;AACzF,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,SAAS,IAAI,CAAC;AACrE,IAAG,UAAU;AACb,IAAG,cAAc,SAAS,YAAY,IAAI,EAAE;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,IAAI,GAAG,GAAG;AAAA,EACrE;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,MAAM,EAAE;AAAA,EAC9E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,WAAW,iBAAiB,SAAS,cAAc;AAAA,EACxD;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AACxF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,EAAE;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,IAAG,WAAW,WAAW,SAAS,SAAS;AAAA,EAC7C;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,SAAS,SAAS,gFAAgF;AAC9G,MAAG,cAAc,GAAG;AACpB,YAAM,WAAc,cAAc,CAAC,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,SAAS,IAAI,IAAK,OAAO,mBAAmB,SAAS,IAAI,IAAI,QAAU,OAAO,mBAAmB,SAAS,IAAI,IAAI,IAAK;AAAA,IACzK,CAAC;AACD,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,KAAK,EAAE;AACvG,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,SAAS,SAAS,+EAA+E,QAAQ;AACrH,MAAG,cAAc,GAAG;AACpB,aAAO,eAAe;AACtB,aAAU,YAAY,OAAO,gBAAgB,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,gBAAgB,EAAE;AACnH,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,CAAC,EAAE;AACrC,UAAM,SAAY,cAAc;AAChC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,SAAS,YAAY,IAAI,EAAE;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,IAAI,GAAG,GAAG;AACnE,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,WAAW,OAAO,eAAe,OAAO,mBAAmB,SAAS,IAAI,CAAC;AACxF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,cAAc,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,QAAQ,CAAC;AAAA,EACnH;AACF;AACA,SAAS,qDAAqD,IAAI,KAAK;AACrE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,2DAA2D,GAAG,IAAI,MAAM,EAAE;AAAA,EAC7F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,IAAG,WAAW,iBAAiB,SAAS,cAAc;AAAA,EACxD;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,MAAM,EAAE;AAAA,EACvF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,eAAe,SAAS,YAAY,OAAO,OAAO,SAAS,SAAS,UAAU,IAAI,EAAE;AAAA,EACzF;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAAC;AAC9E,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,eAAe,CAAC;AAAA,EACrG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,cAAc,CAAC;AAClB,UAAM,mBAAsB,YAAY,EAAE;AAC1C,UAAM,oBAAuB,YAAY,EAAE;AAC3C,IAAG,WAAW,qBAAqB,SAAS,YAAY,OAAO,OAAO,SAAS,SAAS,UAAU,oBAAoB,gBAAgB,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,QAAQ,CAAC;AAAA,EACzM;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,8CAA8C,GAAG,GAAG,MAAM,GAAM,sBAAsB;AAAA,EAC/G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,UAAU,QAAQ;AAAA,EAClC;AACF;AACA,SAAS,0EAA0E,IAAI,KAAK;AAC1F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,EAAE;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,IAAG,WAAW,WAAW,UAAU,SAAS;AAAA,EAC9C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,iBAAiB;AACjC,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE;AAC1C,IAAG,WAAW,SAAS,SAAS,iFAAiF;AAC/G,MAAG,cAAc,IAAI;AACrB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,cAAc,CAAC;AAAA,IAC9C,CAAC;AACD,IAAG,WAAW,GAAG,2EAA2E,GAAG,GAAG,KAAK,EAAE;AACzG,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,cAAiB,gBAAgB,GAAG,KAAK,UAAU,IAAI,CAAC;AACtE,IAAG,UAAU;AACb,IAAG,cAAc,UAAU,YAAY,IAAI,EAAE;AAC7C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,UAAU,IAAI,GAAG,EAAE;AAAA,EACrE;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,OAAO,EAAE;AAAA,EAC/F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,EAAE;AACrC,IAAG,WAAW,iBAAiB,UAAU,cAAc;AAAA,EACzD;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,OAAO,EAAE;AAAA,EACzF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,cAAc,UAAU,OAAO,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK,EAAE;AAAA,EACzB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,IAAG,WAAW,WAAW,UAAU,SAAS;AAAA,EAC9C;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE;AAC3D,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,KAAK,EAAE;AAC3F,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,EAAE;AACvG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,YAAe,cAAc,EAAE;AACrC,UAAM,SAAY,cAAc;AAChC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,aAAa,IAAI;AAC/B,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,mBAAmB,KAAK;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,sBAAsB,CAAC,OAAO,WAAW;AACvD,IAAG,UAAU;AACb,IAAG,cAAc,UAAU,YAAY,IAAI,EAAE;AAC7C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,IAAI,UAAU,IAAI,GAAG,GAAG;AACrE,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,WAAW,OAAO,eAAe,oBAAoB,OAAO,CAAC;AAC5E,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,cAAc,EAAE,2BAA8B,gBAAgB,IAAI,KAAK,SAAS,CAAC;AAAA,EACrH;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,+CAA+C,IAAI,IAAI,OAAO,EAAE;AAAA,EACnF;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,iBAAiB,UAAU,cAAc;AAAA,EACzD;AACF;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,+BAA+B;AACjC;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,CAAC;AACvB,IAAG,OAAO,GAAG,aAAa;AAC1B,IAAG,OAAO,GAAG,iBAAiB;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAc,cAAc,EAAE;AACpC,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAM,YAAY,GAAG,GAAG,SAAS,KAAK,GAAG,SAAS,uBAAuB,GAAM,cAAc;AAAA,EAC5I;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,mFAAmF;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,eAAkB,cAAc,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,OAAO,YAAY,CAAC;AAAA,IAC3D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,MAAM,CAAC;AACxF,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,OAAO,GAAG,aAAa;AAC1B,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,UAAU,CAAC;AAC5F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,uBAAuB,gBAAgB,SAAS,MAAM,YAAY;AACrE,IAAG,WAAW,WAAc,gBAAgB,IAAI,KAAK,SAAS,WAAW,CAAC;AAC1E,IAAG,UAAU;AACb,IAAG,cAAc,SAAS,QAAQ,IAAI,EAAE;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,aAAgB,YAAY,GAAG,GAAM,YAAY,GAAG,GAAG,SAAS,OAAO,GAAG,SAAS,yBAAyB,GAAM,cAAc;AAC9I,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,SAAS,cAAc,IAAI,EAAE;AAAA,EAChD;AACF;AACA,IAAM,MAAM,OAAO;AAAA,EACjB,MAAM;AACR;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,OAAO,GAAG,eAAe;AAC5B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,WAAW,YAAY,SAAS,oFAAoF;AACrH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,KAAK,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,GAAG,SAAS,EAAE;AAC5D,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,iBAAiB,iBAAiB,SAAS,wFAAwF,QAAQ;AAC5I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,MAAG,mBAAmB,OAAO,QAAQ,MAAM,MAAM,MAAM,OAAO,QAAQ,OAAO;AAC7E,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,GAAG;AACxB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,yBAAyB,CAAC;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,iBAAiB,WAAW,OAAO,QAAQ,IAAI;AAClD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,qCAAqC,CAAC;AAAA,EAClF;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,cAAc,EAAE;AACrC,IAAG,WAAW,SAAS,SAAS,uFAAuF;AACrH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,QAAQ,KAAK,CAAC;AAAA,IAC7C,CAAC;AACD,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,cAAc;AAC1C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,6BAA6B,GAAG,GAAG;AACnF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,aAAa,oBAAoB,OAAO,OAAO,iBAAiB,UAAU,OAAO,QAAQ,IAAI;AAC3G,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,2BAA2B,CAAC;AAAA,EACxE;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC;AAClF,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,UAAU,GAAG,IAAI;AACpB,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,GAAG;AACpC,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,eAAe,IAAI,OAAO,CAAC,EAAE,IAAI,KAAK,EAAE;AAC3C,IAAG,WAAW,SAAS,SAAS,gEAAgE;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,OAAO,IAAI,iBAAiB;AAC/B,IAAG,aAAa,EAAE,EAAE,EAAE,EAAE;AACxB,IAAG,eAAe,IAAI,aAAa,EAAE;AACrC,IAAG,iBAAiB,iBAAiB,SAAS,8EAA8E,QAAQ;AAClI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,MAAG,mBAAmB,OAAO,QAAQ,gBAAgB,MAAM,MAAM,OAAO,QAAQ,iBAAiB;AACjG,aAAU,YAAY,MAAM;AAAA,IAC9B,CAAC;AACD,IAAG,WAAW,IAAI,0DAA0D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,0DAA0D,IAAI,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,0DAA0D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAClX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,2BAA2B,CAAC;AACtE,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,IAAI,QAAW,YAAY,IAAI,GAAG,gCAAgC,CAAC;AACxF,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,IAAI,GAAG,2BAA2B,CAAC;AACvE,IAAG,UAAU,CAAC;AACd,IAAG,iBAAiB,WAAW,OAAO,QAAQ,cAAc;AAC5D,IAAG,WAAW,QAAQ,OAAO,QAAQ,SAAS;AAAA,EAChD;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,cAAc;AAChB;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB;AAAA,EAClC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,kBAAkB,CAAC;AAAA,EAC1G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,OAAO,QAAQ,YAAY,CAAC;AAAA,EACjG;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,EAAE;AAAA,EACpB;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ;AAC1C,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAChB,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,kBAAqB,YAAY,GAAG,GAAG,iCAAiC,CAAC;AAC5E,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,2DAA2D,GAAG,GAAG;AAAA,EACnH;AACF;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,OAAO;AACT;AACA,IAAM,MAAM,SAAO;AAAA,EACjB,aAAa;AACf;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,cAAc;AAAA,EACd,QAAQ;AACV;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU;AAAA,EAC5B;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,uBAAuB,OAAO,QAAQ,cAAc,cAAc,UAAU;AAC1F,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,cAAc,EAAE;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,eAAe,OAAO,QAAQ,WAAW;AAAA,EACzD;AACF;AACA,SAAS,8DAA8D,IAAI,KAAK;AAC9E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe;AAAA,EACjC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,cAAc,CAAC,EAAE,GAAG,+DAA+D,GAAG,GAAG,iBAAiB,CAAC;AAAA,EAChM;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,OAAO,QAAQ,oBAAuB,gBAAgB,GAAG,KAAQ,gBAAgB,GAAG,KAAK,OAAO,QAAQ,WAAW,CAAC,CAAC,CAAC;AACzL,IAAG,UAAU;AACb,IAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,OAAO,QAAQ,oBAAoB,CAAC;AAAA,EACzG;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,kBAAkB;AACvC,IAAG,UAAU,GAAG,iBAAiB,MAAM,CAAC;AACxC,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,UAAU;AAAA,EAC5B;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,iEAAiE,IAAI,KAAK;AACjF,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK;AAC1B,IAAG,WAAW,GAAG,kEAAkE,GAAG,GAAG,gBAAgB,CAAC;AAC1G,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,iBAAoB,YAAY,CAAC;AACvC,IAAG,WAAW,uBAAuB,OAAO,QAAQ,cAAc,cAAc,UAAU;AAC1F,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,cAAc;AAAA,EAClD;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,cAAc,EAAE;AAAA,EAClC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,eAAe,OAAO,QAAQ,WAAW;AAAA,EACzD;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe;AAAA,EACjC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,cAAc,CAAC,EAAE,GAAG,mEAAmE,GAAG,GAAG,iBAAiB,CAAC;AAAA,EACxM;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,OAAO,QAAQ,oBAAuB,gBAAgB,GAAG,KAAQ,gBAAgB,GAAG,KAAK,OAAO,QAAQ,WAAW,CAAC,CAAC,CAAC;AACzL,IAAG,UAAU;AACb,IAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,OAAO,QAAQ,oBAAoB,CAAC;AAAA,EACzG;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE,GAAG,GAAG;AACpC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,OAAO,GAAG,IAAI;AACjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,GAAG;AAAA,EAC1B;AACF;AACA,SAAS,qFAAqF,IAAI,KAAK;AACrG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAC1B,IAAG,OAAO,GAAG,YAAY;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,qBAAqB,QAAQ,SAAS,EAAE,6BAAgC,YAAY,GAAG,GAAG,OAAO,CAAC;AAAA,EAClH;AACF;AACA,SAAS,mGAAmG,IAAI,KAAK;AACnH,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,SAAS,SAAS,0HAA0H;AACxJ,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,aAAU,YAAY,QAAQ,SAAS,QAAQ,OAAO,IAAI,IAAI;AAAA,IAChE,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,WAAW,aAAa,QAAQ,MAAS,cAAc;AAAA,EAC5D;AACF;AACA,SAAS,+HAA+H,IAAI,KAAK;AAC/I,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,GAAG;AAAA,EACrB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,uBAAuB,SAAS,QAAQ,aAAa,MAAM,EAAE;AAAA,EAClE;AACF;AACA,SAAS,iHAAiH,IAAI,KAAK;AACjI,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,sIAAsI;AACpK,MAAG,cAAc,GAAG;AACpB,YAAM,UAAa,cAAc,CAAC,EAAE;AACpC,aAAU,YAAY,QAAQ,SAAS,QAAQ,OAAO,IAAI,IAAI;AAAA,IAChE,CAAC;AACD,IAAG,WAAW,GAAG,gIAAgI,GAAG,GAAG,KAAK,EAAE;AAC9J,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,UAAU;AACb,IAAG,cAAc,QAAQ,aAAa,OAAO,IAAI,EAAE;AACnD,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,QAAQ,aAAa,IAAI,GAAG,EAAE;AAAA,EAChF;AACF;AACA,SAAS,mGAAmG,IAAI,KAAK;AACnH,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,kHAAkH,GAAG,GAAG,KAAK,EAAE;AAAA,EAClJ;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,cAAc,QAAQ,eAAe,IAAI,EAAE;AAAA,EAChD;AACF;AACA,SAAS,qFAAqF,IAAI,KAAK;AACrG,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oGAAoG,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,oGAAoG,GAAG,CAAC;AAAA,EAClP;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,cAAc,QAAQ,OAAO,IAAI,CAAC;AAAA,EACvC;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,sFAAsF,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,sFAAsF,GAAG,CAAC;AAC7N,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,CAAC,EAAE;AACpC,IAAG,UAAU;AACb,IAAG,cAAc,QAAQ,YAAY,IAAI,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,MAAM,CAAC;AACtG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,WAAW,iBAAiB,QAAQ,cAAc;AAAA,EACvD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC7G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,WAAW,cAAc,CAAC,QAAQ,WAAW,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAC1E;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;AAC3C,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,OAAO;AACzF,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,eAAe,GAAG,QAAQ;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAiB,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,MAAS,sBAAsB;AAChI,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,QAAI;AACJ,UAAM,yBAA4B,YAAY,CAAC;AAC/C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,eAAe,WAAW,UAAa,YAAY,GAAG,GAAG,OAAO,eAAe,MAAM,OAAO,OAAO,QAAQ,QAAQ,IAAI,IAAI,OAAO;AACrI,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,UAAa,YAAY,GAAG,GAAG,OAAO,YAAY,MAAM,OAAO,OAAO,QAAQ,QAAQ;AAC5G,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,WAAW,OAAO,eAAe,uBAAuB,OAAO,CAAC;AAC/E,IAAG,UAAU;AACb,IAAG,WAAc,YAAY,IAAI,GAAG,OAAO,SAAS,MAAM,CAAC;AAAA,EAC7D;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,iEAAiE;AAC/F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,mBAAmB,GAAG,GAAG;AAAA,EAC3E;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,WAAW,SAAS,SAAS,qEAAqE;AACnG,YAAM,UAAa,cAAc,GAAG,EAAE;AACtC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,aAAa,QAAQ,eAAe,EAAE,CAAC;AAAA,IACtE,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,IAAG,UAAU;AACb,IAAG,kBAAkB,WAAW,OAAO,OAAO,QAAQ,WAAW;AAAA,EACnE;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;AAC3C,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,iBAAiB,GAAG,iDAAiD,GAAG,GAAG,KAAK,GAAM,sBAAsB;AAC/G,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,OAAO,gBAAgB,GAAG,GAAG;AAC7E,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,WAAW,OAAO,eAAe,oBAAoB,OAAO,CAAC;AAC5E,IAAG,UAAU;AACb,IAAG,WAAc,YAAY,GAAG,GAAG,OAAO,kBAAkB,CAAC;AAAA,EAC/D;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,iBAAiB;AAC9B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,SAAS,SAAS,SAAS,iBAAiB,GAAG,GAAG;AAAA,EACpG;AACF;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,cAAc,OAAO,cAAc;AAC7C,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,iBAAa,OAAO,aAAa,oBAAoB,KAAK,GAAG,MAAM;AACjE,WAAK,cAAc;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,UAAM,gBAAgB,OAAO,aAAa;AAC1C,QAAI,iBAAiB,KAAK,gBAAgB,OAAO;AAC/C,WAAK,cAAc;AACnB,iBAAW,MAAM;AACf,aAAK,cAAc;AAAA,MACrB,GAAG,GAAG;AAAA,IACR;AACA,SAAK,cAAc;AACnB,SAAK,MAAM,cAAc;AAAA,EAC3B;AAAA,EACA,sBAAsB;AACpB,SAAK,iBAAiB;AACtB,UAAM,UAAU,UAAU,QAAQ,QAAQ,EAAE,KAAK,aAAa,GAAG,CAAC;AAClE,SAAK,aAAa,OAAO,SAAS,MAAM,KAAK,iBAAiB,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,SAAY,mBAAmB,GAAM,SAAY,iBAAiB,GAAM,SAAY,YAAY,CAAC;AAAA,IACtJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,eAAe,EAAE;AAAA,EAC3C;AAAA,EACA,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAkB,kBAAqB,kBAAkB,CAAC;AAAA,IAC7F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,cAAc,KAAK,GAAG,cAAc,GAAG,CAAC,SAAS,QAAQ,UAAU,QAAQ,GAAG,OAAO,KAAK,CAAC;AAAA,MACrG,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,UAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,sCAAsC,GAAG,CAAC;AACpH,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,QAAQ,UAAU,IAAI,CAAC;AAAA,QAC9C;AAAA,MACF;AAAA,MACA,cAAc,CAAI,UAAU;AAAA,MAC5B,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY,CAAC,GAAG,YAAY,QAAQ;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAuB,eAAe,CAAC;AAAA,IAChG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,SAAS,sCAAsC,GAAG,eAAe,GAAG,CAAC,GAAG,YAAY,UAAU,oBAAoB,GAAG,CAAC,GAAG,qBAAqB,2BAA2B,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,WAAW,CAAC;AAAA,MACvQ,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,UAAG,iBAAiB,GAAG,kCAAkC,GAAG,GAAG,gBAAgB,MAAS,sBAAsB;AAC9G,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAc,YAAY,GAAG,GAAG,IAAI,SAAS,MAAM,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,MACA,cAAc,CAAI,mBAAsB,qBAA0B,qBAAwB,WAAc,cAAc;AAAA,MACtH,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,eAAe,UAAU;AACnC,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,qBAAqB,CAAC;AAC3B,SAAK,YAAY,CAAC,GAAG,SAAS,KAAK;AAAA,EACrC;AAAA,EACA,WAAW,MAAM;AACf,WAAO,CAAC,MAAM,UAAU,KAAK,cAAc,YAAY,KAAK,IAAI;AAAA,EAClE;AAAA,EACA,gBAAgB;AACd,SAAK,mBAAmB,QAAQ,CAAC;AAAA,MAC/B;AAAA,IACF,MAAM;AACJ,WAAK,SAAS,SAAS,eAAe,QAAQ;AAC9C,iBAAW,MAAM,KAAK,SAAS,YAAY,eAAe,QAAQ,GAAG,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,kBAAqB,aAAa,GAAM,kBAAqB,SAAS,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,WAAW,SAAS,sBAAsB,IAAI,KAAK;AACjD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB;AAAA,QACxE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,mBAAmB,aAAa,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,YAAY,GAAG,eAAe,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,YAAY,GAAG,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,WAAW,UAAU,GAAG,YAAY,UAAU,GAAG,CAAC,SAAS,qBAAqB,WAAW,UAAU,GAAG,SAAS,GAAG,eAAe,GAAG,CAAC,WAAW,UAAU,GAAG,YAAY,YAAY,GAAG,OAAO,GAAG,CAAC,eAAe,YAAY,iBAAiB,QAAQ,iBAAiB,SAAS,QAAQ,sBAAsB,GAAG,YAAY,iBAAiB,GAAG,CAAC,GAAG,iBAAiB,YAAY,aAAa,GAAG,OAAO,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,oBAAoB,GAAG,eAAe,GAAG,CAAC,GAAG,iBAAiB,GAAG,SAAS,YAAY,GAAG,CAAC,SAAS,oBAAoB,eAAe,IAAI,aAAa,aAAa,GAAG,aAAa,GAAG,eAAe,GAAG,CAAC,eAAe,IAAI,aAAa,aAAa,GAAG,oBAAoB,GAAG,WAAW,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,eAAe,SAAS,QAAQ,UAAU,GAAG,OAAO,WAAW,cAAc,mBAAmB,GAAG,oBAAoB,GAAG,CAAC,GAAG,iBAAiB,uBAAuB,YAAY,WAAW,CAAC;AAAA,MACl2C,UAAU,SAAS,yBAAyB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,UAAG,iBAAiB,GAAG,gCAAgC,GAAG,GAAG,gBAAgB,GAAM,sBAAsB;AACzG,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wCAAwC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,wCAAwC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,yCAAyC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,yCAAyC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AACpgB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAc,YAAY,GAAG,GAAG,IAAI,cAAc,QAAQ,CAAC;AAAA,QAChE;AAAA,MACF;AAAA,MACA,cAAc,CAAI,SAAY,kBAAqB,YAAe,qBAAwB,aAAgB,mBAAsB,WAAc,gBAAgB;AAAA,MAC9J,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAAgC,kBAAuB,gBAAgB,CAAC;AAAA,IAC3G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,0BAA0B,CAAC;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,SAAS,GAAG,SAAS,GAAG,CAAC,GAAG,iBAAiB,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,QAAQ,UAAU,gBAAgB,SAAS,cAAc,SAAS,GAAG,WAAW,GAAG,CAAC,QAAQ,UAAU,gBAAgB,SAAS,cAAc,SAAS,GAAG,aAAa,GAAG,OAAO,CAAC;AAAA,MAC9T,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,iBAAiB,GAAG,4CAA4C,GAAG,IAAI,OAAO,GAAM,sBAAsB;AAC7G,UAAG,OAAO,GAAG,OAAO;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAc,YAAY,GAAG,GAAG,IAAI,QAAQ,OAAO,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,MACA,cAAc,CAAI,SAAY,WAAc,kBAAqB,YAAY;AAAA,MAC7E,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAuB,gBAAgB,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAAA,MACpD,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,QAAQ,aAAa,WAAW,MAAM,GAAG,CAAC,GAAG,aAAa,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,kBAAkB,cAAc,GAAG,aAAa,OAAO,GAAG,CAAC,GAAG,OAAO,gBAAgB,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,MAAM,uBAAuB,QAAQ,uBAAuB,GAAG,OAAO,UAAU,QAAQ,uBAAuB,GAAG,OAAO,GAAG,CAAC,GAAG,iBAAiB,WAAW,MAAM,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,YAAY,GAAG,CAAC,OAAO,QAAQ,GAAG,YAAY,GAAG,CAAC,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,UAAU,aAAa,IAAI,GAAG,gBAAgB,GAAG,iBAAiB,SAAS,GAAG,CAAC,YAAY,IAAI,QAAQ,UAAU,GAAG,OAAO,qBAAqB,GAAG,CAAC,QAAQ,cAAc,aAAa,eAAe,GAAG,SAAS,UAAU,CAAC;AAAA,MACjxB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,2CAA2C,IAAI,EAAE;AAClE,UAAG,OAAO,GAAG,OAAO;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,UAAa,YAAY,GAAG,GAAG,IAAI,QAAQ,cAAc,KAAQ,gBAAgB,GAAG,GAAG,KAAK,IAAI,IAAI,OAAO;AAAA,QAC/H;AAAA,MACF;AAAA,MACA,cAAc,CAAM,eAAoB,sBAA2B,iBAAsB,sBAA2B,SAAc,QAAW,oBAAyB,iBAAsB,gBAAqB,qBAAwB,WAAc,gBAAgB;AAAA,MACvQ,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB;AAAA,MAC5B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAuB,kBAAkB,CAAC;AAAA,IACtG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,UAAU,CAAI,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAAA,MACtD,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,OAAO,UAAU,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,QAAQ,QAAQ,aAAa,SAAS,GAAG,CAAC,GAAG,SAAS,eAAe,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,aAAa,KAAK,CAAC;AAAA,MACxN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,gBAAgB;AACpF,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC5E,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,OAAO,CAAC;AAC5E,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,cAAiB,YAAY,GAAG,GAAG,IAAI,QAAQ,sBAAsB,KAAK,IAAI,QAAQ,qBAAqB,IAAI,EAAE;AACpH,UAAG,UAAU,CAAC;AACd,UAAG,cAAiB,YAAY,GAAG,GAAG,IAAI,QAAQ,iBAAiB,IAAI,IAAI,CAAC;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,cAAc,CAAI,8BAA8B,oBAAuB,WAAc,gBAAgB;AAAA,MACrG,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,kBAAkB;AAAA,MAC9B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAE3B,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,UAAU;AACf,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,kBAAkB;AAChB,SAAK,QAAQ,oBAAoB;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAkB,aAAa,CAAC;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,UAAU,CAAI,mBAAmB,CAAC,eAAe,mBAAmB,CAAC,CAAC;AAAA,MACtE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,MAAM,eAAe,GAAG,UAAU,oBAAoB,eAAe,WAAW,aAAa,eAAe,eAAe,QAAQ,GAAG,cAAc,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,QAAQ,UAAU,GAAG,kBAAkB,GAAG,OAAO,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,MAAM,wBAAwB,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,WAAW,GAAG,eAAe,GAAG,wBAAwB,GAAG,CAAC,GAAG,WAAW,GAAG,aAAa,CAAC;AAAA,MACxgB,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,YAAY,CAAC;AAChF,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,WAAW,SAAS,SAAS,0DAA0D;AACxF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,QAAQ,cAAc,CAAC,IAAI,QAAQ,WAAW;AAAA,UAC1E,CAAC;AACD,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,KAAK,EAAE,GAAG,+CAA+C,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB;AACnQ,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,UAAU,IAAI,0BAA0B;AAC3C,UAAG,WAAW,IAAI,qDAAqD,GAAG,GAAG,oBAAoB,CAAC;AAClG,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,iBAAoB,YAAY,CAAC;AACvC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,IAAI,QAAQ,gBAAgB,CAAC;AAChG,UAAG,UAAU;AACb,UAAG,YAAY,iBAAiB,CAAC,IAAI,QAAQ,WAAW;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,mBAAmB,IAAI,QAAQ,WAAW;AACzD,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,CAAC,IAAI,QAAQ,cAAc,iBAAiB,IAAI;AAClF,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,QAAQ,cAAc,IAAI,EAAE;AACjD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,IAAI,cAAc,CAAC;AAAA,QACxF;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAqB,cAAiB,8BAA8B,eAAe,mBAAmB,iBAAiB,6BAA6B,oBAAoB;AAAA,MAC1L,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,eAAe,mBAAmB;AAAA,MAC9C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,UAAU,OAAO,aAAa;AAAA,EACrC;AAAA,EAEA,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,SAAK,QAAQ,oBAAoB;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,MACtC,UAAU,CAAI,mBAAmB,CAAC,eAAe,mBAAmB,CAAC,CAAC;AAAA,MACtE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,MAAM,eAAe,GAAG,UAAU,oBAAoB,eAAe,WAAW,aAAa,eAAe,eAAe,QAAQ,GAAG,cAAc,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,QAAQ,UAAU,GAAG,kBAAkB,GAAG,OAAO,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,MAAM,wBAAwB,GAAG,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,SAAS,WAAW,GAAG,eAAe,GAAG,wBAAwB,GAAG,CAAC,GAAG,WAAW,GAAG,aAAa,CAAC;AAAA,MACxgB,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,YAAY,CAAC;AACpF,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,WAAW,SAAS,SAAS,8DAA8D;AAC5F,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,QAAQ,cAAc,CAAC,IAAI,QAAQ,WAAW;AAAA,UAC1E,CAAC;AACD,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,KAAK,EAAE,GAAG,mDAAmD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB;AAC/Q,UAAG,aAAa,EAAE,EAAE;AACpB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,UAAU,IAAI,0BAA0B,EAAE,IAAI,iBAAiB,MAAM,CAAC;AACzE,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,iBAAoB,YAAY,CAAC;AACvC,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA6B,gBAAgB,GAAG,KAAK,IAAI,QAAQ,gBAAgB,CAAC;AAChG,UAAG,UAAU;AACb,UAAG,YAAY,iBAAiB,CAAC,IAAI,QAAQ,WAAW;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,mBAAmB,IAAI,QAAQ,WAAW;AACzD,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,CAAC,IAAI,QAAQ,cAAc,iBAAiB,IAAI;AAClF,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,QAAQ,cAAc,IAAI,EAAE;AAAA,QACnD;AAAA,MACF;AAAA,MACA,cAAc,CAAI,kBAAqB,cAAiB,8BAA8B,eAAe,mBAAmB,iBAAiB,2BAA2B;AAAA,MACpK,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,iBAAiB,kBAAkB;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY,CAAC,iBAAiB,kBAAkB;AAAA,MAChD,WAAW,CAAC,eAAe,mBAAmB;AAAA,MAC9C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,eAAe;AAAA,QACjC;AAAA,MACF;AAAA,MACA,cAAc,CAAI,YAAY;AAAA,MAC9B,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA,IAGZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,IAAI,cAAc;AAChB,WAAO,OAAO,aAAa;AAAA,EAC7B;AAAA,EACA,YAAY,yBAAyB,UAAU,aAAa,aAAa,cAAc;AACrF,SAAK,0BAA0B;AAC/B,SAAK,WAAW;AAChB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,eAAe,KAAK,YAAY,QAAQ,aAAa;AAC1D,SAAK,kBAAkB,KAAK,aAAa,WAAW;AACpD,SAAK,YAAY,CAAC,GAAG,YAAY,QAAQ;AAAA,EAC3C;AAAA,EACA,kBAAkB;AAChB,SAAK,YAAY,gBAAgB;AAAA,EACnC;AAAA,EACA,SAAS;AACP,SAAK,YAAY,OAAO,EAAE,UAAU;AAAA,EACtC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAkB,0BAA0B,GAAM,kBAAuB,eAAe,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,kBAAkB,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IACtR;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,uBAAuB,aAAa,GAAG,CAAC,eAAe,IAAI,WAAW,UAAU,GAAG,UAAU,GAAG,CAAC,QAAQ,UAAU,GAAG,YAAY,SAAS,GAAG,CAAC,qBAAqB,IAAI,QAAQ,sBAAsB,QAAQ,UAAU,MAAM,oBAAoB,eAAe,YAAY,iBAAiB,QAAQ,iBAAiB,SAAS,GAAG,UAAU,GAAG,CAAC,mBAAmB,oBAAoB,GAAG,iBAAiB,qBAAqB,YAAY,WAAW,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,SAAS,sCAAsC,GAAG,eAAe,GAAG,CAAC,GAAG,YAAY,UAAU,oBAAoB,GAAG,CAAC,GAAG,qBAAqB,2BAA2B,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,WAAW,GAAG,CAAC,GAAG,iBAAiB,SAAS,GAAG,CAAC,GAAG,iBAAiB,WAAW,GAAG,OAAO,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,GAAG,YAAY,WAAW,GAAG,OAAO,CAAC;AAAA,MACt0B,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,6CAA6C,IAAI,IAAI,OAAO,CAAC;AAC9E,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,KAAK,CAAC;AAAA,QAC5E;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,gBAAgB,UAAa,YAAY,GAAG,GAAG,IAAI,YAAY,MAAM,OAAO,OAAO,QAAQ,mBAAmB,IAAI,CAAC;AAAA,QACxH;AAAA,MACF;AAAA,MACA,cAAc,CAAI,mBAAsB,qBAA0B,qBAAwB,aAAgB,mBAAsB,WAAc,kBAAqB,cAAc;AAAA,MACjL,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,IAAI,cAAc;AAChB,WAAO,OAAO,aAAa;AAAA,EAC7B;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,WAAW,KAAK,IAAI,eAAa,WAAW,KAAK,UAAQ,KAAK,gBAAgB,KAAK,mBAAmB,GAAG,eAAe,EAAE,CAAC;AAAA,EACzI;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,WAAW,KAAK,IAAI,eAAa,WAAW,OAAO,UAAQ,KAAK,gBAAgB,KAAK,mBAAmB,KAAK,CAAC,CAAC,CAAC;AAAA,EAC9H;AAAA,EACA,IAAI,sBAAsB;AACxB,WAAO,KAAK,aAAa,YAAY;AAAA,EACvC;AAAA,EACA,YAAY,cAAc,aAAa;AACrC,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,aAAa,KAAK,YAAY,SAAS,wBAAwB;AAAA,EACtE;AAAA,EACA,aAAa,aAAa;AACxB,SAAK,aAAa,YAAY,WAAW;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAqB,mBAAmB,GAAM,kBAAqB,kBAAkB,CAAC;AAAA,IAChJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,oBAAoB,aAAa,GAAG,CAAC,eAAe,IAAI,WAAW,UAAU,GAAG,UAAU,GAAG,CAAC,qBAAqB,IAAI,QAAQ,sBAAsB,QAAQ,UAAU,MAAM,oBAAoB,eAAe,YAAY,iBAAiB,QAAQ,iBAAiB,SAAS,GAAG,UAAU,GAAG,CAAC,mBAAmB,oBAAoB,GAAG,iBAAiB,qBAAqB,YAAY,WAAW,GAAG,CAAC,QAAQ,sBAAsB,GAAG,eAAe,GAAG,CAAC,QAAQ,sBAAsB,GAAG,iBAAiB,GAAG,OAAO,CAAC;AAAA,MACtgB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC;AAC1E,UAAG,OAAO,GAAG,OAAO;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,iBAAiB,UAAa,YAAY,GAAG,GAAG,IAAI,kBAAkB,MAAM,OAAO,OAAO,QAAQ,WAAW,KAAK,IAAI,IAAI,EAAE;AAAA,QACjI;AAAA,MACF;AAAA,MACA,cAAc,CAAI,aAAgB,mBAAsB,SAAS;AAAA,MACjE,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgCZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAMA,4BAAN,MAAM,kCAAiC,yBAA2B;AAAA,EAChE,IAAI,YAAY;AACd,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,OAAQ,QAAO,CAAC;AACjD,WAAO,KAAK,OAAO,IAAI,WAAS;AAC9B,UAAI,CAAC,MAAM,QAAS,QAAO;AAC3B,YAAM,QAAQ,MAAM,QAAQ,QAAQ,GAAG;AACvC,UAAI,QAAQ,IAAI;AACd,eAAO,iCACF,QADE;AAAA,UAEL,SAAS,MAAM,QAAQ,MAAM,GAAG,KAAK;AAAA,UACrC,mBAAmB,MAAM,QAAQ,MAAM,QAAQ,GAAG,MAAM,QAAQ,SAAS,CAAC,EAAE,MAAM,GAAG;AAAA,QACvF;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,iCAAiC,mBAAmB;AAClE,gBAAQ,0CAA0C,wCAA2C,sBAAsB,yBAAwB,IAAI,qBAAqB,yBAAwB;AAAA,MAC9L;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,UAAU,CAAI,0BAA0B;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChC,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,iBAAiB,GAAG,yCAAyC,GAAG,GAAG,OAAO,GAAM,sBAAsB;AAAA,QAC3G;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,SAAS;AAAA,QAC7B;AAAA,MACF;AAAA,MACA,cAAc,CAAI,gBAAgB;AAAA,MAClC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkBA,2BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAc,IAAI,eAAe,aAAa;AACpD,IAAM,YAAY;AAClB,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,IAAI,IAAI,KAAK;AACX,QAAI,QAAQ,KAAK,KAAM;AACvB,SAAK,UAAU,GAAG;AAClB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,OAAO;AACZ,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,UAAU,QAAQ;AACvB,SAAK,YAAY,QAAQ;AACzB,SAAK,yBAAyB,QAAQ;AAAA,EACxC;AAAA,EACA,gBAAgB,MAAM;AACpB,QAAI,CAAC,KAAM,QAAO;AAClB,UAAM,IAAI,SAAS,cAAc,GAAG;AACpC,MAAE,OAAO,KAAK;AACd,WAAO,EAAE,SAAS,QAAQ,OAAO,EAAE;AAAA,EACrC;AAAA,EACA,qBAAqB;AACnB,UAAM,OAAO,oBAAoB,WAAW,KAAK,GAAG;AACpD,UAAM,WAAW,WAAW,KAAK,QAAQ,UAAU,EAAE,CAAC;AACtD,UAAM,OAAO,SAAS,cAAc,QAAQ;AAC5C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,yBAAyB,UAAU;AACjC,UAAM,gBAAgB,SAAS,IAAI,yBAAyB;AAE5D,kBAAc,KAAK,UAAU,SAAO;AAClC,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW,SAAS,IAAI,eAAe;AAC5C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,mBAAmB;AAC5B,SAAK,SAAS,OAAO,IAAI,MAAM,IAAI;AAAA,EACrC;AAAA,EACA,UAAU,UAAU;AAClB,SAAK,SAAS,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC;AAAA,EACrD;AAAA,EACA,UAAU,KAAK;AACb,SAAK,OAAO,QAAQ,WAAS;AAC3B,YAAM,UAAU,oBAAoB,OAAO,KAAK,GAAG;AACnD,YAAM,UAAU,oBAAoB,OAAO,GAAG;AAC9C,YAAM,OAAO,KAAK,OAAO,IAAI,OAAO;AACpC,YAAM,OAAO,KAAK,gBAAgB,IAAI,KAAK;AAC3C,YAAM,WAAW,iBAAiB,4BAA4B,IAAI;AAClE,WAAK,SAAS,KAAK,QAAQ,EAAE,UAAU,MAAM;AAC3C,cAAM,UAAU,KAAK,SAAS,OAAO,IAAI,OAAO;AAChD,aAAK,OAAO,OAAO,OAAO;AAC1B,aAAK,OAAO,IAAI,SAAS,OAAO;AAChC,cAAM,UAAU,KAAK,SAAS,OAAO,IAAI,IAAI;AAC7C,aAAK,SAAS,OAAO,OAAO,IAAI;AAChC,aAAK,SAAS,OAAO,IAAI,SAAS,OAAO;AACzC,aAAK,SAAS,OAAO,OAAO;AAAA,MAC9B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,SAAY,QAAQ,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,oBAAoB,OAAO,KAAK;AACvC,SAAO,MAAM,QAAQ,kBAAkB,GAAG;AAC5C;AACA,SAAS,qBAAqB,UAAU;AACtC,SAAO,MAAM,IAAI,iBAAiB,QAAQ;AAC5C;AACA,IAAM,iCAAiC,CAAC;AAAA,EACtC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,eAAe;AAAA,EACtB,OAAO;AACT,CAAC;AACD,SAAS,kBAAkB,UAAU;AACnC,SAAO,MAAM;AACX,aAAS,SAAS,CAAC;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,WAAW;AAAA,IACb,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAI,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0Jb,IAAM,+BAA+B,CAAC;AAAA,EACpC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,qBAAqB,4BAA4B;AAAA,EACxD,OAAO;AACT,CAAC;AACD,SAAS,gBAAgB,cAAc,uBAAuB;AAC5D,SAAO,MAAM;AACX,iBAAa,cAAc,iBAAiB,kBAAkB,MAAM,CAAC;AACrE,gBAAY,qBAAqB;AAAA,EACnC;AACF;AACA,SAAS,YAAY,uBAAuB;AAC1C,wBAAsB,IAAI;AAAA,IACxB,KAAK;AAAA,IACL,WAAW;AAAA,EACb,CAAC;AACD,wBAAsB,IAAI;AAAA,IACxB,KAAK;AAAA,IACL,WAAW;AAAA,EACb,CAAC;AACD,wBAAsB,IAAI;AAAA,IACxB,KAAK;AAAA,IACL,WAAW;AAAA,EACb,CAAC;AACH;AACA,IAAM,kCAAkC,CAAC;AAAA,EACvC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,QAAQ;AAAA,EACf,OAAO;AACT,CAAC;AACD,SAAS,kBAAkB,UAAU;AACnC,QAAM,WAAW,SAAS,IAAI,eAAe;AAC7C,QAAM,cAAc,SAAS,IAAI,WAAW;AAC5C,QAAM,0BAA0B,SAAS,IAAI,0BAA0B;AACvE,SAAO,MAAM;AACX,aAAS,SAAS,CAAC;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,QAAQ,MAAM,wBAAwB;AAAA,IACxC,GAAG;AAAA,MACD,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,QAAQ,MAAM;AACZ,oBAAY,OAAO,EAAE,UAAU;AAAA,MACjC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,0BAA0B;AACjC,SAAO,yBAAyB,CAAC,gCAAgC,iCAAiC,8BAA8B;AAAA,IAC9H,SAAS;AAAA,IACT,UAAUA;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG,kBAAkB;AAAA,IACnB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,MAAM,CAAC,gBAAgB;AAAA,EACzB,CAAC,CAAC;AACJ;AACA,IAAM,UAAU,CAAC,4BAA4B,wBAAwB,oBAAoB;AACzF,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,4BAA4B,wBAAwB,sBAAsBA,2BAA0B,eAAe,mBAAmB,iBAAiB,sBAAsB,oBAAoB,6BAA6B,oBAAoB,oBAAoB;AAAA,MACrR,SAAS,CAAC,YAAY,mBAAmB,mBAAmB,mBAAmB,qBAAqB;AAAA,MACpG,SAAS,CAAC,4BAA4B,wBAAwB,sBAAsBA,2BAA0B,eAAe,mBAAmB,iBAAiB,sBAAsB,oBAAoB,2BAA2B;AAAA,IACxO,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,YAAY,mBAAmB,mBAAmB,mBAAmB,qBAAqB;AAAA,IACtG,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,GAAG,SAASA,2BAA0B,eAAe,mBAAmB,iBAAiB,sBAAsB,oBAAoB,6BAA6B,oBAAoB,oBAAoB;AAAA,MACvN,SAAS,CAAC,GAAG,SAASA,2BAA0B,eAAe,mBAAmB,iBAAiB,sBAAsB,oBAAoB,2BAA2B;AAAA,MACxK,SAAS,CAAC,YAAY,mBAAmB,mBAAmB,mBAAmB,qBAAqB;AAAA,IACtG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA;AAAA,EAIrB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,wBAAwB,CAAC;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB;AAAA,MAC9B,SAAS,CAAC,oBAAoB;AAAA,IAChC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,sBAAsB,oBAAoB;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB;AAAA,MAC9B,SAAS,CAAC,oBAAoB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ValidationErrorComponent"]}