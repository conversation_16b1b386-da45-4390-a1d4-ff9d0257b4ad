import type { AlertLogDto, AlertLogPagedResultRequestDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AlertLogService {
  apiName = 'Default';
  

  getList = (input: AlertLogPagedResultRequestDto, fromDate: string, toDate: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AlertLogDto>>({
      method: 'GET',
      url: '/api/app/alertLog',
      params: { vehicleId: input.vehicleId, deviceId: input.deviceId, skipCount: input.skipCount, maxResultCount: input.maxResultCount, fromDate, toDate },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
