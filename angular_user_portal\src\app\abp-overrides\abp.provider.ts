import { provideAccountConfig } from '@abp/ng.account/config';
import { ABP, LocalizationService, provideAbpCore, withOptions } from '@abp/ng.core';
import { provideFeatureManagementConfig } from '@abp/ng.feature-management';
import { provideIdentityConfig } from '@abp/ng.identity/config';
import { provideAbpOAuth } from '@abp/ng.oauth';
import { provideSettingManagementConfig } from '@abp/ng.setting-management/config';
import { provideTenantManagementConfig } from '@abp/ng.tenant-management/config';
import { ThemeBasicModule } from '@abp/ng.theme.basic';
import { ErrorHand<PERSON>, provideAbpThemeShared } from '@abp/ng.theme.shared';
import { EnvironmentProviders, importProvidersFrom, makeEnvironmentProviders } from '@angular/core';
import {
  NgxMainVisualsConfig,
  provideABPLanguage,
  provideNgxMainVisuals,
} from '@ttwr-framework/ngx-main-visuals';

/**
 * Combined version of `provideNgxMainVisuals` with ABP services.
 *
 * @param config The same config used with `ttwr-framework` with
 * extra `abpOptions` to add the options of `provideAbpCore`.
 *
 * @example
 * ```ts
 * import { environment } from '../environments/environment';
 * import { registerLocale } from '@abp/ng.core/locale';
 *
 * export const appConfig: ApplicationConfig = {
 *  providers: [
 *    ...,
 *    provideNgxMainVisualsWithABP({
 *       initialPageSize: 20,
 *       pageSizeOptions: [20, 30, 40, 100],
 *       // any extra ttwr-framework options...
 *       abpOptions: {
 *         environment,
 *         registerLocaleFn: registerLocale(),
 *       },
 *     }),
 *    ...,
 *  ],
 * }
 * ```
 */
export const provideNgxMainVisualsWithABP = (
  config?: Partial<ConfigWithABP>
): EnvironmentProviders => {
  return makeEnvironmentProviders([
    provideNgxMainVisuals(config),
    provideABPLanguage(LocalizationService),
    provideAbpCore(withOptions(config?.abpOptions)),
    provideAbpOAuth(),
    provideAbpThemeShared(),
    provideAccountConfig(),
    provideIdentityConfig(),
    provideTenantManagementConfig(),
    provideSettingManagementConfig(),
    provideFeatureManagementConfig(),
    importProvidersFrom(ThemeBasicModule),
    {
      provide: ErrorHandler,
      useValue: null,
    },
  ]);
};

interface ConfigWithABP extends NgxMainVisualsConfig {
  abpOptions?: ABP.Root;
}
