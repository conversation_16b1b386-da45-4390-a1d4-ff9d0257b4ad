import { Injectable, inject } from '@angular/core';
import { Messaging, getToken, onMessage, deleteToken } from '@angular/fire/messaging';
import { from, Observable, of, throwError } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';
import { FcmDeviceService } from '@proxy/mobile/fcmdevices/fcm-device.service';
import { DeviceType } from '@proxy/fcmdevices/device-type.enum';

@Injectable({
  providedIn: 'root',
})
export class FcmService {
  messaging = inject(Messaging);
  private fcmDeviceService = inject(FcmDeviceService);

  messages$: Observable<any>;

  constructor() {
    // Initialize the messages observable
    this.messages$ = new Observable(observer => {
      onMessage(this.messaging, message => {
        observer.next(message);
      });
    }).pipe(
      tap(message => {
        console.log('New message received:', message);
      })
    );
  }

  /**
   * Request permission for notifications
   */
  requestPermission(): Observable<NotificationPermission> {
    return from(Notification.requestPermission()).pipe(
      tap(permission => {
        console.log('Notification permission:', permission);
      }),
      catchError(error => {
        console.error('Error requesting notification permission:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get FCM token and register it with the backend
   */
  getAndRegisterToken(): Observable<string | null> {
    return this.requestPermission().pipe(
      switchMap(permission => {
        if (permission !== 'granted') {
          console.warn('Notification permission not granted');
          return of(null);
        }

        return from(
          navigator.serviceWorker
            .register('./assets/firebase-messaging-sw.js', {
              type: 'module',
            })
            .then(serviceWorkerRegistration => {
              return getToken(this.messaging, {
                serviceWorkerRegistration,
              });
            })
        );
      }),
      switchMap(token => {
        if (!token) {
          return of(null);
        }

        // Register the token with the backend
        return this.registerTokenWithBackend(token).pipe(switchMap(() => of(token)));
      }),
      catchError(error => {
        console.error('Error getting FCM token:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Register FCM token with the backend
   */
  private registerTokenWithBackend(token: string): Observable<void> {
    const deviceId = this.getDeviceId();

    return this.fcmDeviceService.register({
      fcmToken: token,
      deviceType: DeviceType.Web,
      name: 'Web Browser',
      deviceId: deviceId,
    });
  }

  /**
   * Delete FCM token
   */
  deleteToken(): Observable<boolean> {
    return from(getToken(this.messaging)).pipe(
      switchMap(token => {
        if (!token) {
          return of(true);
        }
        // First remove from backend
        return this.fcmDeviceService.removeDeviceToken(token).pipe(
          // Then delete from Firebase
          switchMap(() => deleteToken(this.messaging)),
          catchError(error => {
            console.error('Error removing token from backend:', error);
            // Still try to delete from Firebase
            return deleteToken(this.messaging);
          })
        );
      }),
      catchError(error => {
        console.error('Error deleting FCM token:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Generate a unique device ID or retrieve existing one
   */
  private getDeviceId(): string {
    const storageKey = 'fcm_device_id';
    let deviceId = localStorage.getItem(storageKey);

    if (!deviceId) {
      deviceId = 'web_' + this.generateUUID();
      localStorage.setItem(storageKey, deviceId);
    }

    return deviceId;
  }

  /**
   * Generate a UUID for device identification
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }
}
