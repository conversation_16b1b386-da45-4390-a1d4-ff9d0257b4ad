<div class="grid h-full justify-center items-center">
  <div class="relative grid grid-cols-2 justify-center gap-4">
    <!-- <div class="bg-main_red item absolute-1 top-0 -left-[150%]">
      <div class="inner_item">
        <div>
          <img src="assets/images/svg/vehicle_settings.svg" class="m-auto" alt="" />
        </div>

        <div class="mt-2">{{ 'UserPortal:vehicle_settings' | i18n }}</div>
      </div>
    </div> -->

    <div
      class="bg-main_sky_blue2 item absolute-1 -top-3/4 -left-3/4"
      [ngClass]="{ '!bg-gray-500': !feature()['GoTrack.GroupManagement'] }"
      [routerLink]="feature()['GoTrack.GroupManagement'] ? ['/main/groups-management'] : null"
    >
      <div class="inner_item">
        <div>
          <img src="assets/images/svg/groups.svg" class="m-auto" alt="" />
        </div>
        <div class="mt-2">{{ 'UserPortal:groups' | i18n }}</div>
      </div>
    </div>

    <div
      class="bg-light_perple item"
      [ngClass]="{ '!bg-gray-500': !feature()['GoTrack.PathManagement'] }"
      [routerLink]="feature()['GoTrack.PathManagement'] ? ['/main/routes'] : null"
    >
      <div class="inner_item">
        <div>
          <img src="assets/images/svg/lines.svg" class="m-auto" alt="" />
        </div>
        <div class="mt-2">{{ 'UserPortal:lines' | i18n }}</div>
      </div>
    </div>

    <div
      class="bg-main_perple item absolute-1 -top-3/4 -right-3/4"
      [ngClass]="{
        'bg-gray-500': !(
          feature()['GoTrack.Alerts'] || feature()['GoTrack.GeographicAreaManagement']
        )
      }"
      [routerLink]="
        feature()['GoTrack.Alerts'] || feature()['GoTrack.GeographicAreaManagement']
          ? ['/main/alerts']
          : null
      "
    >
      <div class="inner_item">
        <div>
          <img src="assets/images/svg/alerts.svg" class="m-auto" alt="" />
        </div>
        <div class="mt-2">{{ 'UserPortal:alerts' | i18n }}</div>
      </div>
    </div>
    <div
      class="bg-main_sky_blue item absolute-1 top-0 -right-[150%]"
      [ngClass]="{ 'bg-gray-500': !feature()['GoTrack.ObserverManagement'] }"
      [routerLink]="feature()['GoTrack.ObserverManagement'] ? ['/main/observers-management'] : null"
    >
      <div class="inner_item">
        <div>
          <img src="assets/images/svg/observer.svg" class="m-auto" alt="" />
        </div>
        <div class="mt-2">{{ 'UserPortal:observersManagement' | i18n }}</div>
      </div>
    </div>
  </div>
</div>
