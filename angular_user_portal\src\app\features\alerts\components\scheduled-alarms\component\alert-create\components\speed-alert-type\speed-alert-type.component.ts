import { Component, computed, input } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatCard } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatIcon } from '@angular/material/icon';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { NgxGaugeModule } from 'ngx-gauge';

@Component({
  selector: 'alert-speed-alert-type',
  standalone: true,
  templateUrl: `./speed-alert-type.component.html`,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatChipsModule,
    MatIcon,
    MatButton,
    NgxGaugeModule,
    MatCard,
  ],
})
export class SpeedAlertTypeComponent {
  markerConfig = {
    '0': { color: '#555', size: 1, label: '0' },
    '120': { color: '#555', size: 1, label: '120' },
    '240': { color: '#555', size: 1, label: '240' },
    '360': { color: '#555', size: 1, label: '360' },
  };
  thresholds = {
    '0': {
      color: 'green',
      bgOpacity: 0.2,
    },
    '120': {
      color: 'orange',
      bgOpacity: 0.2,
    },
    '240': {
      color: 'red',
      bgOpacity: 0.2,
    },
  };

  alertForm = input.required<FormGroup>();
  speedControl = computed(() => {
    return this.alertForm().get('maxSpeed');
  });

  gaugeColor$ = computed(() => {
    const speed = this.speedControl().value;
    return speed < 120 ? 'green' : speed < 240 ? 'orange' : 'red';
  });
  removeGeoZone(id) {
    const control = this.alertForm().get('geoZoneIds');
    const newVal = [...control.value].filter(v => v != id);
    control.setValue(newVal);
  }

  increaseSpeed(): void {
    let currentSpeed = this.speedControl().value;
    if (currentSpeed < 360) {
      this.speedControl().setValue(currentSpeed + 10);
    }
  }

  decreaseSpeed(): void {
    let currentSpeed = this.speedControl().value;
    if (currentSpeed > 0) {
      this.speedControl().setValue(currentSpeed - 10);
    }
  }
}
