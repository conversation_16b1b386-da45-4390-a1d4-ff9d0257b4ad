<h1>
  {{ 'AbpSettingManagement::Settings' | abpLocalization }}
</h1>
<mat-card>
  <mat-card-content>
    <mat-nav-list>
      @for (tab of tabs(); track tab.name) {
      <button
        (click)="activeSectionIndex.set($index)"
        role="tab"
        mat-list-item
        [activated]="activeSectionIndex() === $index"
        *abpPermission="tab.requiredPolicy"
      >
        {{ tab.name | abpLocalization }}
      </button>
      }
    </mat-nav-list>
    <div>
      @if (tabs()[activeSectionIndex()]; as activeTab) { @switch (activeTab.name) { @case
      ('AbpFeatureManagement::Permission:FeatureManagement') {
      <p>
        {{ 'AbpFeatureManagement::ManageHostFeaturesText' | abpLocalization }}
      </p>
      <button mat-raised-button (click)="openFeaturesModel()" color="primary">
        <mat-icon>settings</mat-icon>
        {{ 'AbpFeatureManagement::ManageHostFeatures' | abpLocalization }}
      </button>
      } @case ('AbpSettingManagement::Menu:Emailing') {
      <ttwr-form [config]="emailFormConfig" />
      } @default {
      <ng-template *ngComponentOutlet="activeTab.component"></ng-template>
      } } }
    </div>
  </mat-card-content>
</mat-card>
