<div class="relative overflow-clip h-full w-full">
  <div class="geoLocation row h-full">
    <app-map [nodes]="node$()" [options]="options$()" />
  </div>
  <div class="absolute top-2 right-4 gray z-[999]">
    <button class="!bg-white" mat-mini-fab [routerLink]="['/main']">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>
  <div class="absolute bottom-14 right-4 gray max-w-80 z-[999]">
    <div class="bg-white rounded-lg p-4">
      <div class="text-start">
        <div>
          {{ 'UserPortal:status' | i18n }} :
          {{ selectedVehcileInfo$().isActive ? 'active' : 'Not Active' }}
        </div>
        <div>{{ 'UserPortal:vehicle_number' | i18n }} : {{ vehicle$().licensePlateSerial }}</div>
        <div>
          {{ 'UserPortal:average_speed' | i18n }} : {{ selectedVehcileInfo$().averageSpeed }}
          {{ 'Km/h' | i18n }}
        </div>
        <div class="flex">
          <span> {{ 'UserPortal:color' | i18n }} : </span>

          <div
            class="size-6 rounded-full inline-block mx-2"
            [ngStyle]="{ background: vehicle$().colorHex | hexToColor }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</div>
