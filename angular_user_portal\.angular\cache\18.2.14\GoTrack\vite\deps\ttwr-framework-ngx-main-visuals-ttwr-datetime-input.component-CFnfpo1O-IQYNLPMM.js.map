{"version": 3, "sources": ["../../../../../../node_modules/@ttwr-framework/ngx-main-visuals/fesm2022/ttwr-framework-ngx-main-visuals-ttwr-datetime-input.component-CFnfpo1O.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { input, Component } from '@angular/core';\nimport * as i9 from '@angular/forms';\nimport { ReactiveFormsModule, ControlContainer, FormGroupDirective } from '@angular/forms';\nimport * as i1 from '@angular/material/form-field';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport * as i2 from '@angular/material/input';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { provideNativeDatetimeAdapter, LanguagePipe, TtwrDatetimepickerToggle, TtwrDatetimepickerInput, TtwrDatetimepicker } from './ttwr-framework-ngx-main-visuals.mjs';\nimport '@angular/common/http';\nimport 'rxjs';\nimport '@angular/material/snack-bar';\nimport '@angular/core/rxjs-interop';\nimport '@angular/platform-browser';\nimport '@angular/common';\nimport '@ckeditor/ckeditor5-angular';\nimport '@angular/material/progress-spinner';\nimport '@angular/material/tooltip';\nimport '@angular/material/checkbox';\nimport '@angular/material/slide-toggle';\nimport '@angular/material/core';\nimport '@angular/material/datepicker';\nimport '@angular/material/select';\nimport '@angular/material/radio';\nimport '@angular/material/autocomplete';\nimport '@angular/material/chips';\nimport '@angular/material/dialog';\nimport '@angular/animations';\nimport '@angular/material/table';\nimport '@angular/material/paginator';\nimport '@angular/material/sort';\nimport '@angular/material/menu';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/keycodes';\nimport '@angular/cdk/portal';\nimport 'rxjs/operators';\nimport '@angular/cdk/overlay';\nconst _forTrack0 = ($index, $item) => $item.name;\nconst _c0 = () => [];\nfunction TtwrDatetimeInputComponent_For_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"i18n\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const validator_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"hidden\", !ctx_r1.field().control.hasError(validator_r1.name));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, validator_r1.message), \" \");\n  }\n}\nfunction TtwrDatetimeInputComponent_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"i18n\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, ctx), \" \");\n  }\n}\nclass TtwrDatetimeInputComponent {\n  constructor() {\n    this.field = input.required();\n  }\n  static {\n    this.ɵfac = function TtwrDatetimeInputComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TtwrDatetimeInputComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TtwrDatetimeInputComponent,\n      selectors: [[\"ttwr-datetime-input\"]],\n      inputs: {\n        field: [1, \"field\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([provideNativeDatetimeAdapter()], [{\n        provide: ControlContainer,\n        useExisting: FormGroupDirective\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 15,\n      consts: [[\"datetimePicker\", \"\"], [3, \"twelvehour\", \"touchUi\", \"timeInput\", \"mode\", \"type\"], [\"matInput\", \"\", 3, \"ttwrDatetimepicker\", \"formControl\", \"readonly\"], [\"matSuffix\", \"\", 3, \"disabled\", \"for\"], [3, \"hidden\"]],\n      template: function TtwrDatetimeInputComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-form-field\")(1, \"mat-label\");\n          i0.ɵɵtext(2);\n          i0.ɵɵpipe(3, \"i18n\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"ttwr-datetimepicker\", 1, 0)(6, \"input\", 2)(7, \"ttwr-datetimepicker-toggle\", 3);\n          i0.ɵɵelementStart(8, \"mat-error\");\n          i0.ɵɵrepeaterCreate(9, TtwrDatetimeInputComponent_For_10_Template, 3, 4, \"span\", 4, _forTrack0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, TtwrDatetimeInputComponent_Conditional_11_Template, 3, 3, \"mat-hint\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          let tmp_12_0;\n          let tmp_13_0;\n          const datetimePicker_r3 = i0.ɵɵreference(5);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 12, (tmp_1_0 = ctx.field().label) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx.field().name));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"twelvehour\", (tmp_2_0 = ctx.field().twelveHour) !== null && tmp_2_0 !== undefined ? tmp_2_0 : false)(\"touchUi\", (tmp_3_0 = (tmp_3_0 = ctx.field().touchUISignal) == null ? null : tmp_3_0()) !== null && tmp_3_0 !== undefined ? tmp_3_0 : false)(\"timeInput\", (tmp_4_0 = ctx.field().timeInput) !== null && tmp_4_0 !== undefined ? tmp_4_0 : true)(\"mode\", (tmp_5_0 = ctx.field().mode) !== null && tmp_5_0 !== undefined ? tmp_5_0 : \"auto\")(\"type\", (tmp_6_0 = ctx.field().pickerType) !== null && tmp_6_0 !== undefined ? tmp_6_0 : \"datetime\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ttwrDatetimepicker\", datetimePicker_r3)(\"formControl\", ctx.field().control)(\"readonly\", (tmp_9_0 = ctx.field().readonlySignal) == null ? null : tmp_9_0());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ((tmp_10_0 = ctx.field().disabledSignal) == null ? null : tmp_10_0()) || ((tmp_10_0 = ctx.field().readonlySignal) == null ? null : tmp_10_0()))(\"for\", datetimePicker_r3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵrepeater((tmp_12_0 = ctx.field().validators) !== null && tmp_12_0 !== undefined ? tmp_12_0 : i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional((tmp_13_0 = ctx.field().hint) ? 11 : -1, tmp_13_0);\n        }\n      },\n      dependencies: [LanguagePipe, MatFormFieldModule, i1.MatFormField, i1.MatLabel, i1.MatHint, i1.MatError, i1.MatSuffix, MatInputModule, i2.MatInput, MatButtonModule, MatIconModule, ReactiveFormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.FormControlDirective, TtwrDatetimepickerToggle, TtwrDatetimepickerInput, TtwrDatetimepicker],\n      styles: [\"mat-form-field[_ngcontent-%COMP%]{width:100%}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TtwrDatetimeInputComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ttwr-datetime-input',\n      standalone: true,\n      imports: [LanguagePipe, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, ReactiveFormsModule, TtwrDatetimepickerToggle, TtwrDatetimepickerInput, TtwrDatetimepicker],\n      viewProviders: [{\n        provide: ControlContainer,\n        useExisting: FormGroupDirective\n      }],\n      providers: [provideNativeDatetimeAdapter()],\n      template: \"<mat-form-field>\\r\\n  <mat-label>{{ (field().label ?? field().name) | i18n }}</mat-label>\\r\\n  <ttwr-datetimepicker\\r\\n    [twelvehour]=\\\"field().twelveHour ?? false\\\"\\r\\n    [touchUi]=\\\"field().touchUISignal?.() ?? false\\\"\\r\\n    [timeInput]=\\\"field().timeInput ?? true\\\"\\r\\n    [mode]=\\\"field().mode ?? 'auto'\\\"\\r\\n    [type]=\\\"field().pickerType ?? 'datetime'\\\"\\r\\n    #datetimePicker\\r\\n  />\\r\\n  <input\\r\\n    matInput\\r\\n    [ttwrDatetimepicker]=\\\"datetimePicker\\\"\\r\\n    [formControl]=\\\"field().control\\\"\\r\\n    [readonly]=\\\"field().readonlySignal?.()\\\"\\r\\n  >\\r\\n  <ttwr-datetimepicker-toggle\\r\\n    [disabled]=\\\"field().disabledSignal?.() || field().readonlySignal?.()\\\"\\r\\n    [for]=\\\"datetimePicker\\\"\\r\\n    matSuffix\\r\\n  />\\r\\n  <mat-error>\\r\\n    @for (validator of field().validators ?? []; track validator.name) {\\r\\n      <span [hidden]=\\\"!field().control.hasError(validator.name)\\\">\\r\\n          {{ validator.message | i18n }}\\r\\n        </span>\\r\\n    }\\r\\n  </mat-error>\\r\\n  @if (field().hint; as hint) {\\r\\n    <mat-hint>\\r\\n      {{ hint | i18n }}\\r\\n    </mat-hint>\\r\\n  }\\r\\n</mat-form-field>\\r\\n\",\n      styles: [\"mat-form-field{width:100%}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { TtwrDatetimeInputComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,IAAM,MAAM,MAAM,CAAC;AACnB,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,MAAM;AACnB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,CAAC,OAAO,MAAM,EAAE,QAAQ,SAAS,aAAa,IAAI,CAAC;AAC3E,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,aAAa,OAAO,GAAG,GAAG;AAAA,EAC5E;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,MAAM;AACnB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAQ,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG;AAAA,EAC3D;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,QAAQ,MAAM,SAAS;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,OAAO;AAAA,MACpB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,6BAA6B,CAAC,GAAG,CAAC;AAAA,QAClE,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,cAAc,WAAW,aAAa,QAAQ,MAAM,GAAG,CAAC,YAAY,IAAI,GAAG,sBAAsB,eAAe,UAAU,GAAG,CAAC,aAAa,IAAI,GAAG,YAAY,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,MACxN,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,gBAAgB,EAAE,GAAG,WAAW;AACrD,UAAG,OAAO,CAAC;AACX,UAAG,OAAO,GAAG,MAAM;AACnB,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,uBAAuB,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,8BAA8B,CAAC;AAC9F,UAAG,eAAe,GAAG,WAAW;AAChC,UAAG,iBAAiB,GAAG,4CAA4C,GAAG,GAAG,QAAQ,GAAG,UAAU;AAC9F,UAAG,aAAa;AAChB,UAAG,WAAW,IAAI,oDAAoD,GAAG,GAAG,UAAU;AACtF,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,cAAI;AACJ,gBAAM,oBAAuB,YAAY,CAAC;AAC1C,UAAG,UAAU,CAAC;AACd,UAAG,kBAAqB,YAAY,GAAG,KAAK,UAAU,IAAI,MAAM,EAAE,WAAW,QAAQ,YAAY,SAAY,UAAU,IAAI,MAAM,EAAE,IAAI,CAAC;AACxI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,eAAe,UAAU,IAAI,MAAM,EAAE,gBAAgB,QAAQ,YAAY,SAAY,UAAU,KAAK,EAAE,YAAY,WAAW,UAAU,IAAI,MAAM,EAAE,kBAAkB,OAAO,OAAO,QAAQ,OAAO,QAAQ,YAAY,SAAY,UAAU,KAAK,EAAE,cAAc,UAAU,IAAI,MAAM,EAAE,eAAe,QAAQ,YAAY,SAAY,UAAU,IAAI,EAAE,SAAS,UAAU,IAAI,MAAM,EAAE,UAAU,QAAQ,YAAY,SAAY,UAAU,MAAM,EAAE,SAAS,UAAU,IAAI,MAAM,EAAE,gBAAgB,QAAQ,YAAY,SAAY,UAAU,UAAU;AACliB,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,sBAAsB,iBAAiB,EAAE,eAAe,IAAI,MAAM,EAAE,OAAO,EAAE,aAAa,UAAU,IAAI,MAAM,EAAE,mBAAmB,OAAO,OAAO,QAAQ,CAAC;AACxK,UAAG,UAAU;AACb,UAAG,WAAW,cAAc,WAAW,IAAI,MAAM,EAAE,mBAAmB,OAAO,OAAO,SAAS,QAAQ,WAAW,IAAI,MAAM,EAAE,mBAAmB,OAAO,OAAO,SAAS,EAAE,EAAE,OAAO,iBAAiB;AAClM,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,WAAW,IAAI,MAAM,EAAE,gBAAgB,QAAQ,aAAa,SAAY,WAAc,gBAAgB,IAAI,GAAG,CAAC;AAC7H,UAAG,UAAU,CAAC;AACd,UAAG,eAAe,WAAW,IAAI,MAAM,EAAE,QAAQ,KAAK,IAAI,QAAQ;AAAA,QACpE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAc,oBAAuB,cAAiB,UAAa,SAAY,UAAa,WAAW,gBAAmB,UAAU,iBAAiB,eAAe,qBAAwB,sBAAyB,iBAAoB,sBAAsB,0BAA0B,yBAAyB,kBAAkB;AAAA,MACnV,QAAQ,CAAC,+CAA+C;AAAA,IAC1D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,oBAAoB,gBAAgB,iBAAiB,eAAe,qBAAqB,0BAA0B,yBAAyB,kBAAkB;AAAA,MACtL,eAAe,CAAC;AAAA,QACd,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,WAAW,CAAC,6BAA6B,CAAC;AAAA,MAC1C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,8BAA8B;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}