import type { CreateObserverDto, ObservationViewModelDto, UpdateObserverDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { UserTrackAccountAssociationDto } from '../user-track-account-associations/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class ObservationService {
  apiName = 'Default';
  

  activateObserver = (userId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/observation/activateObserver/${userId}`,
    },
    { apiName: this.apiName,...config });
  

  addObservationVehicle = (userTrackAccountAssociationId: string, vehicleId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/observation/observationVehicle',
      params: { userTrackAccountAssociationId, vehicleId },
    },
    { apiName: this.apiName,...config });
  

  addObservationVehicleGroup = (userTrackAccountAssociationId: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/observation/observationVehicleGroup',
      params: { userTrackAccountAssociationId, vehicleGroupId },
    },
    { apiName: this.apiName,...config });
  

  createObserver = (input: CreateObserverDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/observation/observer',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  deactivateObserver = (userId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/observation/deactivateObserver/${userId}`,
    },
    { apiName: this.apiName,...config });
  

  deleteObserver = (phoneNumber: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/observation/observer',
      params: { phoneNumber },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<UserTrackAccountAssociationDto>>({
      method: 'GET',
      url: '/api/app/observation',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListVehicleAndVehicleGroupOfObsever = (userTrackAccountAssociationId: string, input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ObservationViewModelDto>>({
      method: 'GET',
      url: `/api/app/observation/vehicleAndVehicleGroupOfObsever/${userTrackAccountAssociationId}`,
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  removeObservationVehicle = (userTrackAccountAssociationId: string, vehicleId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/observation/observationVehicle',
      params: { userTrackAccountAssociationId, vehicleId },
    },
    { apiName: this.apiName,...config });
  

  removeObservationVehicleGroup = (userTrackAccountAssociationId: string, vehicleGroupId: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/observation/observationVehicleGroup',
      params: { userTrackAccountAssociationId, vehicleGroupId },
    },
    { apiName: this.apiName,...config });
  

  updateObserver = (input: UpdateObserverDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, UserTrackAccountAssociationDto>({
      method: 'PUT',
      url: '/api/app/observation/observer',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
