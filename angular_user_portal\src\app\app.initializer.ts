import { eLayoutType, LocalizationService, RoutesService } from '@abp/ng.core';
import { inject } from '@angular/core';

export const appInitializer = () => {
  const routesService = inject(RoutesService);
  const localization = inject(LocalizationService);

  localization.languageChange$.subscribe(() => {
    location.reload();
  });

  return () => {
    routesService.add([
      {
        path: '/',
        name: '::Menu:Home',
        iconClass: 'home',
        order: 1,
        layout: eLayoutType.application,
      },
    ]);
  };
};
