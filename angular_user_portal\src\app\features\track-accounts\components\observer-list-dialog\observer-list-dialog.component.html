<div class="p-6 max-w-2xl">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800">{{ 'UserPortal:ObserversList' | i18n }}</h2>
  </div>

  <div class="space-y-4">
    @for (observer of data; track observer.id) {
    <div
      class="flex flex-row items-center justify-between p-4 shadow-md rounded-md border border-main_light_gray"
    >
      <div class="flex items-center space-x-4">
        <img src="/assets/images/sms/مراقب.svg" alt="Observer" class="w-6 h-6" />
        <div>
          <h3 class="text-sm text-gray-900">{{ observer.name }}</h3>
          <p class="text-xs text-gray-500">{{ observer.phoneNumber }}</p>
        </div>
      </div>

      <button mat-icon-button (click)="handleAction(observer.id)">
        <mat-icon>{{ deletedObservers$().includes(observer.id) ? 'refresh' : 'delete' }}</mat-icon>
      </button>
    </div>
    } @if (data.length === 0) {
    <div class="text-center py-8 text-gray-500">
      {{ 'UserPortal:NoObserversFound' | i18n }}
    </div>
    }
  </div>
  <div class="flex justify-center mt-4">
    <button mat-button mat-flat-button (click)="close()">
      {{ 'UserPortal:Close' | i18n }}
    </button>
  </div>
</div>
