import { LocalizationService } from '@abp/ng.core';
import { TenantService } from '@abp/ng.tenant-management/proxy';
import { Component, DestroyRef, inject, OnDestroy, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import {
  AlertService,
  fields,
  LOADING,
  model,
  TtwrGridComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { gridOptionToAbpOptions } from '../abp-overrides.utils';
import { FeatureManagementDialogComponent } from '../feature-management-dialog/feature-management-dialog.component';
import { TenantManagementCreateDialogComponent } from './tenant-management-create-dialog/tenant-management-create-dialog.component';
import { TenantManagementUpdateDialogComponent } from './tenant-management-update-dialog/tenant-management-update-dialog.component';

@Component({
  selector: 'app-tenant-management',
  standalone: true,
  imports: [TtwrGridComponent],
  templateUrl: './tenant-management.component.html',
  styleUrl: './tenant-management.component.scss',
})
export class TenantManagementComponent implements OnDestroy {
  private localization = inject(LocalizationService);
  private tenants = inject(TenantService);
  private alert = inject(AlertService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = model({
    id: fields.text(),
    name: fields.text(),

    // hidden
    concurrencyStamp: fields.text(),
  }).grid({
    title: this.localization.instant('AbpTenantManagement::Tenants'),
    refreshSubject: this.refreshSubject,
    defaultActions: {
      sort: false,
      filters: false,
      pdf: false,
      print: false,
      excel: false,
      toggleAll: false,
    },
    dataFunc: (...args) =>
      this.tenants.getList(gridOptionToAbpOptions(args)).pipe(map(res => res as any)),
    fields: {
      concurrencyStamp: {
        hiddenSignal: signal(true),
      },
    },
    actions: [
      {
        label: this.localization.instant('AbpTenantManagement::NewTenant'),
        delegateFunc: () => {
          const ref = this.dialog.open(TenantManagementCreateDialogComponent, {
            width: '500px',
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) this.refreshSubject.next();
            });
        },
      },
    ],
    fieldActions: [
      {
        color: 'primary',
        label: 'Edit',
        delegateFunc: obj => {
          const ref = this.dialog.open(TenantManagementUpdateDialogComponent, {
            width: '500px',
            data: obj,
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) this.refreshSubject.next();
            });
        },
      },
      {
        color: 'accent',
        label: this.localization.instant('AbpTenantManagement::Permission:ManageFeatures'),
        delegateFunc: obj =>
          this.dialog.open(FeatureManagementDialogComponent, {
            data: obj.id,
            maxWidth: '800px',
            width: '100%',
          }),
      },
      {
        color: 'warn',
        label: 'Delete',
        confirmation: {
          title: this.localization.instant('AbpUi::AreYouSure'),
          message: this.localization.instant(
            'AbpTenantManagement::TenantDeletionConfirmationMessage',
            ''
          ),
        },
        delegateFunc: obj => {
          this.loading.set(true);
          this.tenants.delete(obj.id).subscribe({
            next: () => {
              this.alert.success(this.localization.instant('AbpUi::SuccessfullyDeleted'));
              this.refreshSubject.next();
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
        },
      },
    ],
  });

  ngOnDestroy() {
    this.loading.set(false);
  }
}
