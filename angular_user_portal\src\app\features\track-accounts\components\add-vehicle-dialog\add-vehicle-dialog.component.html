<div class="p-6 bg-white rounded-lg shadow-lg text-sm">
  <h2 class="font-semibold text-indigo-600">{{ 'UserPortal:addVehicle' | i18n }}</h2>

  <div class="border-t border-gray-300 my-1 pb-1"></div>

  <div class="space-y-2">
    <form [formGroup]="form">
      <div>
        <mat-label class="ms-2">{{ 'UserPortal:licensePlateSubClass' | i18n }}</mat-label>
        <mat-form-field>
          <mat-select
            matInput
            [placeholder]="'UserPortal:licensePlateSubClass' | i18n"
            formControlName="licensePlateSubClass"
            required
          >
            @for (item of VehicleLicensePlateSubClass; track $index) {
            <mat-option [value]="item.value">{{
              'Enum:VehicleLicensePlateSubClass.' + $index | i18n
            }}</mat-option>
            }
          </mat-select>
          <mat-error>
            <app-validation [errors]="form.controls.licensePlateSubClass" />
          </mat-error>
        </mat-form-field>
      </div>

      <div>
        <mat-label class="ms-2">{{ 'UserPortal:licensePlateSerial' | i18n }}</mat-label>
        <mat-form-field>
          <input
            matInput
            [placeholder]="'UserPortal:licensePlateSerial' | i18n"
            formControlName="licensePlateSerial"
            required
          />
          <mat-error>
            <app-validation [errors]="form.controls.licensePlateSerial" />
          </mat-error>
        </mat-form-field>
      </div>

      <div>
        <mat-label class="ms-2">{{ 'UserPortal:consumptionRate' | i18n }}</mat-label>
        <mat-form-field>
          <input
            matInput
            [type]="'number'"
            [placeholder]="'UserPortal:consumptionRate' | i18n"
            formControlName="consumptionRate"
            required
          />
          <mat-hint [align]="'end'"><small>Km/20L</small> </mat-hint>
          <mat-error>
            <app-validation [errors]="form.controls.consumptionRate" />
          </mat-error>
        </mat-form-field>
      </div>
      <div>
        <mat-label class="ms-2">{{ 'UserPortal:color' | i18n }}</mat-label>
        <mat-form-field appearance="outline" class="w-full">
          <mat-select
            formControlName="color"
            #e
            panelClass="flex flex-wrap"
            [ngStyle]="{ backgroundColor: e.value }"
          >
            @for (color of colors(); track color) {
            <mat-option [value]="color"
              ><div class="size-8" [ngStyle]="{ backgroundColor: color }"></div
            ></mat-option>
            }
          </mat-select>
          <mat-error>
            <app-validation [errors]="form.controls.color" />
          </mat-error>
        </mat-form-field>
      </div>
      <div>
        <div class="flex flex-wrap mb-3">
          <mat-label class="pr-2 w-full">{{ 'UserPortal:needsTrackingDevice' | i18n }}</mat-label>
          <mat-radio-group formControlName="needsTrackingDevice">
            <mat-radio-button [value]="false">
              {{ 'UserPortal:No' | i18n }}
            </mat-radio-button>
            <mat-radio-button [value]="true">
              {{ 'UserPortal:Yes' | i18n }}
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
    </form>
  </div>

  <div class="text-right mt-4 text-sm grid gap-4 grid-cols-2">
    <button mat-button mat-flat-button class="cancleButton" (click)="closeDialog()">
      {{ 'UserPortal:cancel' | i18n }}
    </button>
    <button mat-button mat-flat-button [disabled]="!form.valid" (click)="save()">
      {{ 'UserPortal:save' | i18n }}
    </button>
  </div>
</div>
