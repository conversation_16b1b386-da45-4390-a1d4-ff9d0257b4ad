<section
  id="pricing"
  class="pricing-content section-padding py-3 flex justify-center bg-[url('/assets/images/background-grad.png')] bg-cover min-h-svh w-full"
>
  <div class="w-11/12 bg-white rounded-3xl shadow-lg flex justify-center">
    <div class="flex w-full xl:w-4/5 flex-wrap justify-center gap-y-4">
      @for (plan of plans|keyvalue; track plan; let i = $index) {
      <div class="w-full sm:w-1/2 lg:w-1/3 p-0">
        <div class="p-1 h-full">
          <div
            class="rounded-2xl border border-[#D1C4E9] border-solid h-full relative flex flex-col"
          >
            <div class="relative">
              <img src="/assets/images/{{ plan.value.key }}.svg" class="w-full" alt="..." />
              <div class="absolute top-0 w-full text-center text-white">
                <h2 class="mb-5 text-2xl bg-white bg-opacity-15 rounded-xl p-3">
                  {{ plan.value.localizedName }}
                </h2>
                <h4 class="text-xl mb-2">{{ 'price per vehicle' | i18n }}</h4>
                <h1 class="text-6xl font-light">{{ plan.value.price }} {{ 'SP' | i18n }}</h1>
                <h4 class="text-xl mt-2">
                  {{ 'UserPortal:userCount' | i18n }} : {{ plan.value.userCount }}
                </h4>
              </div>
            </div>
            <div class="card-body flex-grow">
              <div class="px-6 text-main_gray">
                <ul class="divide-y-2">
                  @for (feature of features.get(plan.value.key); track feature) {
                  <li class="flex justify-between py-2">
                    {{ feature.displayName }}
                    @if (feature.value=='True') {
                    <mat-icon class="text-[#8bc34a] align-middle">done</mat-icon>
                    } @else {
                    <mat-icon class="text-main_red align-middle">close</mat-icon>

                    }
                  </li>
                  }
                </ul>
              </div>
            </div>
            <div class="flex justify-center m-4">
              <button mat-flat-button class="w-full" (click)="setPlan(plan.value)">
                أطلب الأن
              </button>
            </div>
          </div>
        </div>
      </div>
      }
    </div>
  </div>
</section>
