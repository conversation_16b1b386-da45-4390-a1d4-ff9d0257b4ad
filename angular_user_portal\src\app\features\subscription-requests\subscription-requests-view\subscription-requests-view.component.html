<div>
  <div class="flex justify-between m-4">
    <img src="assets/images/logo/LogoColored.png" class="" alt="" />
    <button class="top-4 end-4" mat-button [routerLink]="['/subscription-requests']">
      {{ 'UserPortal:Back' | i18n }}
    </button>
  </div>
  @if (request()) {
  <div class="grid grid-cols-1 lg:grid-cols-[2fr_4fr_1fr] p-8 pt-0 gap-8">
    <div
      class="flex gap-2 p-8 text-white bg-opacity-90 rounded-3xl shadow-lg drop-shadow-lg lg:flex-col bg-main_perple h-fit"
    >
      <div>
        @if (request().status=='Pending') {
        <button class="e" mat-icon-button [matMenuTriggerFor]="menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button mat-menu-item (click)="cancle()">
            <span>{{ 'cancle' | i18n }}</span>
          </button>
        </mat-menu>
        }
      </div>
      <div class="flex-grow">
        <div>{{ 'UserPortal:Hello' | i18n }} {{ request().accountName }}</div>
        <div class="my-2 font-semibold">
          <span>{{ 'UserPortal:Order Status' | i18n }}:</span
          ><span class="mx-3 text-main_red">{{ request().status | i18n }}</span>
        </div>
        <p>
          {{ 'UserPortal:status_text.' + request().status | i18n }}
        </p>
      </div>
      <div class="flex justify-center items-center">
        <img src="assets/images/svg/pin.svg" class="size-28" alt="" />
      </div>
    </div>
    <div>
      <div class="flex-grow p-8 bg-white bg-opacity-90 rounded-3xl shadow-lg drop-shadow-lg">
        <div class="flex">
          <div class="flex-grow me-4">
            <div class="mb-6 font-semibold">{{ 'UserPortal:notes' | i18n }}</div>
            <div class="grid grid-cols-[auto_2fr] gap-x-4 gap-y-2">
              @for (item of request().requestNotes; track $index) {
              <div class="text-main_gray">{{ 'UserPortal:note' | i18n }} {{ $index }}</div>
              <div>{{ item.note }}</div>
              }
            </div>
            <div class="mt-6 mb-6 font-semibold">{{ 'UserPortal:Order Information' | i18n }}</div>
            <div class="grid grid-cols-[auto_2fr] gap-x-4 gap-y-2">
              @if (request().type == 'BusinessAccountSubscription') {
              <div class="text-main_gray">{{ 'UserPortal:companyName' | i18n }}</div>
              <div>{{ request().companyName }}</div>

              <div class="text-main_gray">{{ 'UserPortal:companyAddress' | i18n }}</div>
              <div>
                {{ request().companyAddress.governorateDisplayName }} -
                {{ request().companyAddress.cityDisplayName }}
              </div>
              }

              <div class="text-main_gray">{{ 'UserPortal:requestDate' | i18n }}</div>
              <div>{{ request().creationTime | date }}</div>
              <div class="text-main_gray">{{ 'UserPortal:requesrType' | i18n }}</div>
              <div>{{ request().type | i18n }}</div>
              <div class="text-main_gray">
                {{ 'UserPortal:trackerInstallationLocation' | i18n }}
              </div>
              <div>{{ request().trackerInstallationLocation | i18n }}</div>
              <div class="text-main_gray">{{ 'UserPortal:trackVehicleCount' | i18n }}</div>
              <div>{{ request().trackVehicles.length }}</div>
              <div class="text-main_gray">{{ 'UserPortal:subscriptionPlan' | i18n }}</div>
              <div>{{ request().subscriptionPlanLocalizedName }}</div>
              <div class="text-main_gray">
                {{ 'UserPortal:subscriptionDurationInMonths' | i18n }}
              </div>
              <div>{{ request().subscriptionDurationInMonths }}</div>
              <div class="text-main_gray">{{ 'UserPortal:smsBundleId' | i18n }}</div>
              <div>
                {{ smsPundel().name }} ( {{ smsPundel().messagesCount }} ) {{ smsPundel().price }}
                {{ 'SP' | i18n }}
              </div>

              <div class="text-main_gray">{{ 'UserPortal:observerCount' | i18n }}</div>
              <div>{{ request().userCount }}</div>

              <div class="text-main_gray">{{ 'UserPortal:stage' | i18n }}</div>
              <div>{{ request().stage | i18n }}</div>
            </div>
          </div>
        </div>
        <div class="flex mt-4 justify-around">
          <button mat-button (click)="openPriceOffer()">
            {{ 'UserPortal:PriceOffer' | i18n }}
          </button>
          @if (request().stage == "Payment") {
          <button mat-flat-button (click)="payment()">
            {{ 'UserPortal:MakePayment' | i18n }}
          </button>
          }
        </div>
      </div>
      <div
        class="flex justify-between p-4 mt-4 mb-4 text-white bg-opacity-90 rounded-xl shadow-lg drop-shadow-lg bg-main_perple"
      >
        <div>
          {{ 'UserPortal:Vehicles to be Tracked' | i18n }}
        </div>
        <div>
          <button mat-mini-fab>
            <mat-icon (click)="open.set(!open())" [ngClass]="{ 'rotate-180': open() }"
              >arrow_drop_down</mat-icon
            >
          </button>
        </div>
      </div>
      @for (item of request().trackVehicles; track $index) {
      <div auto-animate>
        @if (open()) {
        <div class="flex-grow p-8 my-2 bg-white bg-opacity-90 rounded-3xl shadow-lg drop-shadow-lg">
          <div class="flex">
            <div class="flex-grow me-4">
              <div class="mb-3 font-semibold">
                {{ 'UserPortal:Vehicle' | i18n }} {{ $index + 1 }}
              </div>
              <div class="grid grid-cols-[auto_2fr] gap-x-4 gap-y-2 text-main_gray">
                <div>{{ 'UserPortal:Plate Type' | i18n }}</div>
                <div>{{ item.licensePlateSubClass }}</div>
                <div>{{ 'UserPortal:Plate Number' | i18n }}</div>
                <div>{{ item.licensePlateSerial }}</div>
                <div>{{ 'UserPortal:Vehicle Color' | i18n }}</div>
                <div>
                  <div
                    class="rounded-full size-6"
                    [ngStyle]="{ background: item.colorHex | hexToColor }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        }
      </div>
      }
    </div>
  </div>
  }
</div>
