import type { AlertDefinitionDto, CreateAlertDefinitionBaseDto } from '../../dtos/models';

export interface CreateJobTimeAlertDefinitionDto extends CreateAlertDefinitionBaseDto {
  name?: string;
  startTime?: string;
  endTime?: string;
  daysOfWeek: any[];
}

export interface JobTimeAlertDefinitionDto extends AlertDefinitionDto {
  name?: string;
  startTime?: string;
  endTime?: string;
  daysOfWeek: any[];
}
