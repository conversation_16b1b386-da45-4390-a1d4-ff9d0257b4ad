import { <PERSON><PERSON><PERSON>, NgStyle } from '@angular/common';
import { Component, computed, inject, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormBuilder,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { MatButton, MatMiniFabButton } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { Router, RouterLink } from '@angular/router';
import { GeoNodeService } from '@proxy/mobile/geo-nodes';
import { DiscountService } from '@proxy/mobile/payments/discounts';
import { SubscriptionDurationDiscountDto } from '@proxy/mobile/payments/discounts/dtos';
import { SubscriptionVehicleInfoCreateDto } from '@proxy/mobile/requests/account-subscription-requests';
import { BusinessAccountSubscriptionRequestService } from '@proxy/mobile/requests/account-subscription-requests/business-account-subscription-requests';
import { PersonalAccountSubscriptionRequestService } from '@proxy/mobile/requests/account-subscription-requests/personal-account-subscription-requests';
import { SmsBundleService } from '@proxy/mobile/sms-bundles';
import { SubscriptionPlanService } from '@proxy/mobile/subscription-plans';

import { VehicleLicensePlateSubClass } from '@proxy/vehicles/license-plates';
import { car_svg } from '@shared';
import { openPriceOfferDialog } from '@shared/components/price-offer-dialog/price-offer-dialog.component';
import { ValidationComponent } from '@shared/components/validation/validation.component';
import { hexToColor } from '@shared/functions/hex-to-color';
import { AlertService, LanguagePipe, SafeHtmlPipe } from '@ttwr-framework/ngx-main-visuals';
import { firstValueFrom, map, Observable, of, switchMap } from 'rxjs';
import * as XLSX from 'xlsx';
import { TempSubscriptionCacheService } from '../../services/temp-subscription-cache.service';
import { AddVehicleDialogComponent } from '../add-vehicle-dialog/add-vehicle-dialog.component';
import { customValidatitors } from '@shared/helper-assets/validation';
import { ConfirmationDialogService } from '@shared/components/confirmation-dialog';

@Component({
  selector: 'app-create-track-account',
  standalone: true,
  templateUrl: `./create-track-account.component.html`,
  styleUrl: `./create-track-account.component.scss`,
  imports: [
    NgClass,
    FormsModule,
    ReactiveFormsModule,
    MatButton,
    LanguagePipe,
    SafeHtmlPipe,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    RouterLink,
    NgStyle,
    MatIcon,
    MatMenuModule,
    ValidationComponent,
    MatRadioModule,
    MatMiniFabButton,
  ],
})
export class CreateTrackAccountComponent {
  months = signal([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
  fileVehicles: SubscriptionVehicleInfoCreateDto[] = [];
  step = signal<number>(1);
  pro = signal<boolean>(false);
  vehicleCount = signal<number>(1);
  trackersCount = signal<number>(1);
  excel = signal<boolean>(false);
  proOption = computed(() => {
    const requiredFields = ['companyName', 'address.governorate', 'address.city'];
    requiredFields.forEach(field => {
      const control = this.final.get(field);
      if (control) {
        this.pro() ? control.setValidators([Validators.required]) : control.clearValidators();
        control.updateValueAndValidity();
      }
    });
    return this.pro();
  });
  withSms = signal(false);
  discounts$ = signal<Map<number, SubscriptionDurationDiscountDto>>(new Map());
  private alertService = inject(AlertService);
  private _formBuilder = inject(FormBuilder);
  private geoNodeService = inject(GeoNodeService);
  private discountService = inject(DiscountService);
  private subscriptionPlanService = inject(SubscriptionPlanService);
  private tempSubscriptionCacheService = inject(TempSubscriptionCacheService);
  private smsBundleService = inject(SmsBundleService);
  private confirmationDialogService = inject(ConfirmationDialogService);
  private personalAccountSubscriptionRequestService = inject(
    PersonalAccountSubscriptionRequestService
  );
  private businessAccountSubscriptionRequestService = inject(
    BusinessAccountSubscriptionRequestService
  );
  private dialog = inject(MatDialog);
  private router = inject(Router);
  totalCapacity = 20;

  arrays = {
    Country: [],
    Governorate: [],
    City: [],
    Area: [],
  };

  car_svg = car_svg;
  plans = toSignal(this.subscriptionPlanService.getList());
  smsBundles = toSignal(
    this.smsBundleService.getList({ maxResultCount: 999, skipCount: 0 }).pipe(map(sms => sms.items))
  );

  final = this._formBuilder.group({
    subscriptionPlanKey: this._formBuilder.control(null, Validators.required),
    promoCode: this._formBuilder.control(null),
    companyName: this._formBuilder.control('', Validators.required),
    address: this._formBuilder.group({
      country: this._formBuilder.control('', Validators.required),
      governorate: this._formBuilder.control('', Validators.required),
      city: this._formBuilder.control('', Validators.required),
      area: this._formBuilder.control(''),
      street: this._formBuilder.control(''),
    }),
    mach: this._formBuilder.control(true),
    accountName: this._formBuilder.control('', Validators.required),
    userCount: this._formBuilder.control(null, []),
    smsBundleId: this._formBuilder.control(null),
    subscriptionDurationInMonths: this._formBuilder.control(1, [Validators.required]),
    subscriptionVehicleInfoCreateDtos: this._formBuilder.array<SubscriptionVehicleInfoCreateDto>(
      []
    ),
  });

  obs = this.final
    .get('address')
    ?.get('governorate')
    ?.valueChanges.pipe(
      map((value: any) => {
        return this.arrays.City.filter(item => item.parentId === value.id);
      })
    );
  city = toSignal(this.obs);

  planObs$ = this.final.get('subscriptionPlanKey').valueChanges.pipe(
    map((value: any) => {
      const plan = this.plans().find(val => val.key === value);
      this.final.controls.userCount.setValue(plan.userCount);
      if (plan.relatedSubscriptionDurationMonths)
        this.months.set(plan.relatedSubscriptionDurationMonths);
      else {
        this.months.set([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
      }
      return plan.userCount;
    })
  );

  selectedPlan$ = toSignal(this.planObs$);

  ngOnInit(): void {
    if (this.tempSubscriptionCacheService.take.pop()) {
      this.final.patchValue(this.tempSubscriptionCacheService.getValues());
    }
    this.final.controls.smsBundleId.addAsyncValidators(
      customValidatitors.smsBundleValidation(this.withSms)
    );
    this.getGeoNodes();
    this.getDiscounts();
  }

  getDiscounts() {
    this.discountService
      .getSubscriptionDurationDiscounts()
      .pipe(
        map(discount => {
          const map = new Map();
          discount.map(d => {
            map.set(d.months, d);
          });
          this.discounts$.set(map);
        })
      )
      .subscribe();
  }

  getGeoNodes() {
    this.geoNodeService
      .get()
      .pipe(
        map(geo => {
          geo[0].geoNodes.map(node => {
            this.arrays[node.type].push(node);
          });
          this.final.get('address')?.get('country')?.setValue(this.arrays.Country[0].name);
        })
      )
      .subscribe();
  }

  addVehiclesDialog(index: number | null = null) {
    let data: any = null;
    if (index != null) {
      data = this.final.controls.subscriptionVehicleInfoCreateDtos.at(index);
    }
    const ref = this.dialog.open(AddVehicleDialogComponent, { data: data });
    ref.afterClosed().subscribe(res => {
      if (res) {
        if (index != null) {
          this.remove(index);
        }
        this.final.controls.subscriptionVehicleInfoCreateDtos.push(res);
      }
    });
  }

  async save() {
    const data = {
      ...this.final.getRawValue(),
      vehicleCount: this.vehicleCount(),
      deviceCount: this.trackersCount(),
    };
    if (!this.excel() && this.final.controls.subscriptionVehicleInfoCreateDtos.length == 0) {
      this.alertService.error('UserPortal:vehicleCountError');
      return;
    }
    data.address.governorate = (data.address.governorate as any).name;
    data.address.city = (data.address.city as any).name;
    data.address.area = data.address.area || 'area';
    data.address.street = data.address.street || 'street';

    let req = this.pro()
      ? this.businessAccountSubscriptionRequestService.createTempBill(data)
      : this.personalAccountSubscriptionRequestService.createTempBill(data);

    if (this.excel() && this.fileVehicles.length == 0) {
      req = this.businessAccountSubscriptionRequestService.createTempBillWithoutVehiclesData(data);
    }

    const bill = await firstValueFrom(req);
    openPriceOfferDialog(this.dialog, { ...bill, pay: false })
      .afterClosed()
      .pipe(
        switchMap(val => {
          if (val == false) {
            if (this.excel() && this.fileVehicles.length == 0) {
              this.step.set(3);
              return of(null);
            } else {
              return this.confirmationDialogService
                .open({
                  payConfirm: true,
                })
                .pipe(
                  switchMap(() => {
                    return this.createTrackAccount();
                  })
                );
            }
          }
        })
      )
      .subscribe();
  }
  createTrackAccount() {
    const data = this.final.getRawValue();
    data.address.governorate = (data.address.governorate as any).name;
    data.address.city = (data.address.city as any).name;

    data.subscriptionVehicleInfoCreateDtos = data.subscriptionVehicleInfoCreateDtos.map(
      (item: {
        color: string;
        consumptionRate: number;
        licensePlateSubClass: VehicleLicensePlateSubClass;
        licensePlateSerial: string;
        needsTrackingDevice: boolean;
      }) => {
        item.color = hexToColor(item.color);
        item.consumptionRate = +(item.consumptionRate / 20).toFixed(2);
        return item;
      }
    );
    const req = this.pro()
      ? this.businessAccountSubscriptionRequestService.create(data)
      : this.personalAccountSubscriptionRequestService.create(data);

    return req.pipe(
      map(() => {
        this.router.navigate(['track-accounts']);
      })
    );
  }

  remove(index: number) {
    this.final.controls.subscriptionVehicleInfoCreateDtos.removeAt(index);
  }

  goToPlans() {
    this.router.navigate(['/plans'], { queryParams: { select: 'true' } });
    this.tempSubscriptionCacheService.setValues(this.final.value);
  }

  onFileSelected(ev: any) {
    const file = ev.target.files[0];
    if (!file) {
      return;
    }

    // Clear existing vehicles before adding new ones
    while (this.final.controls.subscriptionVehicleInfoCreateDtos.length > 0) {
      this.final.controls.subscriptionVehicleInfoCreateDtos.removeAt(0);
    }

    const reader = new FileReader();
    reader.onload = (event: any) => {
      try {
        const arrayBuffer = event.target.result;
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const json = XLSX.utils.sheet_to_json(worksheet);

        if (json && json.length > 0) {
          const vehicles = this.transformToVehicles(json);

          const trackersNeeded = vehicles.filter(v => v.needsTrackingDevice).length;
          if (vehicles.length != this.vehicleCount()) {
            this.alertService.error('UserPortal:vehicleCountError');
          } else if (trackersNeeded != this.trackersCount()) {
            this.alertService.error('UserPortal:trackersCountError');
          } else {
            this.fileVehicles = vehicles;
          }
        }
      } catch (error) {
        console.error('Error processing Excel file:', error);
      }
    };

    reader.onerror = error => {
      console.error('Error reading file:', error);
    };

    // Use readAsArrayBuffer instead of readAsBinaryString (which is deprecated)
    reader.readAsArrayBuffer(file);
  }

  transformToVehicles(json: any[]): SubscriptionVehicleInfoCreateDto[] {
    if (!json || !json.length) {
      return [];
    }

    const vehicles: SubscriptionVehicleInfoCreateDto[] = [];

    for (const item of json) {
      // Extract values from the JSON object
      if (
        !item['License Plate Sub Class'] ||
        !item['License Plate Serial'] ||
        !item['Color Hex'] ||
        !item['Consumption Rate (km per 1 liters) '] ||
        !item['Needs tracking device (yes / no)']
      ) {
        continue;
      }
      const licensePlateSubClass = item['License Plate Sub Class'];
      const licensePlateSerial = item['License Plate Serial']?.toString();

      // Handle color - prioritize 'Color Hex' but fall back to __EMPTY_2 if available
      let color = item['Color Hex'];

      // Parse consumption rate - convert to number and handle different formats
      let consumptionRate = 0;
      if (item['Consumption Rate (km per 1 liters) '] !== undefined) {
        consumptionRate = Number(item['Consumption Rate (km per 1 liters) ']);
      }

      // Parse tracking device need - convert various formats to boolean
      let needsTrackingDevice = false;
      const trackingDeviceValue = item['Needs tracking device (yes / no)'] || '';
      needsTrackingDevice = trackingDeviceValue.toLowerCase() === 'yes';

      // Create vehicle DTO
      const vehicle: SubscriptionVehicleInfoCreateDto = {
        licensePlateSubClass: licensePlateSubClass as VehicleLicensePlateSubClass,
        licensePlateSerial: licensePlateSerial,
        color: color,
        consumptionRate: consumptionRate,
        needsTrackingDevice: needsTrackingDevice,
      };

      vehicles.push(vehicle);
    }

    return vehicles;
  }

  setFileData() {
    this.fileVehicles.map(val => {
      const form = this._formBuilder.group({
        color: [val.color],
        consumptionRate: [val.consumptionRate],
        licensePlateSubClass: [val.licensePlateSubClass],
        licensePlateSerial: [val.licensePlateSerial],
        needsTrackingDevice: [val.needsTrackingDevice],
      });
      this.final.controls.subscriptionVehicleInfoCreateDtos.push(form as any);
    });
    this.step.set(2);
  }

  reupload() {
    this.final.controls.subscriptionVehicleInfoCreateDtos.reset();
    this.fileVehicles = [];
    this.step.set(3);
  }
}
