import { DatePipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { RequestService } from '@proxy/mobile/requests';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';

@Component({
  selector: 'app-subscription-requests',
  standalone: true,
  templateUrl: `./subscription-requests.component.html`,
  imports: [RouterLink, MatButton, MatIcon, DatePipe, LanguagePipe],
})
export class SubscriptionRequestsComponent {
  requestService = inject(RequestService);

  orders = toSignal(
    this.requestService
      .getList({
        skipCount: 0,
        maxResultCount: 999,
        types: ['BusinessAccountSubscription', 'PersonalAccountSubscription'],
        status: [],
      })
      .pipe(
        map(val => {
          return val.items;
        })
      )
  );
}
