<div class="flex flex-col justify-center items-center h-full">
  <mat-card class="w-3/4 shadow-lg rounded-lg transition-all duration-500 h-11/12 card-blue">
    <form [formGroup]="form" class="p-6">
      <h2 class="main-header">{{ 'UserPortal:basic information' | i18n }}</h2>

      <div class="flex flex-col">
        <div class="m-4">
          <mat-label class="px-2">{{ 'UserPortal:name' | i18n }}</mat-label>
          <mat-form-field appearance="outline" class="w-full">
            <input formControlName="name" matInput />
          </mat-form-field>
        </div>
        <div class="h-60 w-full">
          <app-map
            class="h-full"
            (change_nodes)="onNodeChange($event)"
            [draw]="{polygon:true,circle:true,}"
          />
        </div>
      </div>
      <div class="flex justify-between mt-6">
        <button mat-button mat-flat-button class="cancleButton" [routerLink]="['/main/alerts']">
          {{ 'UserPortal:cancel' | i18n }}
        </button>
        <button mat-button mat-flat-button (click)="save()">
          {{ 'UserPortal:save' | i18n }}
        </button>
      </div>
    </form>
  </mat-card>
</div>
