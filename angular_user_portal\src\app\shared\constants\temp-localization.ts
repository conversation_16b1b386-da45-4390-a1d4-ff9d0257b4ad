export const tempLocalization = [
  {
    culture: 'en',
    resources: [
      {
        resourceName: 'GoTrack',
        texts: {
          'UserPortal:change account': 'Change account',
          'UserPortal:my requests': 'My requests',
          'UserPortal:virify email': 'Verify email',
          'UserPortal:EmailIsVerifyedpanner':
            'If you want to change your verified email click on change',
          'UserPortal:VerifyEmail': 'Verify Email',
          'UserPortal:EmailIsVerifyed': 'Email Is Verified',
          'UserPortal:EnterEmailToVerify': 'Enter your email address to verify',
          'UserPortal:Email': 'Email',
          'UserPortal:Cancel': 'Cancel',
          'UserPortal:Submit': 'Submit',
          'UserPortal:change': 'Change',
          'UserPortal:Apply': 'Apply',
          'UserPortal:makePaymentSelected': 'Payment Selected',
          'UserPortal:DeleteConfirmation': 'Delete Confirmation',
          'UserPortal:ConfirmMissage': 'Are you sure you want to delete this request?',
          'UserPortal:Delete': 'Delete',
          'UserPortal:Validation_minLength': 'Must have at least one item',
          'UserPortal:promoCode': 'Promo Code',
          'UserPortal:payDialogMessage1':
            'are you sure you want to send a request? click Ok to continue',
          'UserPortal:payDialogMessage2':
            'Upon approval of your request, you can complete the payment electronically through Fatoorah e-payment services, or in cash by contacting the following number:',
          'try again': 'Try Again',
          'price per vehicle': 'Price per vehicle',
          'Delete Confirm': 'Delete Confirm',
          'UserPortal:SelectRoutes': 'Select Routes',
          'UserPortal:ExitingRouteAlert': 'Exiting Route Alert',
          custom: 'Custom',
          'you don`t have access to this part of the application':
            'You don`t have access to this part of the application',
          'Are you sure you want to delete this request':
            'Are you sure you want to delete this request',
        },
      },
    ],
  },
  {
    culture: 'ar',
    resources: [
      {
        resourceName: 'GoTrack',
        texts: {
          'UserPortal:change account': 'تغيير الحساب',
          'UserPortal:my requests': 'طلباتي',
          'UserPortal:virify email': 'تحقق البريد الإلكتروني',
          'UserPortal:EmailIsVerifyedpanner':
            'إذا كنت ترغب في تغيير بريدك الالكتروني المعتمد اضغط على تغيير ',
          'UserPortal:VerifyEmail': 'تحقق من البريد الإلكتروني',
          'UserPortal:EmailIsVerifyed': 'تم التحقق من البريد الإلكتروني',
          'UserPortal:EnterEmailToVerify': 'أدخل عنوان بريدك الإلكتروني للتحقق',
          'UserPortal:Email': 'البريد الإلكتروني',
          'UserPortal:Cancel': 'إلغاء',
          'UserPortal:Submit': 'إرسال',
          'UserPortal:change': 'تغيير',
          'UserPortal:Apply': 'تطبيق',
          'UserPortal:makePaymentSelected': 'دفع المحدد',
          'UserPortal:Validation_minLength': 'يجب أن يحتوي على عنصر واحد على الأقل',
          'UserPortal:DeleteConfirmation': 'تأكيد الحذف',
          'UserPortal:ConfirmMissage': 'هل أنت متأكد أنك تريد حذف هذا الطلب؟',
          'UserPortal:Delete': 'حذف',
          'price per vehicle': 'رسوم الباقة للمركبة',
          'UserPortal:promoCode': 'رمز الترويج',
          'UserPortal:payDialogMessage1':
            'هل أنت متأكد أنك تريد إرسال الطلب؟ انقر فوق موافق للمتابعة',
          'UserPortal:payDialogMessage2':
            'عند الموافقة على طلبك، يمكنك إتمام الدفع إلكترونيًا من خلال خدمات فاتورة للدفع الإلكتروني، أو نقدًا عبر التواصل على الرقم التالي:',
          'try again': 'حاول مرة أخرى',
          'Delete Confirm': 'تأكيد الحذف',
          'UserPortal:SelectRoutes': 'اختر مسار',
          custom: 'مخصص',
          'UserPortal:ExitingRouteAlert': 'انذار خروج عن المسار',
          'you don`t have access to this part of the application':
            'ليس لديك وصول إلى هذا الجزء من التطبيق ',
          'Are you sure you want to delete this request': 'هل أنت متأكد أنك تريد حذف هذا الطلب',
        },
      },
    ],
  },
];
