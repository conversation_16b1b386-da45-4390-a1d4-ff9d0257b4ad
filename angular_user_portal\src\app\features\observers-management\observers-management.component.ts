import { Component } from '@angular/core';
import { DynamicTabsComponent } from '@shared/components/dynamic-tabs/dynamic-tabs.component';
import { IdentifiedObserversComponent } from './components/identified-observers/identified-observers.component';

@Component({
  selector: 'app-observers-management',
  standalone: true,
  imports: [DynamicTabsComponent],
  templateUrl: './observers-management.component.html',
})
export class ObserversManagementComponent {
  tabs: { label: string; component: any }[] = [
    {
      label: 'GoTrack:Observers',
      component: IdentifiedObserversComponent,
    },
  ];
}
