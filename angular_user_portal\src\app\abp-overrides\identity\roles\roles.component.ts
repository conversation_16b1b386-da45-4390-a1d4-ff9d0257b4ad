import { LocalizationService } from '@abp/ng.core';
import { IdentityRoleService } from '@abp/ng.identity/proxy';
import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { AlertService, LOADING, TtwrGridComponent } from '@ttwr-framework/ngx-main-visuals';
import { map, Subject } from 'rxjs';
import { gridOptionToAbpOptions } from '../../abp-overrides.utils';
import {
  PermissionsDialogComponent,
  PermissionsDialogData,
} from '../permissions-dialog/permissions-dialog.component';
import { RolesCreateDialogComponent } from './roles-create-dialog/roles-create-dialog.component';
import { RolesUpdateDialogComponent } from './roles-update-dialog/roles-update-dialog.component';
import { role } from './roles.model';

@Component({
  selector: 'app-roles',
  standalone: true,
  imports: [TtwrGridComponent],
  templateUrl: './roles.component.html',
  styleUrl: './roles.component.scss',
})
export class RolesComponent {
  private identityRole = inject(IdentityRoleService);
  private localization = inject(LocalizationService);
  private dialog = inject(MatDialog);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private refreshSubject = new Subject<void>();

  protected config = role.grid({
    title: this.localization.instant('AbpIdentity::Roles'),
    refreshSubject: this.refreshSubject,
    defaultActions: {
      sort: false,
      filters: false,
      pdf: false,
      print: false,
      excel: false,
      toggleAll: false,
    },
    dataFunc: (...args) =>
      this.identityRole.getList(gridOptionToAbpOptions(args)).pipe(map(res => res as any)),
    fields: {
      name: {
        displayCell: v => v,
      },
    },
    actions: [
      {
        label: this.localization.instant('AbpIdentity::NewRole'),
        delegateFunc: () => {
          const ref = this.dialog.open(RolesCreateDialogComponent, {
            width: '600px',
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) this.refreshSubject.next();
            });
        },
      },
    ],
    fieldActions: [
      {
        color: 'primary',
        label: 'Edit',
        showFunc: obj => obj.name !== 'admin',
        delegateFunc: obj => {
          const ref = this.dialog.open(RolesUpdateDialogComponent, {
            width: '500px',
            data: obj,
          });

          ref
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(res => {
              if (res) this.refreshSubject.next();
            });
        },
      },
      {
        color: 'accent',
        label: this.localization.instant('AbpIdentity::Permissions'),
        showFunc: obj => obj.name !== 'admin',
        delegateFunc: role =>
          this.dialog.open<PermissionsDialogComponent, PermissionsDialogData>(
            PermissionsDialogComponent,
            {
              maxWidth: '800px',
              width: '100%',
              data: {
                providerName: 'R',
                providerKey: role.name,
                displayName: role.name,
              },
            }
          ),
      },
      {
        color: 'warn',
        label: 'Delete',
        showFunc: obj => obj.name !== 'admin',
        confirmation: {
          title: this.localization.instant('AbpUi::AreYouSure'),
          message: this.localization.instant('AbpIdentity::RoleDeletionConfirmationMessage', ''),
        },
        delegateFunc: obj => {
          this.loading.set(true);
          this.identityRole.delete(obj.id).subscribe({
            next: () => {
              this.alert.success(this.localization.instant('AbpUi::SuccessfullyDeleted'));
              this.refreshSubject.next();
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
        },
      },
    ],
  });
}
