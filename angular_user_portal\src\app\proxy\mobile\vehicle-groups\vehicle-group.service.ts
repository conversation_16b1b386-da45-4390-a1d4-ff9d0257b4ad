import type { VehicleGroupCreateDto, VehicleGroupDetailsDto, VehicleGroupDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto, PagedResultRequestDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class VehicleGroupService {
  apiName = 'Default';
  

  create = (createCreateDto: VehicleGroupCreateDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'POST',
      responseType: 'text',
      url: '/api/app/vehicleGroup',
      body: createCreateDto,
    },
    { apiName: this.apiName,...config });
  

  get = (id: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VehicleGroupDetailsDto>({
      method: 'GET',
      url: `/api/app/vehicleGroup/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<VehicleGroupDto>>({
      method: 'GET',
      url: '/api/app/vehicleGroup',
      params: { skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
