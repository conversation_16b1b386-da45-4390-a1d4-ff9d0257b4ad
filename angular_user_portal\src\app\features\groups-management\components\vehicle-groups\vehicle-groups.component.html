<div class="flex justify-between items-center mt-4">
  <h3 class="text-lg font-semibold">{{ 'UserPortal:DefinedGroups' | i18n }}</h3>
  <button mat-button color="warn" [routerLink]="['/main/groups-management/add-group']">
    + {{ 'UserPortal:AddGroup' | i18n }}
  </button>
</div>

<mat-accordion class="mt-4">
  @for(group of groups$(); track group; let i = $index) {
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title
        class="flex !flex-basis-[unset]"
        (click)="$event.stopPropagation(); $event.preventDefault()"
      >
        <button mat-icon-button [matMenuTriggerFor]="menu" class="ml-auto">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="openAssignedVehicles(group)"
          >
            <mat-icon>directions_car</mat-icon>
            <span>{{ 'UserPortal:ViewAssignedVehicles' | i18n }}</span>
          </button>

          <button
            class="flex items-center gap-2 text-gray-700 cursor-pointer transition hover:text-indigo-600 justify-between hover:bg-slate-50"
            mat-menu-item
            (click)="openVehiclesCreationDialog(group)"
          >
            <mat-icon>add_circle</mat-icon>
            <span>{{ 'UserPortal:AddVehicle' | i18n }}</span>
          </button>
        </mat-menu>
        <div class="flex-grow">
          <span class="inline-block mx-1 w-2 h-2 rounded-full bg-green-500"></span>
          {{ group.name }}
        </div>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="flex flex-col p-3 gap-3">
      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray">{{ 'UserPortal:CreationTime' | i18n }}:</span>
        <span class="text-main_gray">{{ group.creationTime | date : 'dd/MM/yyyy HH:mm a' }}</span>
      </div>

      @if (group.lastModificationTime) {
      <div
        class="flex justify-between items-center py-1.5 border-b border-black/10 last:border-b-0"
      >
        <span class="font-semibold text-main_gray"
          >{{ 'UserPortal:LastModificationTime' | i18n }}:</span
        >
        <span class="text-main_gray">{{
          group.lastModificationTime | date : 'dd/MM/yyyy HH:mm a'
        }}</span>
      </div>
      }
    </div>
  </mat-expansion-panel>
  }
</mat-accordion>
