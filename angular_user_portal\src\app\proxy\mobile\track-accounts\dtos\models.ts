import type { CreationAuditedEntityDto, EntityDto } from '@abp/ng.core';
import type { UserTrackAccountAssociationDto } from '../../user-track-account-associations/dtos/models';

export interface CurrentTrackAccountSubscriptionPlanDto {
  codeName?: string;
  value: number;
  localizedName?: string;
  minimumObserversCount: number;
}

export interface TrackAccountDetailsDto extends CreationAuditedEntityDto<string> {
  accountType?: string;
  name?: string;
  currentTrackAccountSubscriptionPlan: CurrentTrackAccountSubscriptionPlanDto;
  additionalUsers: number;
}

export interface TrackAccountDto extends EntityDto<string> {
  accountType?: string;
  name?: string;
  userTrackAccountAssociation: UserTrackAccountAssociationDto;
}
