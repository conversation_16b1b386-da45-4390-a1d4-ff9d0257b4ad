
export interface LiveLocationDto {
  imei?: string;
  vehicleId?: string;
  warpGtses: WarpGTSDto[];
}

export interface LiveLocationInputDto {
  vehicleIds: string[];
  onlySpeed: boolean;
}

export interface WarpGTSDto {
  className?: string;
  lastActivity?: string;
  values: WarpGTSValueDto[];
}

export interface WarpGTSValueDto {
  dateTime?: string;
  latitude?: string;
  longitude?: string;
  altitude?: string;
  value?: string;
}
