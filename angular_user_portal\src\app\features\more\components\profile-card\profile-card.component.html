<div class="relative z-10 min-h-screen flex flex-col items-center p-6">
  <div class="mt-16 mx-auto w-full sm:w-4/5 md:w-3/5 max-w-lg">
    <div class="bg-white rounded-2xl shadow-lg p-6 text-center w-full mx-auto">
      <app-profile-image />

      <ul class="mt-4 space-y-2 text-right text-gray-700 text-sm">
        <li
          class="flex gap-3 items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded"
          [routerLink]="['/more/about']"
        >
          <img class="w-6 pl-2" [src]="appInfoIcon" />
          <span>{{ 'UserPortal.AboutApp' | i18n }}</span>
        </li>
        <li
          class="flex gap-3 items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded"
        >
          <img class="w-6 pl-2" [src]="phoneIcon" />
          <span>{{ 'UserPortal.ContactSupport' | i18n }}</span>
        </li>
        <li class="flex items-center justify-between hover:bg-gray-100 p-2 rounded">
          <div class="p-0 flex gap-3 items-center justify-start">
            <img class="w-6 pl-2" [src]="languageIcon" />
            <span>{{ 'UserPortal.AppLanguage' | i18n }}</span>
          </div>
          <div class="flex space-x-1 gap-1">
            <span
              class="cursor-pointer text-main_perple"
              [ngClass]="{
                'font-bold': (session.getLanguage$() | async) === 'ar'
              }"
              (click)="switchLanguage('ar')"
              >{{ 'UserPortal.Arabic' | i18n }}</span
            >
            <span
              class="cursor-pointer text-main_perple"
              [ngClass]="{
            'font-bold': (session.getLanguage$()|async) === 'en',
          }"
              (click)="switchLanguage('en')"
              >{{ 'UserPortal.English' | i18n }}</span
            >
          </div>
        </li>
        <li
          class="flex items-center justify-start cursor-pointer hover:bg-gray-100 p-2 rounded text-gray-700 logout"
          (click)="logout()"
        >
          <img class="w-6 pl-2" [src]="logoutIcon" />
          <span>{{ 'UserPortal.Logout' | i18n }}</span>
        </li>
      </ul>
    </div>
  </div>
</div>
