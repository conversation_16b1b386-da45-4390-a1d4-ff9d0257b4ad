<div class="relative overflow-clip h-full w-full">
  <div class="geoLocation row h-full">
    <app-map [nodes]="node$()" [options]="options$()" [line]="line$()" />
  </div>
  <div class="absolute top-2 right-4 gray z-[999]">
    <button class="!bg-white" mat-mini-fab [routerLink]="['/main']">
      <mat-icon>chevron_right</mat-icon>
    </button>
  </div>

  <div class="absolute top-16 right-4 gray max-w-80 z-[999]">
    <div class="bg-white rounded-lg p-4 pb-0 flex justify-center gap-2">
      <mat-form-field class="w-full">
        <mat-select matInput [placeholder]="'vehicle' | i18n" [formControl]="selectedTime">
          @for (item of times|keyvalue; track $index) {
          <mat-option [value]="item.value">{{ item.key | i18n }}</mat-option>
          }
          <mat-option (click)="openDateDialog()">{{ 'custom' | i18n }}</mat-option>
        </mat-select>
      </mat-form-field>
      <button class="!bg-white" mat-mini-fab (click)="selectedTime.setValue(selectedTime.value)">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>
  <div class="absolute bottom-2 right-4 gray max-w-80 z-[999]">
    <div class="bg-white rounded-lg p-2 text-sm">
      <div class="grid grid-cols-3 gap-4 text-center text-main_dark_blue">
        @for (item of info; track $index) {

        <div>
          <mat-icon>{{ item.icon }}</mat-icon>
          <div class="text-xs">
            {{ item.name | i18n }}
          </div>
          <div class="">
            {{ item.key() }}
          </div>
        </div>
        }
      </div>
    </div>
  </div>
</div>
