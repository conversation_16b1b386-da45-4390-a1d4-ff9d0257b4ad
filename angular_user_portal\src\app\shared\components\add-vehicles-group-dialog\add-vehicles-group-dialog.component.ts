import { Component, inject, model } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';

import { toSignal } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormField, MatLabel, MatOption, MatSelect } from '@angular/material/select';
import { AlertDefinitionService } from '@proxy/mobile/alert-definitions';
import { VehicleGroupService } from '@proxy/mobile/vehicle-groups';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';
import { map } from 'rxjs';

@Component({
  selector: 'app-add-vehicles-group-dialog',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    LanguagePipe,
    MatSelect,
    MatO<PERSON>,
    MatFormField,
    Mat<PERSON><PERSON><PERSON>,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: `./add-vehicles-group-dialog.component.html`,
})
export class AddVehiclesGroupDialogComponent {
  dialogRef = inject(MatDialogRef<AddVehiclesGroupDialogComponent>);
  data = inject<string>(MAT_DIALOG_DATA);
  private vehicleGroupService = inject(VehicleGroupService);
  private alertDefinitionService = inject(AlertDefinitionService);

  vehicleId = model<string | null>();
  availableGroup$ = toSignal(
    this.vehicleGroupService.getList({ skipCount: 0, maxResultCount: 50 }).pipe(
      map(response => {
        return response.items;
      })
    )
  );

  closeDialog() {
    this.dialogRef.close();
  }
  save() {
    this.dialogRef.close(this.vehicleId());
  }
}
export const openAddVehiclesGroupDialog = (dialog: MatDialog, data: any) => {
  return dialog
    .open(AddVehiclesGroupDialogComponent, {
      data: data,
    })
    .afterClosed();
};
