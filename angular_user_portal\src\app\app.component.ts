import { AuthService } from '@abp/ng.core';
import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { logout } from '@shared/functions/logout';
import { FcmService } from '@shared/services/fcm.service';
import { TtwrLoaderComponent } from '@ttwr-framework/ngx-main-visuals';
import { debounceTime, fromEvent, merge, of, switchMap } from 'rxjs';

@Component({
  standalone: true,
  selector: 'app-root',
  imports: [TtwrLoaderComponent, RouterOutlet],
  template: `
    <ttwr-loader />
    <router-outlet></router-outlet>
  `,
})
export class AppComponent {
  fcm = inject(FcmService);
  auth = inject(AuthService);
  private inactivityTime = 10 * 60 * 1000; // 10 minutes

  activityEvents = merge(
    fromEvent(document, 'mousemove'),
    fromEvent(document, 'keydown'),
    fromEvent(document, 'click')
  );

  ngOnInit(): void {
    this.activityEvents
      .pipe(
        debounceTime(this.inactivityTime),
        switchMap(() => {
          logout(this.fcm);
          return of(true);
        })
      )
      .subscribe();
  }
}
