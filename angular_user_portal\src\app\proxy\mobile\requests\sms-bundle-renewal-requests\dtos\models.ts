import type { RequestDto } from '../../models';
import type { SmsBundleRenewalStage } from '../../../../requests/sms-bundle-renewal-stage.enum';

export interface CreateSmsBundleRenewalRequestDto {
  smsBundleId?: string;
}

export interface SmsBundleRenewalRequestDto extends RequestDto {
  ownerId?: string;
  ownerFirstName?: string;
  ownerLastName?: string;
  ownerEmail?: string;
  ownerPhoneNumber?: string;
  trackAccountSubscriptionId?: string;
  stage: SmsBundleRenewalStage;
  smsBundleId?: string;
  paymentUrl?: string;
}
