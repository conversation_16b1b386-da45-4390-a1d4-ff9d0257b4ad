{"version": 3, "sources": ["../../../../../../node_modules/@mapbox/polyline/src/polyline.js"], "sourcesContent": ["'use strict';\n\n/**\n * Based off of [the offical Google document](https://developers.google.com/maps/documentation/utilities/polylinealgorithm)\n *\n * Some parts from [this implementation](http://facstaff.unca.edu/mcmcclur/GoogleMaps/EncodePolyline/PolylineEncoder.js)\n * by [<PERSON>](http://facstaff.unca.edu/mcmcclur/)\n *\n * @module polyline\n */\nvar polyline = {};\nfunction py2_round(value) {\n  // Google's polyline algorithm uses the same rounding strategy as Python 2, which is different from JS for negative values\n  return Math.floor(Math.abs(value) + 0.5) * (value >= 0 ? 1 : -1);\n}\nfunction encode(current, previous, factor) {\n  current = py2_round(current * factor);\n  previous = py2_round(previous * factor);\n  var coordinate = (current - previous) * 2;\n  if (coordinate < 0) {\n    coordinate = -coordinate - 1;\n  }\n  var output = '';\n  while (coordinate >= 0x20) {\n    output += String.fromCharCode((0x20 | coordinate & 0x1f) + 63);\n    coordinate /= 32;\n  }\n  output += String.fromCharCode((coordinate | 0) + 63);\n  return output;\n}\n\n/**\n * Decodes to a [latitude, longitude] coordinates array.\n *\n * This is adapted from the implementation in Project-OSRM.\n *\n * @param {String} str\n * @param {Number} precision\n * @returns {Array}\n *\n * @see https://github.com/Project-OSRM/osrm-frontend/blob/master/WebContent/routing/OSRM.RoutingGeometry.js\n */\npolyline.decode = function (str, precision) {\n  var index = 0,\n    lat = 0,\n    lng = 0,\n    coordinates = [],\n    shift = 0,\n    result = 0,\n    byte = null,\n    latitude_change,\n    longitude_change,\n    factor = Math.pow(10, Number.isInteger(precision) ? precision : 5);\n\n  // Coordinates have variable length when encoded, so just keep\n  // track of whether we've hit the end of the string. In each\n  // loop iteration, a single coordinate is decoded.\n  while (index < str.length) {\n    // Reset shift, result, and byte\n    byte = null;\n    shift = 1;\n    result = 0;\n    do {\n      byte = str.charCodeAt(index++) - 63;\n      result += (byte & 0x1f) * shift;\n      shift *= 32;\n    } while (byte >= 0x20);\n    latitude_change = result & 1 ? (-result - 1) / 2 : result / 2;\n    shift = 1;\n    result = 0;\n    do {\n      byte = str.charCodeAt(index++) - 63;\n      result += (byte & 0x1f) * shift;\n      shift *= 32;\n    } while (byte >= 0x20);\n    longitude_change = result & 1 ? (-result - 1) / 2 : result / 2;\n    lat += latitude_change;\n    lng += longitude_change;\n    coordinates.push([lat / factor, lng / factor]);\n  }\n  return coordinates;\n};\n\n/**\n * Encodes the given [latitude, longitude] coordinates array.\n *\n * @param {Array.<Array.<Number>>} coordinates\n * @param {Number} precision\n * @returns {String}\n */\npolyline.encode = function (coordinates, precision) {\n  if (!coordinates.length) {\n    return '';\n  }\n  var factor = Math.pow(10, Number.isInteger(precision) ? precision : 5),\n    output = encode(coordinates[0][0], 0, factor) + encode(coordinates[0][1], 0, factor);\n  for (var i = 1; i < coordinates.length; i++) {\n    var a = coordinates[i],\n      b = coordinates[i - 1];\n    output += encode(a[0], b[0], factor);\n    output += encode(a[1], b[1], factor);\n  }\n  return output;\n};\nfunction flipped(coords) {\n  var flipped = [];\n  for (var i = 0; i < coords.length; i++) {\n    var coord = coords[i].slice();\n    flipped.push([coord[1], coord[0]]);\n  }\n  return flipped;\n}\n\n/**\n * Encodes a GeoJSON LineString feature/geometry.\n *\n * @param {Object} geojson\n * @param {Number} precision\n * @returns {String}\n */\npolyline.fromGeoJSON = function (geojson, precision) {\n  if (geojson && geojson.type === 'Feature') {\n    geojson = geojson.geometry;\n  }\n  if (!geojson || geojson.type !== 'LineString') {\n    throw new Error('Input must be a GeoJSON LineString');\n  }\n  return polyline.encode(flipped(geojson.coordinates), precision);\n};\n\n/**\n * Decodes to a GeoJSON LineString geometry.\n *\n * @param {String} str\n * @param {Number} precision\n * @returns {Object}\n */\npolyline.toGeoJSON = function (str, precision) {\n  var coords = polyline.decode(str, precision);\n  return {\n    type: 'LineString',\n    coordinates: flipped(coords)\n  };\n};\nif (typeof module === 'object' && module.exports) {\n  module.exports = polyline;\n}"], "mappings": ";;;;;AAAA;AAAA;AAUA,QAAI,WAAW,CAAC;AAChB,aAAS,UAAU,OAAO;AAExB,aAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI;AAAA,IAC/D;AACA,aAAS,OAAO,SAAS,UAAU,QAAQ;AACzC,gBAAU,UAAU,UAAU,MAAM;AACpC,iBAAW,UAAU,WAAW,MAAM;AACtC,UAAI,cAAc,UAAU,YAAY;AACxC,UAAI,aAAa,GAAG;AAClB,qBAAa,CAAC,aAAa;AAAA,MAC7B;AACA,UAAI,SAAS;AACb,aAAO,cAAc,IAAM;AACzB,kBAAU,OAAO,cAAc,KAAO,aAAa,MAAQ,EAAE;AAC7D,sBAAc;AAAA,MAChB;AACA,gBAAU,OAAO,cAAc,aAAa,KAAK,EAAE;AACnD,aAAO;AAAA,IACT;AAaA,aAAS,SAAS,SAAU,KAAK,WAAW;AAC1C,UAAI,QAAQ,GACV,MAAM,GACN,MAAM,GACN,cAAc,CAAC,GACf,QAAQ,GACR,SAAS,GACT,OAAO,MACP,iBACA,kBACA,SAAS,KAAK,IAAI,IAAI,OAAO,UAAU,SAAS,IAAI,YAAY,CAAC;AAKnE,aAAO,QAAQ,IAAI,QAAQ;AAEzB,eAAO;AACP,gBAAQ;AACR,iBAAS;AACT,WAAG;AACD,iBAAO,IAAI,WAAW,OAAO,IAAI;AACjC,qBAAW,OAAO,MAAQ;AAC1B,mBAAS;AAAA,QACX,SAAS,QAAQ;AACjB,0BAAkB,SAAS,KAAK,CAAC,SAAS,KAAK,IAAI,SAAS;AAC5D,gBAAQ;AACR,iBAAS;AACT,WAAG;AACD,iBAAO,IAAI,WAAW,OAAO,IAAI;AACjC,qBAAW,OAAO,MAAQ;AAC1B,mBAAS;AAAA,QACX,SAAS,QAAQ;AACjB,2BAAmB,SAAS,KAAK,CAAC,SAAS,KAAK,IAAI,SAAS;AAC7D,eAAO;AACP,eAAO;AACP,oBAAY,KAAK,CAAC,MAAM,QAAQ,MAAM,MAAM,CAAC;AAAA,MAC/C;AACA,aAAO;AAAA,IACT;AASA,aAAS,SAAS,SAAU,aAAa,WAAW;AAClD,UAAI,CAAC,YAAY,QAAQ;AACvB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,IAAI,IAAI,OAAO,UAAU,SAAS,IAAI,YAAY,CAAC,GACnE,SAAS,OAAO,YAAY,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM,IAAI,OAAO,YAAY,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM;AACrF,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAI,IAAI,YAAY,CAAC,GACnB,IAAI,YAAY,IAAI,CAAC;AACvB,kBAAU,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM;AACnC,kBAAU,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AACA,aAAS,QAAQ,QAAQ;AACvB,UAAIA,WAAU,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,QAAQ,OAAO,CAAC,EAAE,MAAM;AAC5B,QAAAA,SAAQ,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AAAA,MACnC;AACA,aAAOA;AAAA,IACT;AASA,aAAS,cAAc,SAAU,SAAS,WAAW;AACnD,UAAI,WAAW,QAAQ,SAAS,WAAW;AACzC,kBAAU,QAAQ;AAAA,MACpB;AACA,UAAI,CAAC,WAAW,QAAQ,SAAS,cAAc;AAC7C,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACtD;AACA,aAAO,SAAS,OAAO,QAAQ,QAAQ,WAAW,GAAG,SAAS;AAAA,IAChE;AASA,aAAS,YAAY,SAAU,KAAK,WAAW;AAC7C,UAAI,SAAS,SAAS,OAAO,KAAK,SAAS;AAC3C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,aAAa,QAAQ,MAAM;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,OAAO,WAAW,YAAY,OAAO,SAAS;AAChD,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["flipped"]}