import { <PERSON>son<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, computed, input, model, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatChipListbox, MatChipsModule } from '@angular/material/chips';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatOption, MatSelect, MatSelectTrigger } from '@angular/material/select';
import { GeoZoneDto } from '@proxy/mobile/geo-zones';
import { LanguagePipe } from '@ttwr-framework/ngx-main-visuals';

@Component({
  selector: 'alert-zones-alert-type',
  standalone: true,
  templateUrl: `./zones-alert-type.component.html`,
  imports: [
    MatLabel,
    MatFormField,
    MatSelect,
    FormsModule,
    ReactiveFormsModule,
    LanguagePipe,
    MatSelectTrigger,
    MatO<PERSON>,
    MatChipsModule,
    MatIcon,
  ],
})
export class ZonesAlertTypeComponent {
  alertForm = input.required<FormGroup>();
  geoZones = input.required<Map<string, GeoZoneDto>>();

  removeGeoZone(id) {
    const control = this.alertForm().get('geoZoneIds');
    const newVal = [...control.value].filter(v => v != id);
    control.setValue(newVal);
  }
}
