<mat-toolbar class="mat-elevation-z2 !bg-main_perple !text-white rounded-b-3xl">
  <button [matMenuTriggerFor]="menuLink" mat-icon-button>
    <mat-icon>menu</mat-icon>
  </button>

  <mat-menu #menuLink>
    <div class="flex gap-2 mx-2">
      @for (item of routess; track $index) {

      <a
        class="!w-fit flex-col !p-2 rounded-md !text-sm !font-medium !text-main_gray"
        mat-menu-item
        (click)="navigate(item)"
        routerLinkActive="!text-main_blue"
      >
        <mat-icon class="!mr-0 !ml-0 !text-main_gray">{{ item.icon }}</mat-icon
        >{{ item.name | i18n }}</a
      >
      }
    </div>
  </mat-menu>
  <mat-menu #menu>
    <button mat-menu-item (click)="changeLang('ar')">العربية</button>
    <button mat-menu-item (click)="changeLang('en')">English</button>
  </mat-menu>
  <button mat-icon-button [matMenuTriggerFor]="menu">
    <mat-icon>language</mat-icon>
  </button>
  <button mat-icon-button [routerLink]="'/main/account-profile'">
    <mat-icon>person</mat-icon>
  </button>
  <div class="flex-grow">
    <app-breadcrumb />
  </div>
  <div class="w-1/12">
    <img [src]="'assets/images/logo/Logo_white.svg'" alt="" />
  </div>
</mat-toolbar>
<mat-sidenav-container>
  <mat-sidenav-content>
    <main>
      <router-outlet />
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>
