import { GridFilter, GridPagination, GridSort } from '@ttwr-framework/ngx-main-visuals';

export const gridOptionToAbpOptions = ([pagination, sort, filters]: [
  GridPagination,
  GridSort[],
  GridFilter[]
]) => ({
  filter: filters[0]?.value,
  sorting: sort[0] ? `${sort[0]?.attribute} ${sort[0]?.direction}` : undefined,
  skipCount: pagination.pageIndex * pagination.pageSize,
  maxResultCount: pagination.pageSize,
});
