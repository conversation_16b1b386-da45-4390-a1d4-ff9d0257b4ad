import { LocalizationModule, LocalizationService } from '@abp/ng.core';
import { IdentityRoleService } from '@abp/ng.identity/proxy';
import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import {
  AlertService,
  LOADING,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { role } from '../roles.model';

@Component({
  selector: 'app-roles-create-dialog',
  standalone: true,
  imports: [LocalizationModule, MatDialogContent, TtwrFormComponent, MatDialogTitle],
  templateUrl: './roles-create-dialog.component.html',
  styleUrl: './roles-create-dialog.component.scss',
})
export class RolesCreateDialogComponent {
  private identityRole = inject(IdentityRoleService);
  private localization = inject(LocalizationService);
  private ref = inject(MatDialogRef);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  protected config = role.form({
    submitAction: {
      onSubmit: value => {
        this.loading.set(true);
        this.identityRole
          .create(value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.loading.set(false);
              this.ref.close(true);
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    fields: {
      name: {
        validators: [requiredValidator],
      },
    },
    actions: [
      {
        label: 'Cancel',
        delegateFunc: () => {
          this.ref.close();
        },
      },
    ],
  });
}
