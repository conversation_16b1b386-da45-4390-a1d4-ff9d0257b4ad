{"version": 3, "sources": ["../../../../../../node_modules/@bluehalo/ngx-leaflet/fesm2022/bluehalo-ngx-leaflet.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { latLng, map, control, tileLayer } from 'leaflet';\nclass LeafletUtil {\n  static mapToArray(map) {\n    const toReturn = [];\n    for (const k in map) {\n      if (map.hasOwnProperty(k)) {\n        toReturn.push(map[k]);\n      }\n    }\n    return toReturn;\n  }\n  static handleEvent(zone, eventEmitter, event) {\n    // Don't want to emit if there are no observers\n    if (0 < eventEmitter.observers.length) {\n      zone.run(() => {\n        eventEmitter.emit(event);\n      });\n    }\n  }\n}\nclass LeafletDirective {\n  constructor(element, zone) {\n    this.element = element;\n    this.zone = zone;\n    this.DEFAULT_ZOOM = 1;\n    this.DEFAULT_CENTER = latLng(38.907192, -77.036871);\n    this.DEFAULT_FPZ_OPTIONS = {};\n    this.fitBoundsOptions = this.DEFAULT_FPZ_OPTIONS;\n    this.panOptions = this.DEFAULT_FPZ_OPTIONS;\n    this.zoomOptions = this.DEFAULT_FPZ_OPTIONS;\n    this.zoomPanOptions = this.DEFAULT_FPZ_OPTIONS;\n    // Default configuration\n    this.options = {};\n    // Configure callback function for the map\n    this.mapReady = new EventEmitter();\n    this.zoomChange = new EventEmitter();\n    this.centerChange = new EventEmitter();\n    // Mouse Map Events\n    this.onClick = new EventEmitter();\n    this.onDoubleClick = new EventEmitter();\n    this.onMouseDown = new EventEmitter();\n    this.onMouseUp = new EventEmitter();\n    this.onMouseMove = new EventEmitter();\n    this.onMouseOver = new EventEmitter();\n    this.onMouseOut = new EventEmitter();\n    // Map Move Events\n    this.onMapMove = new EventEmitter();\n    this.onMapMoveStart = new EventEmitter();\n    this.onMapMoveEnd = new EventEmitter();\n    // Map Zoom Events\n    this.onMapZoom = new EventEmitter();\n    this.onMapZoomStart = new EventEmitter();\n    this.onMapZoomEnd = new EventEmitter();\n    // Nothing here\n  }\n  ngOnInit() {\n    // Create the map outside of angular so the various map events don't trigger change detection\n    this.zone.runOutsideAngular(() => {\n      // Create the map with some reasonable defaults\n      this.map = map(this.element.nativeElement, this.options);\n      this.addMapEventListeners();\n    });\n    // Only setView if there is a center/zoom\n    if (null != this.center && null != this.zoom) {\n      this.setView(this.center, this.zoom);\n    }\n    // Set up all the initial settings\n    if (null != this.fitBounds) {\n      this.setFitBounds(this.fitBounds);\n    }\n    if (null != this.maxBounds) {\n      this.setMaxBounds(this.maxBounds);\n    }\n    if (null != this.minZoom) {\n      this.setMinZoom(this.minZoom);\n    }\n    if (null != this.maxZoom) {\n      this.setMaxZoom(this.maxZoom);\n    }\n    this.doResize();\n    // Fire map ready event\n    this.mapReady.emit(this.map);\n  }\n  ngOnChanges(changes) {\n    /*\n     * The following code is to address an issue with our (basic) implementation of\n     * zooming and panning. From our testing, it seems that a pan operation followed\n     * by a zoom operation in the same thread will interfere with eachother. The zoom\n     * operation interrupts/cancels the pan, resulting in a final center point that is\n     * inaccurate. The solution seems to be to either separate them with a timeout or\n      * to collapse them into a setView call.\n     */\n    // Zooming and Panning\n    if (changes['zoom'] && changes['center'] && null != this.zoom && null != this.center) {\n      this.setView(changes['center'].currentValue, changes['zoom'].currentValue);\n    }\n    // Set the zoom level\n    else if (changes['zoom']) {\n      this.setZoom(changes['zoom'].currentValue);\n    }\n    // Set the map center\n    else if (changes['center']) {\n      this.setCenter(changes['center'].currentValue);\n    }\n    // Other options\n    if (changes['fitBounds']) {\n      this.setFitBounds(changes['fitBounds'].currentValue);\n    }\n    if (changes['maxBounds']) {\n      this.setMaxBounds(changes['maxBounds'].currentValue);\n    }\n    if (changes['minZoom']) {\n      this.setMinZoom(changes['minZoom'].currentValue);\n    }\n    if (changes['maxZoom']) {\n      this.setMaxZoom(changes['maxZoom'].currentValue);\n    }\n  }\n  ngOnDestroy() {\n    // If this directive is destroyed, the map is too\n    if (null != this.map) {\n      this.map.remove();\n    }\n  }\n  getMap() {\n    return this.map;\n  }\n  onResize() {\n    this.delayResize();\n  }\n  addMapEventListeners() {\n    const registerEventHandler = (eventName, handler) => {\n      this.map.on(eventName, handler);\n    };\n    // Add all the pass-through mouse event handlers\n    registerEventHandler('click', e => LeafletUtil.handleEvent(this.zone, this.onClick, e));\n    registerEventHandler('dblclick', e => LeafletUtil.handleEvent(this.zone, this.onDoubleClick, e));\n    registerEventHandler('mousedown', e => LeafletUtil.handleEvent(this.zone, this.onMouseDown, e));\n    registerEventHandler('mouseup', e => LeafletUtil.handleEvent(this.zone, this.onMouseUp, e));\n    registerEventHandler('mouseover', e => LeafletUtil.handleEvent(this.zone, this.onMouseOver, e));\n    registerEventHandler('mouseout', e => LeafletUtil.handleEvent(this.zone, this.onMouseOut, e));\n    registerEventHandler('mousemove', e => LeafletUtil.handleEvent(this.zone, this.onMouseMove, e));\n    registerEventHandler('zoomstart', e => LeafletUtil.handleEvent(this.zone, this.onMapZoomStart, e));\n    registerEventHandler('zoom', e => LeafletUtil.handleEvent(this.zone, this.onMapZoom, e));\n    registerEventHandler('zoomend', e => LeafletUtil.handleEvent(this.zone, this.onMapZoomEnd, e));\n    registerEventHandler('movestart', e => LeafletUtil.handleEvent(this.zone, this.onMapMoveStart, e));\n    registerEventHandler('move', e => LeafletUtil.handleEvent(this.zone, this.onMapMove, e));\n    registerEventHandler('moveend', e => LeafletUtil.handleEvent(this.zone, this.onMapMoveEnd, e));\n    // Update any things for which we provide output bindings\n    const outputUpdateHandler = () => {\n      const zoom = this.map.getZoom();\n      if (zoom !== this.zoom) {\n        this.zoom = zoom;\n        LeafletUtil.handleEvent(this.zone, this.zoomChange, zoom);\n      }\n      const center = this.map.getCenter();\n      if (null != center || null != this.center) {\n        if ((null == center || null == this.center) && center !== this.center || center.lat !== this.center.lat || center.lng !== this.center.lng) {\n          this.center = center;\n          LeafletUtil.handleEvent(this.zone, this.centerChange, center);\n        }\n      }\n    };\n    registerEventHandler('moveend', outputUpdateHandler);\n    registerEventHandler('zoomend', outputUpdateHandler);\n  }\n  /**\n   * Resize the map to fit it's parent container\n   */\n  doResize() {\n    // Run this outside of angular so the map events stay outside of angular\n    this.zone.runOutsideAngular(() => {\n      // Invalidate the map size to trigger it to update itself\n      if (null != this.map) {\n        this.map.invalidateSize({});\n      }\n    });\n  }\n  /**\n   * Manage a delayed resize of the component\n   */\n  delayResize() {\n    if (null != this.resizeTimer) {\n      clearTimeout(this.resizeTimer);\n    }\n    this.resizeTimer = setTimeout(this.doResize.bind(this), 200);\n  }\n  /**\n   * Set the view (center/zoom) all at once\n   * @param center The new center\n   * @param zoom The new zoom level\n   */\n  setView(center, zoom) {\n    if (null != this.map && null != center && null != zoom) {\n      this.map.setView(center, zoom, this.zoomPanOptions);\n    }\n  }\n  /**\n   * Set the map zoom level\n   * @param zoom the new zoom level for the map\n   */\n  setZoom(zoom) {\n    if (null != this.map && null != zoom) {\n      this.map.setZoom(zoom, this.zoomOptions);\n    }\n  }\n  /**\n   * Set the center of the map\n   * @param center the center point\n   */\n  setCenter(center) {\n    if (null != this.map && null != center) {\n      this.map.panTo(center, this.panOptions);\n    }\n  }\n  /**\n   * Fit the map to the bounds\n   * @param latLngBounds the boundary to set\n   */\n  setFitBounds(latLngBounds) {\n    if (null != this.map && null != latLngBounds) {\n      this.map.fitBounds(latLngBounds, this.fitBoundsOptions);\n    }\n  }\n  /**\n   * Set the map's max bounds\n   * @param latLngBounds the boundary to set\n   */\n  setMaxBounds(latLngBounds) {\n    if (null != this.map && null != latLngBounds) {\n      this.map.setMaxBounds(latLngBounds);\n    }\n  }\n  /**\n   * Set the map's min zoom\n   * @param number the new min zoom\n   */\n  setMinZoom(zoom) {\n    if (null != this.map && null != zoom) {\n      this.map.setMinZoom(zoom);\n    }\n  }\n  /**\n   * Set the map's min zoom\n   * @param number the new min zoom\n   */\n  setMaxZoom(zoom) {\n    if (null != this.map && null != zoom) {\n      this.map.setMaxZoom(zoom);\n    }\n  }\n  static {\n    this.ɵfac = function LeafletDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LeafletDirective,\n      selectors: [[\"\", \"leaflet\", \"\"]],\n      hostBindings: function LeafletDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function LeafletDirective_resize_HostBindingHandler() {\n            return ctx.onResize();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        fitBoundsOptions: [0, \"leafletFitBoundsOptions\", \"fitBoundsOptions\"],\n        panOptions: [0, \"leafletPanOptions\", \"panOptions\"],\n        zoomOptions: [0, \"leafletZoomOptions\", \"zoomOptions\"],\n        zoomPanOptions: [0, \"leafletZoomPanOptions\", \"zoomPanOptions\"],\n        options: [0, \"leafletOptions\", \"options\"],\n        zoom: [0, \"leafletZoom\", \"zoom\"],\n        center: [0, \"leafletCenter\", \"center\"],\n        fitBounds: [0, \"leafletFitBounds\", \"fitBounds\"],\n        maxBounds: [0, \"leafletMaxBounds\", \"maxBounds\"],\n        minZoom: [0, \"leafletMinZoom\", \"minZoom\"],\n        maxZoom: [0, \"leafletMaxZoom\", \"maxZoom\"]\n      },\n      outputs: {\n        mapReady: \"leafletMapReady\",\n        zoomChange: \"leafletZoomChange\",\n        centerChange: \"leafletCenterChange\",\n        onClick: \"leafletClick\",\n        onDoubleClick: \"leafletDoubleClick\",\n        onMouseDown: \"leafletMouseDown\",\n        onMouseUp: \"leafletMouseUp\",\n        onMouseMove: \"leafletMouseMove\",\n        onMouseOver: \"leafletMouseOver\",\n        onMouseOut: \"leafletMouseOut\",\n        onMapMove: \"leafletMapMove\",\n        onMapMoveStart: \"leafletMapMoveStart\",\n        onMapMoveEnd: \"leafletMapMoveEnd\",\n        onMapZoom: \"leafletMapZoom\",\n        onMapZoomStart: \"leafletMapZoomStart\",\n        onMapZoomEnd: \"leafletMapZoomEnd\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[leaflet]',\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }], {\n    fitBoundsOptions: [{\n      type: Input,\n      args: ['leafletFitBoundsOptions']\n    }],\n    panOptions: [{\n      type: Input,\n      args: ['leafletPanOptions']\n    }],\n    zoomOptions: [{\n      type: Input,\n      args: ['leafletZoomOptions']\n    }],\n    zoomPanOptions: [{\n      type: Input,\n      args: ['leafletZoomPanOptions']\n    }],\n    options: [{\n      type: Input,\n      args: ['leafletOptions']\n    }],\n    mapReady: [{\n      type: Output,\n      args: ['leafletMapReady']\n    }],\n    zoom: [{\n      type: Input,\n      args: ['leafletZoom']\n    }],\n    zoomChange: [{\n      type: Output,\n      args: ['leafletZoomChange']\n    }],\n    center: [{\n      type: Input,\n      args: ['leafletCenter']\n    }],\n    centerChange: [{\n      type: Output,\n      args: ['leafletCenterChange']\n    }],\n    fitBounds: [{\n      type: Input,\n      args: ['leafletFitBounds']\n    }],\n    maxBounds: [{\n      type: Input,\n      args: ['leafletMaxBounds']\n    }],\n    minZoom: [{\n      type: Input,\n      args: ['leafletMinZoom']\n    }],\n    maxZoom: [{\n      type: Input,\n      args: ['leafletMaxZoom']\n    }],\n    onClick: [{\n      type: Output,\n      args: ['leafletClick']\n    }],\n    onDoubleClick: [{\n      type: Output,\n      args: ['leafletDoubleClick']\n    }],\n    onMouseDown: [{\n      type: Output,\n      args: ['leafletMouseDown']\n    }],\n    onMouseUp: [{\n      type: Output,\n      args: ['leafletMouseUp']\n    }],\n    onMouseMove: [{\n      type: Output,\n      args: ['leafletMouseMove']\n    }],\n    onMouseOver: [{\n      type: Output,\n      args: ['leafletMouseOver']\n    }],\n    onMouseOut: [{\n      type: Output,\n      args: ['leafletMouseOut']\n    }],\n    onMapMove: [{\n      type: Output,\n      args: ['leafletMapMove']\n    }],\n    onMapMoveStart: [{\n      type: Output,\n      args: ['leafletMapMoveStart']\n    }],\n    onMapMoveEnd: [{\n      type: Output,\n      args: ['leafletMapMoveEnd']\n    }],\n    onMapZoom: [{\n      type: Output,\n      args: ['leafletMapZoom']\n    }],\n    onMapZoomStart: [{\n      type: Output,\n      args: ['leafletMapZoomStart']\n    }],\n    onMapZoomEnd: [{\n      type: Output,\n      args: ['leafletMapZoomEnd']\n    }],\n    onResize: [{\n      type: HostListener,\n      args: ['window:resize', []]\n    }]\n  });\n})();\nclass LeafletDirectiveWrapper {\n  constructor(leafletDirective) {\n    this.leafletDirective = leafletDirective;\n  }\n  init() {\n    // Nothing for now\n  }\n  getMap() {\n    return this.leafletDirective.getMap();\n  }\n}\n\n/**\n * Layer directive\n *\n * This directive is used to directly control a single map layer. The purpose of this directive is to\n * be used as part of a child structural directive of the map element.\n *\n */\nclass LeafletLayerDirective {\n  constructor(leafletDirective, zone) {\n    this.zone = zone;\n    // Layer Events\n    this.onAdd = new EventEmitter();\n    this.onRemove = new EventEmitter();\n    this.leafletDirective = new LeafletDirectiveWrapper(leafletDirective);\n  }\n  ngOnInit() {\n    // Init the map\n    this.leafletDirective.init();\n  }\n  ngOnDestroy() {\n    if (null != this.layer) {\n      // Unregister the event handlers\n      this.removeLayerEventListeners(this.layer);\n      // Remove the layer from the map\n      this.layer.remove();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['layer']) {\n      // Update the layer\n      const p = changes['layer'].previousValue;\n      const n = changes['layer'].currentValue;\n      this.zone.runOutsideAngular(() => {\n        if (null != p) {\n          this.removeLayerEventListeners(p);\n          p.remove();\n        }\n        if (null != n) {\n          this.addLayerEventListeners(n);\n          this.leafletDirective.getMap().addLayer(n);\n        }\n      });\n    }\n  }\n  addLayerEventListeners(l) {\n    this.onAddLayerHandler = e => LeafletUtil.handleEvent(this.zone, this.onAdd, e);\n    l.on('add', this.onAddLayerHandler);\n    this.onRemoveLayerHandler = e => LeafletUtil.handleEvent(this.zone, this.onRemove, e);\n    l.on('remove', this.onRemoveLayerHandler);\n  }\n  removeLayerEventListeners(l) {\n    l.off('add', this.onAddLayerHandler);\n    l.off('remove', this.onRemoveLayerHandler);\n  }\n  static {\n    this.ɵfac = function LeafletLayerDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletLayerDirective)(i0.ɵɵdirectiveInject(LeafletDirective), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LeafletLayerDirective,\n      selectors: [[\"\", \"leafletLayer\", \"\"]],\n      inputs: {\n        layer: [0, \"leafletLayer\", \"layer\"]\n      },\n      outputs: {\n        onAdd: \"leafletLayerAdd\",\n        onRemove: \"leafletLayerRemove\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletLayerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[leafletLayer]',\n      standalone: false\n    }]\n  }], () => [{\n    type: LeafletDirective\n  }, {\n    type: i0.NgZone\n  }], {\n    layer: [{\n      type: Input,\n      args: ['leafletLayer']\n    }],\n    onAdd: [{\n      type: Output,\n      args: ['leafletLayerAdd']\n    }],\n    onRemove: [{\n      type: Output,\n      args: ['leafletLayerRemove']\n    }]\n  });\n})();\n\n/**\n * Layers directive\n *\n * This directive is used to directly control map layers. As changes are made to the input array of\n * layers, the map is synched to the array. As layers are added or removed from the input array, they\n * are also added or removed from the map. The input array is treated as immutable. To detect changes,\n * you must change the array instance.\n *\n * Important Note: The input layers array is assumed to be immutable. This means you need to use an\n * immutable array implementation or create a new copy of your array when you make changes, otherwise\n * this directive won't detect the change. This is by design. It's for performance reasons. Change\n * detection of mutable arrays requires diffing the state of the array on every DoCheck cycle, which\n * is extremely expensive from a time complexity perspective.\n *\n */\nclass LeafletLayersDirective {\n  // Set/get the layers\n  set layers(v) {\n    this.layersValue = v;\n    // Now that we have a differ, do an immediate layer update\n    this.updateLayers();\n  }\n  get layers() {\n    return this.layersValue;\n  }\n  constructor(leafletDirective, differs, zone) {\n    this.differs = differs;\n    this.zone = zone;\n    this.leafletDirective = new LeafletDirectiveWrapper(leafletDirective);\n    this.layersDiffer = this.differs.find([]).create();\n  }\n  ngDoCheck() {\n    this.updateLayers();\n  }\n  ngOnInit() {\n    // Init the map\n    this.leafletDirective.init();\n    // Update layers once the map is ready\n    this.updateLayers();\n  }\n  ngOnDestroy() {\n    this.layers = [];\n  }\n  /**\n   * Update the state of the layers.\n   * We use an iterable differ to synchronize the map layers with the state of the bound layers array.\n   * This is important because it allows us to react to changes to the contents of the array as well\n   * as changes to the actual array instance.\n   */\n  updateLayers() {\n    const map = this.leafletDirective.getMap();\n    if (null != map && null != this.layersDiffer) {\n      const changes = this.layersDiffer.diff(this.layersValue);\n      if (null != changes) {\n        // Run outside angular to ensure layer events don't trigger change detection\n        this.zone.runOutsideAngular(() => {\n          changes.forEachRemovedItem(c => {\n            map.removeLayer(c.item);\n          });\n          changes.forEachAddedItem(c => {\n            map.addLayer(c.item);\n          });\n        });\n      }\n    }\n  }\n  static {\n    this.ɵfac = function LeafletLayersDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletLayersDirective)(i0.ɵɵdirectiveInject(LeafletDirective), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LeafletLayersDirective,\n      selectors: [[\"\", \"leafletLayers\", \"\"]],\n      inputs: {\n        layers: [0, \"leafletLayers\", \"layers\"]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletLayersDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[leafletLayers]',\n      standalone: false\n    }]\n  }], () => [{\n    type: LeafletDirective\n  }, {\n    type: i0.IterableDiffers\n  }, {\n    type: i0.NgZone\n  }], {\n    layers: [{\n      type: Input,\n      args: ['leafletLayers']\n    }]\n  });\n})();\nclass LeafletControlLayersChanges {\n  constructor() {\n    this.layersRemoved = 0;\n    this.layersChanged = 0;\n    this.layersAdded = 0;\n  }\n  changed() {\n    return !(this.layersRemoved === 0 && this.layersChanged === 0 && this.layersAdded === 0);\n  }\n}\nclass LeafletControlLayersWrapper {\n  constructor(zone, layersControlReady) {\n    this.zone = zone;\n    this.layersControlReady = layersControlReady;\n  }\n  getLayersControl() {\n    return this.layersControl;\n  }\n  init(controlConfig, controlOptions) {\n    const baseLayers = controlConfig.baseLayers || {};\n    const overlays = controlConfig.overlays || {};\n    // Create the control outside of angular to ensure events don't trigger change detection\n    this.zone.runOutsideAngular(() => {\n      this.layersControl = control.layers(baseLayers, overlays, controlOptions);\n    });\n    this.layersControlReady.emit(this.layersControl);\n    return this.layersControl;\n  }\n  applyBaseLayerChanges(changes) {\n    let results = new LeafletControlLayersChanges();\n    if (null != this.layersControl) {\n      results = this.applyChanges(changes, this.layersControl.addBaseLayer);\n    }\n    return results;\n  }\n  applyOverlayChanges(changes) {\n    let results = new LeafletControlLayersChanges();\n    if (null != this.layersControl) {\n      results = this.applyChanges(changes, this.layersControl.addOverlay);\n    }\n    return results;\n  }\n  applyChanges(changes, addFn) {\n    const results = new LeafletControlLayersChanges();\n    if (null != changes) {\n      // All layer management is outside angular to avoid layer events from triggering change detection\n      this.zone.runOutsideAngular(() => {\n        changes.forEachChangedItem(c => {\n          this.layersControl.removeLayer(c.previousValue);\n          addFn.call(this.layersControl, c.currentValue, c.key);\n          results.layersChanged++;\n        });\n        changes.forEachRemovedItem(c => {\n          this.layersControl.removeLayer(c.previousValue);\n          results.layersRemoved++;\n        });\n        changes.forEachAddedItem(c => {\n          addFn.call(this.layersControl, c.currentValue, c.key);\n          results.layersAdded++;\n        });\n      });\n    }\n    return results;\n  }\n}\nclass LeafletControlLayersConfig {\n  constructor() {\n    this.baseLayers = {};\n    this.overlays = {};\n  }\n}\n\n/**\n * Layers Control\n *\n * This directive is used to configure the layers control. The input accepts an object with two\n * key-value maps of layer name -> layer. Mutable changes are detected. On changes, a differ is\n * used to determine what changed so that layers are appropriately added or removed.\n *\n * To specify which layer to show as the 'active' baselayer, you will want to add it to the map\n * using the layers directive. Otherwise, the last one it sees will be used.\n */\nclass LeafletLayersControlDirective {\n  set layersControlConfig(v) {\n    // Validation/init stuff\n    if (null == v) {\n      v = new LeafletControlLayersConfig();\n    }\n    if (null == v.baseLayers) {\n      v.baseLayers = {};\n    }\n    if (null == v.overlays) {\n      v.overlays = {};\n    }\n    // Store the value\n    this.layersControlConfigValue = v;\n    // Update the map\n    this.updateLayers();\n  }\n  get layersControlConfig() {\n    return this.layersControlConfigValue;\n  }\n  constructor(leafletDirective, differs, zone) {\n    this.differs = differs;\n    this.zone = zone;\n    this.layersControlReady = new EventEmitter();\n    this.leafletDirective = new LeafletDirectiveWrapper(leafletDirective);\n    this.controlLayers = new LeafletControlLayersWrapper(this.zone, this.layersControlReady);\n    // Generate differs\n    this.baseLayersDiffer = this.differs.find({}).create();\n    this.overlaysDiffer = this.differs.find({}).create();\n  }\n  ngOnInit() {\n    // Init the map\n    this.leafletDirective.init();\n    // Set up control outside of angular to avoid change detection when using the control\n    this.zone.runOutsideAngular(() => {\n      // Set up all the initial settings\n      this.controlLayers.init({}, this.layersControlOptions).addTo(this.leafletDirective.getMap());\n    });\n    this.updateLayers();\n  }\n  ngOnDestroy() {\n    this.layersControlConfig = {\n      baseLayers: {},\n      overlays: {}\n    };\n    this.controlLayers.getLayersControl().remove();\n  }\n  ngDoCheck() {\n    this.updateLayers();\n  }\n  updateLayers() {\n    const map = this.leafletDirective.getMap();\n    const layersControl = this.controlLayers.getLayersControl();\n    if (null != map && null != layersControl) {\n      // Run the baselayers differ\n      if (null != this.baseLayersDiffer && null != this.layersControlConfigValue.baseLayers) {\n        const changes = this.baseLayersDiffer.diff(this.layersControlConfigValue.baseLayers);\n        this.controlLayers.applyBaseLayerChanges(changes);\n      }\n      // Run the overlays differ\n      if (null != this.overlaysDiffer && null != this.layersControlConfigValue.overlays) {\n        const changes = this.overlaysDiffer.diff(this.layersControlConfigValue.overlays);\n        this.controlLayers.applyOverlayChanges(changes);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function LeafletLayersControlDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletLayersControlDirective)(i0.ɵɵdirectiveInject(LeafletDirective), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LeafletLayersControlDirective,\n      selectors: [[\"\", \"leafletLayersControl\", \"\"]],\n      inputs: {\n        layersControlConfig: [0, \"leafletLayersControl\", \"layersControlConfig\"],\n        layersControlOptions: [0, \"leafletLayersControlOptions\", \"layersControlOptions\"]\n      },\n      outputs: {\n        layersControlReady: \"leafletLayersControlReady\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletLayersControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[leafletLayersControl]',\n      standalone: false\n    }]\n  }], () => [{\n    type: LeafletDirective\n  }, {\n    type: i0.KeyValueDiffers\n  }, {\n    type: i0.NgZone\n  }], {\n    layersControlConfig: [{\n      type: Input,\n      args: ['leafletLayersControl']\n    }],\n    layersControlOptions: [{\n      type: Input,\n      args: ['leafletLayersControlOptions']\n    }],\n    layersControlReady: [{\n      type: Output,\n      args: ['leafletLayersControlReady']\n    }]\n  });\n})();\n\n/**\n * Baselayers directive\n *\n * This directive is provided as a convenient way to add baselayers to the map. The input accepts\n * a key-value map of layer name -> layer. Mutable changed are detected. On changes, a differ is\n * used to determine what changed so that layers are appropriately added or removed. This directive\n * will also add the layers control so users can switch between available base layers.\n *\n * To specify which layer to show as the 'active' baselayer, you will want to add it to the map\n * using the layers directive. Otherwise, the plugin will use the last one it sees.\n */\nclass LeafletBaseLayersDirective {\n  // Set/get baseLayers\n  set baseLayers(v) {\n    this.baseLayersValue = v;\n    this.updateBaseLayers();\n  }\n  get baseLayers() {\n    return this.baseLayersValue;\n  }\n  constructor(leafletDirective, differs, zone) {\n    this.differs = differs;\n    this.zone = zone;\n    // Output for once the layers control is ready\n    this.layersControlReady = new EventEmitter();\n    this.leafletDirective = new LeafletDirectiveWrapper(leafletDirective);\n    this.controlLayers = new LeafletControlLayersWrapper(this.zone, this.layersControlReady);\n    this.baseLayersDiffer = this.differs.find({}).create();\n  }\n  ngOnDestroy() {\n    this.baseLayers = {};\n    if (null != this.controlLayers.getLayersControl()) {\n      this.controlLayers.getLayersControl().remove();\n    }\n  }\n  ngOnInit() {\n    // Init the map\n    this.leafletDirective.init();\n    // Create the control outside angular to prevent events from triggering chnage detection\n    this.zone.runOutsideAngular(() => {\n      // Initially configure the controlLayers\n      this.controlLayers.init({}, this.layersControlOptions).addTo(this.leafletDirective.getMap());\n    });\n    this.updateBaseLayers();\n  }\n  ngDoCheck() {\n    this.updateBaseLayers();\n  }\n  updateBaseLayers() {\n    const map = this.leafletDirective.getMap();\n    const layersControl = this.controlLayers.getLayersControl();\n    if (null != map && null != layersControl && null != this.baseLayersDiffer) {\n      const changes = this.baseLayersDiffer.diff(this.baseLayersValue);\n      const results = this.controlLayers.applyBaseLayerChanges(changes);\n      if (results.changed()) {\n        this.syncBaseLayer();\n      }\n    }\n  }\n  /**\n   * Check the current base layer and change it to the new one if necessary\n   */\n  syncBaseLayer() {\n    const map = this.leafletDirective.getMap();\n    const layers = LeafletUtil.mapToArray(this.baseLayers);\n    let foundLayer;\n    // Search all the layers in the map to see if we can find them in the baselayer array\n    map.eachLayer(l => {\n      foundLayer = layers.find(bl => l === bl);\n    });\n    // Did we find the layer?\n    if (null != foundLayer) {\n      // Yes - set the baselayer to the one we found\n      this.baseLayer = foundLayer;\n    } else {\n      // No - set the baselayer to the first in the array and add it to the map\n      if (layers.length > 0) {\n        this.baseLayer = layers[0];\n        // Add layers outside of angular to prevent events from triggering change detection\n        this.zone.runOutsideAngular(() => {\n          this.baseLayer.addTo(map);\n        });\n      }\n    }\n  }\n  static {\n    this.ɵfac = function LeafletBaseLayersDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletBaseLayersDirective)(i0.ɵɵdirectiveInject(LeafletDirective), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: LeafletBaseLayersDirective,\n      selectors: [[\"\", \"leafletBaseLayers\", \"\"]],\n      inputs: {\n        baseLayers: [0, \"leafletBaseLayers\", \"baseLayers\"],\n        layersControlOptions: [0, \"leafletLayersControlOptions\", \"layersControlOptions\"]\n      },\n      outputs: {\n        layersControlReady: \"leafletLayersControlReady\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletBaseLayersDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[leafletBaseLayers]',\n      standalone: false\n    }]\n  }], () => [{\n    type: LeafletDirective\n  }, {\n    type: i0.KeyValueDiffers\n  }, {\n    type: i0.NgZone\n  }], {\n    baseLayers: [{\n      type: Input,\n      args: ['leafletBaseLayers']\n    }],\n    layersControlOptions: [{\n      type: Input,\n      args: ['leafletLayersControlOptions']\n    }],\n    layersControlReady: [{\n      type: Output,\n      args: ['leafletLayersControlReady']\n    }]\n  });\n})();\nclass LeafletModule {\n  static {\n    this.ɵfac = function LeafletModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LeafletModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LeafletModule,\n      declarations: [LeafletDirective, LeafletLayerDirective, LeafletLayersDirective, LeafletLayersControlDirective, LeafletBaseLayersDirective],\n      exports: [LeafletDirective, LeafletLayerDirective, LeafletLayersDirective, LeafletLayersControlDirective, LeafletBaseLayersDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LeafletModule, [{\n    type: NgModule,\n    args: [{\n      exports: [LeafletDirective, LeafletLayerDirective, LeafletLayersDirective, LeafletLayersControlDirective, LeafletBaseLayersDirective],\n      declarations: [LeafletDirective, LeafletLayerDirective, LeafletLayersDirective, LeafletLayersControlDirective, LeafletBaseLayersDirective]\n    }]\n  }], null, null);\n})();\nclass LeafletTileLayerDefinition {\n  constructor(type, url, options) {\n    this.type = type;\n    this.url = url;\n    this.options = options;\n  }\n  /**\n   * Creates a TileLayer from the provided definition. This is a convenience function\n   * to help with generating layers from objects.\n   *\n   * @param layerDef The layer to create\n   * @returns {TileLayer} The TileLayer that has been created\n   */\n  static createTileLayer(layerDef) {\n    let layer;\n    switch (layerDef.type) {\n      case 'xyz':\n        layer = tileLayer(layerDef.url, layerDef.options);\n        break;\n      case 'wms':\n      default:\n        layer = tileLayer.wms(layerDef.url, layerDef.options);\n        break;\n    }\n    return layer;\n  }\n  /**\n   * Creates a TileLayer for each key in the incoming map. This is a convenience function\n   * for generating an associative array of layers from an associative array of objects\n   *\n   * @param layerDefs A map of key to tile layer definition\n   * @returns {{[p: string]: TileLayer}} A new map of key to TileLayer\n   */\n  static createTileLayers(layerDefs) {\n    const layers = {};\n    for (const k in layerDefs) {\n      if (layerDefs.hasOwnProperty(k)) {\n        layers[k] = LeafletTileLayerDefinition.createTileLayer(layerDefs[k]);\n      }\n    }\n    return layers;\n  }\n  /**\n   * Create a Tile Layer from the current state of this object\n   *\n   * @returns {TileLayer} A new TileLayer\n   */\n  createTileLayer() {\n    return LeafletTileLayerDefinition.createTileLayer(this);\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LeafletBaseLayersDirective, LeafletControlLayersChanges, LeafletControlLayersConfig, LeafletControlLayersWrapper, LeafletDirective, LeafletDirectiveWrapper, LeafletLayerDirective, LeafletLayersControlDirective, LeafletLayersDirective, LeafletModule, LeafletTileLayerDefinition, LeafletUtil };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,qBAAgD;AAChD,IAAM,cAAN,MAAkB;AAAA,EAChB,OAAO,WAAWA,MAAK;AACrB,UAAM,WAAW,CAAC;AAClB,eAAW,KAAKA,MAAK;AACnB,UAAIA,KAAI,eAAe,CAAC,GAAG;AACzB,iBAAS,KAAKA,KAAI,CAAC,CAAC;AAAA,MACtB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,YAAY,MAAM,cAAc,OAAO;AAE5C,QAAI,IAAI,aAAa,UAAU,QAAQ;AACrC,WAAK,IAAI,MAAM;AACb,qBAAa,KAAK,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,SAAS,MAAM;AACzB,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,qBAAiB,uBAAO,WAAW,UAAU;AAClD,SAAK,sBAAsB,CAAC;AAC5B,SAAK,mBAAmB,KAAK;AAC7B,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc,KAAK;AACxB,SAAK,iBAAiB,KAAK;AAE3B,SAAK,UAAU,CAAC;AAEhB,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,eAAe,IAAI,aAAa;AAErC,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,cAAc,IAAI,aAAa;AACpC,SAAK,aAAa,IAAI,aAAa;AAEnC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,eAAe,IAAI,aAAa;AAErC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,eAAe,IAAI,aAAa;AAAA,EAEvC;AAAA,EACA,WAAW;AAET,SAAK,KAAK,kBAAkB,MAAM;AAEhC,WAAK,UAAM,oBAAI,KAAK,QAAQ,eAAe,KAAK,OAAO;AACvD,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAED,QAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,MAAM;AAC5C,WAAK,QAAQ,KAAK,QAAQ,KAAK,IAAI;AAAA,IACrC;AAEA,QAAI,QAAQ,KAAK,WAAW;AAC1B,WAAK,aAAa,KAAK,SAAS;AAAA,IAClC;AACA,QAAI,QAAQ,KAAK,WAAW;AAC1B,WAAK,aAAa,KAAK,SAAS;AAAA,IAClC;AACA,QAAI,QAAQ,KAAK,SAAS;AACxB,WAAK,WAAW,KAAK,OAAO;AAAA,IAC9B;AACA,QAAI,QAAQ,KAAK,SAAS;AACxB,WAAK,WAAW,KAAK,OAAO;AAAA,IAC9B;AACA,SAAK,SAAS;AAEd,SAAK,SAAS,KAAK,KAAK,GAAG;AAAA,EAC7B;AAAA,EACA,YAAY,SAAS;AAUnB,QAAI,QAAQ,MAAM,KAAK,QAAQ,QAAQ,KAAK,QAAQ,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AACpF,WAAK,QAAQ,QAAQ,QAAQ,EAAE,cAAc,QAAQ,MAAM,EAAE,YAAY;AAAA,IAC3E,WAES,QAAQ,MAAM,GAAG;AACxB,WAAK,QAAQ,QAAQ,MAAM,EAAE,YAAY;AAAA,IAC3C,WAES,QAAQ,QAAQ,GAAG;AAC1B,WAAK,UAAU,QAAQ,QAAQ,EAAE,YAAY;AAAA,IAC/C;AAEA,QAAI,QAAQ,WAAW,GAAG;AACxB,WAAK,aAAa,QAAQ,WAAW,EAAE,YAAY;AAAA,IACrD;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,WAAK,aAAa,QAAQ,WAAW,EAAE,YAAY;AAAA,IACrD;AACA,QAAI,QAAQ,SAAS,GAAG;AACtB,WAAK,WAAW,QAAQ,SAAS,EAAE,YAAY;AAAA,IACjD;AACA,QAAI,QAAQ,SAAS,GAAG;AACtB,WAAK,WAAW,QAAQ,SAAS,EAAE,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,cAAc;AAEZ,QAAI,QAAQ,KAAK,KAAK;AACpB,WAAK,IAAI,OAAO;AAAA,IAClB;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,uBAAuB;AACrB,UAAM,uBAAuB,CAAC,WAAW,YAAY;AACnD,WAAK,IAAI,GAAG,WAAW,OAAO;AAAA,IAChC;AAEA,yBAAqB,SAAS,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC;AACtF,yBAAqB,YAAY,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,eAAe,CAAC,CAAC;AAC/F,yBAAqB,aAAa,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,aAAa,CAAC,CAAC;AAC9F,yBAAqB,WAAW,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,WAAW,CAAC,CAAC;AAC1F,yBAAqB,aAAa,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,aAAa,CAAC,CAAC;AAC9F,yBAAqB,YAAY,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,YAAY,CAAC,CAAC;AAC5F,yBAAqB,aAAa,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,aAAa,CAAC,CAAC;AAC9F,yBAAqB,aAAa,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,gBAAgB,CAAC,CAAC;AACjG,yBAAqB,QAAQ,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,WAAW,CAAC,CAAC;AACvF,yBAAqB,WAAW,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,cAAc,CAAC,CAAC;AAC7F,yBAAqB,aAAa,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,gBAAgB,CAAC,CAAC;AACjG,yBAAqB,QAAQ,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,WAAW,CAAC,CAAC;AACvF,yBAAqB,WAAW,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,cAAc,CAAC,CAAC;AAE7F,UAAM,sBAAsB,MAAM;AAChC,YAAM,OAAO,KAAK,IAAI,QAAQ;AAC9B,UAAI,SAAS,KAAK,MAAM;AACtB,aAAK,OAAO;AACZ,oBAAY,YAAY,KAAK,MAAM,KAAK,YAAY,IAAI;AAAA,MAC1D;AACA,YAAM,SAAS,KAAK,IAAI,UAAU;AAClC,UAAI,QAAQ,UAAU,QAAQ,KAAK,QAAQ;AACzC,aAAK,QAAQ,UAAU,QAAQ,KAAK,WAAW,WAAW,KAAK,UAAU,OAAO,QAAQ,KAAK,OAAO,OAAO,OAAO,QAAQ,KAAK,OAAO,KAAK;AACzI,eAAK,SAAS;AACd,sBAAY,YAAY,KAAK,MAAM,KAAK,cAAc,MAAM;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AACA,yBAAqB,WAAW,mBAAmB;AACnD,yBAAqB,WAAW,mBAAmB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAET,SAAK,KAAK,kBAAkB,MAAM;AAEhC,UAAI,QAAQ,KAAK,KAAK;AACpB,aAAK,IAAI,eAAe,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,QAAQ,KAAK,aAAa;AAC5B,mBAAa,KAAK,WAAW;AAAA,IAC/B;AACA,SAAK,cAAc,WAAW,KAAK,SAAS,KAAK,IAAI,GAAG,GAAG;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,QAAQ,MAAM;AACpB,QAAI,QAAQ,KAAK,OAAO,QAAQ,UAAU,QAAQ,MAAM;AACtD,WAAK,IAAI,QAAQ,QAAQ,MAAM,KAAK,cAAc;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM;AACZ,QAAI,QAAQ,KAAK,OAAO,QAAQ,MAAM;AACpC,WAAK,IAAI,QAAQ,MAAM,KAAK,WAAW;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AAChB,QAAI,QAAQ,KAAK,OAAO,QAAQ,QAAQ;AACtC,WAAK,IAAI,MAAM,QAAQ,KAAK,UAAU;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,cAAc;AACzB,QAAI,QAAQ,KAAK,OAAO,QAAQ,cAAc;AAC5C,WAAK,IAAI,UAAU,cAAc,KAAK,gBAAgB;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,cAAc;AACzB,QAAI,QAAQ,KAAK,OAAO,QAAQ,cAAc;AAC5C,WAAK,IAAI,aAAa,YAAY;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACf,QAAI,QAAQ,KAAK,OAAO,QAAQ,MAAM;AACpC,WAAK,IAAI,WAAW,IAAI;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACf,QAAI,QAAQ,KAAK,OAAO,QAAQ,MAAM;AACpC,WAAK,IAAI,WAAW,IAAI;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,UAAU,SAAS,6CAA6C;AAC5E,mBAAO,IAAI,SAAS;AAAA,UACtB,GAAG,OAAU,eAAe;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB;AAAA,QACnE,YAAY,CAAC,GAAG,qBAAqB,YAAY;AAAA,QACjD,aAAa,CAAC,GAAG,sBAAsB,aAAa;AAAA,QACpD,gBAAgB,CAAC,GAAG,yBAAyB,gBAAgB;AAAA,QAC7D,SAAS,CAAC,GAAG,kBAAkB,SAAS;AAAA,QACxC,MAAM,CAAC,GAAG,eAAe,MAAM;AAAA,QAC/B,QAAQ,CAAC,GAAG,iBAAiB,QAAQ;AAAA,QACrC,WAAW,CAAC,GAAG,oBAAoB,WAAW;AAAA,QAC9C,WAAW,CAAC,GAAG,oBAAoB,WAAW;AAAA,QAC9C,SAAS,CAAC,GAAG,kBAAkB,SAAS;AAAA,QACxC,SAAS,CAAC,GAAG,kBAAkB,SAAS;AAAA,MAC1C;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,SAAS;AAAA,QACT,eAAe;AAAA,QACf,aAAa;AAAA,QACb,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,kBAAkB;AAC5B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AAAA,EAEP;AAAA,EACA,SAAS;AACP,WAAO,KAAK,iBAAiB,OAAO;AAAA,EACtC;AACF;AASA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,kBAAkB,MAAM;AAClC,SAAK,OAAO;AAEZ,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,WAAW,IAAI,aAAa;AACjC,SAAK,mBAAmB,IAAI,wBAAwB,gBAAgB;AAAA,EACtE;AAAA,EACA,WAAW;AAET,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA,EACA,cAAc;AACZ,QAAI,QAAQ,KAAK,OAAO;AAEtB,WAAK,0BAA0B,KAAK,KAAK;AAEzC,WAAK,MAAM,OAAO;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,OAAO,GAAG;AAEpB,YAAM,IAAI,QAAQ,OAAO,EAAE;AAC3B,YAAM,IAAI,QAAQ,OAAO,EAAE;AAC3B,WAAK,KAAK,kBAAkB,MAAM;AAChC,YAAI,QAAQ,GAAG;AACb,eAAK,0BAA0B,CAAC;AAChC,YAAE,OAAO;AAAA,QACX;AACA,YAAI,QAAQ,GAAG;AACb,eAAK,uBAAuB,CAAC;AAC7B,eAAK,iBAAiB,OAAO,EAAE,SAAS,CAAC;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,GAAG;AACxB,SAAK,oBAAoB,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,OAAO,CAAC;AAC9E,MAAE,GAAG,OAAO,KAAK,iBAAiB;AAClC,SAAK,uBAAuB,OAAK,YAAY,YAAY,KAAK,MAAM,KAAK,UAAU,CAAC;AACpF,MAAE,GAAG,UAAU,KAAK,oBAAoB;AAAA,EAC1C;AAAA,EACA,0BAA0B,GAAG;AAC3B,MAAE,IAAI,OAAO,KAAK,iBAAiB;AACnC,MAAE,IAAI,UAAU,KAAK,oBAAoB;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,kBAAkB,gBAAgB,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACjI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAAA,MACpC;AAAA,MACA,SAAS;AAAA,QACP,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAiBH,IAAM,yBAAN,MAAM,wBAAuB;AAAA;AAAA,EAE3B,IAAI,OAAO,GAAG;AACZ,SAAK,cAAc;AAEnB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,kBAAkB,SAAS,MAAM;AAC3C,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,mBAAmB,IAAI,wBAAwB,gBAAgB;AACpE,SAAK,eAAe,KAAK,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO;AAAA,EACnD;AAAA,EACA,YAAY;AACV,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AAET,SAAK,iBAAiB,KAAK;AAE3B,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,UAAMA,OAAM,KAAK,iBAAiB,OAAO;AACzC,QAAI,QAAQA,QAAO,QAAQ,KAAK,cAAc;AAC5C,YAAM,UAAU,KAAK,aAAa,KAAK,KAAK,WAAW;AACvD,UAAI,QAAQ,SAAS;AAEnB,aAAK,KAAK,kBAAkB,MAAM;AAChC,kBAAQ,mBAAmB,OAAK;AAC9B,YAAAA,KAAI,YAAY,EAAE,IAAI;AAAA,UACxB,CAAC;AACD,kBAAQ,iBAAiB,OAAK;AAC5B,YAAAA,KAAI,SAAS,EAAE,IAAI;AAAA,UACrB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAkB,gBAAgB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,MAAM,CAAC;AAAA,IAC5K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,QAAQ;AAAA,QACN,QAAQ,CAAC,GAAG,iBAAiB,QAAQ;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAkC;AAAA,EAChC,cAAc;AACZ,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,UAAU;AACR,WAAO,EAAE,KAAK,kBAAkB,KAAK,KAAK,kBAAkB,KAAK,KAAK,gBAAgB;AAAA,EACxF;AACF;AACA,IAAM,8BAAN,MAAkC;AAAA,EAChC,YAAY,MAAM,oBAAoB;AACpC,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK,eAAe,gBAAgB;AAClC,UAAM,aAAa,cAAc,cAAc,CAAC;AAChD,UAAM,WAAW,cAAc,YAAY,CAAC;AAE5C,SAAK,KAAK,kBAAkB,MAAM;AAChC,WAAK,gBAAgB,uBAAQ,OAAO,YAAY,UAAU,cAAc;AAAA,IAC1E,CAAC;AACD,SAAK,mBAAmB,KAAK,KAAK,aAAa;AAC/C,WAAO,KAAK;AAAA,EACd;AAAA,EACA,sBAAsB,SAAS;AAC7B,QAAI,UAAU,IAAI,4BAA4B;AAC9C,QAAI,QAAQ,KAAK,eAAe;AAC9B,gBAAU,KAAK,aAAa,SAAS,KAAK,cAAc,YAAY;AAAA,IACtE;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAAS;AAC3B,QAAI,UAAU,IAAI,4BAA4B;AAC9C,QAAI,QAAQ,KAAK,eAAe;AAC9B,gBAAU,KAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,SAAS,OAAO;AAC3B,UAAM,UAAU,IAAI,4BAA4B;AAChD,QAAI,QAAQ,SAAS;AAEnB,WAAK,KAAK,kBAAkB,MAAM;AAChC,gBAAQ,mBAAmB,OAAK;AAC9B,eAAK,cAAc,YAAY,EAAE,aAAa;AAC9C,gBAAM,KAAK,KAAK,eAAe,EAAE,cAAc,EAAE,GAAG;AACpD,kBAAQ;AAAA,QACV,CAAC;AACD,gBAAQ,mBAAmB,OAAK;AAC9B,eAAK,cAAc,YAAY,EAAE,aAAa;AAC9C,kBAAQ;AAAA,QACV,CAAC;AACD,gBAAQ,iBAAiB,OAAK;AAC5B,gBAAM,KAAK,KAAK,eAAe,EAAE,cAAc,EAAE,GAAG;AACpD,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,6BAAN,MAAiC;AAAA,EAC/B,cAAc;AACZ,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,CAAC;AAAA,EACnB;AACF;AAYA,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,IAAI,oBAAoB,GAAG;AAEzB,QAAI,QAAQ,GAAG;AACb,UAAI,IAAI,2BAA2B;AAAA,IACrC;AACA,QAAI,QAAQ,EAAE,YAAY;AACxB,QAAE,aAAa,CAAC;AAAA,IAClB;AACA,QAAI,QAAQ,EAAE,UAAU;AACtB,QAAE,WAAW,CAAC;AAAA,IAChB;AAEA,SAAK,2BAA2B;AAEhC,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,sBAAsB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,kBAAkB,SAAS,MAAM;AAC3C,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,mBAAmB,IAAI,wBAAwB,gBAAgB;AACpE,SAAK,gBAAgB,IAAI,4BAA4B,KAAK,MAAM,KAAK,kBAAkB;AAEvF,SAAK,mBAAmB,KAAK,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO;AACrD,SAAK,iBAAiB,KAAK,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO;AAAA,EACrD;AAAA,EACA,WAAW;AAET,SAAK,iBAAiB,KAAK;AAE3B,SAAK,KAAK,kBAAkB,MAAM;AAEhC,WAAK,cAAc,KAAK,CAAC,GAAG,KAAK,oBAAoB,EAAE,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAAA,IAC7F,CAAC;AACD,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB;AAAA,MACzB,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,IACb;AACA,SAAK,cAAc,iBAAiB,EAAE,OAAO;AAAA,EAC/C;AAAA,EACA,YAAY;AACV,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe;AACb,UAAMA,OAAM,KAAK,iBAAiB,OAAO;AACzC,UAAM,gBAAgB,KAAK,cAAc,iBAAiB;AAC1D,QAAI,QAAQA,QAAO,QAAQ,eAAe;AAExC,UAAI,QAAQ,KAAK,oBAAoB,QAAQ,KAAK,yBAAyB,YAAY;AACrF,cAAM,UAAU,KAAK,iBAAiB,KAAK,KAAK,yBAAyB,UAAU;AACnF,aAAK,cAAc,sBAAsB,OAAO;AAAA,MAClD;AAEA,UAAI,QAAQ,KAAK,kBAAkB,QAAQ,KAAK,yBAAyB,UAAU;AACjF,cAAM,UAAU,KAAK,eAAe,KAAK,KAAK,yBAAyB,QAAQ;AAC/E,aAAK,cAAc,oBAAoB,OAAO;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAAkC,kBAAkB,gBAAgB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACnL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,wBAAwB,EAAE,CAAC;AAAA,MAC5C,QAAQ;AAAA,QACN,qBAAqB,CAAC,GAAG,wBAAwB,qBAAqB;AAAA,QACtE,sBAAsB,CAAC,GAAG,+BAA+B,sBAAsB;AAAA,MACjF;AAAA,MACA,SAAS;AAAA,QACP,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAaH,IAAM,6BAAN,MAAM,4BAA2B;AAAA;AAAA,EAE/B,IAAI,WAAW,GAAG;AAChB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,kBAAkB,SAAS,MAAM;AAC3C,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,SAAK,qBAAqB,IAAI,aAAa;AAC3C,SAAK,mBAAmB,IAAI,wBAAwB,gBAAgB;AACpE,SAAK,gBAAgB,IAAI,4BAA4B,KAAK,MAAM,KAAK,kBAAkB;AACvF,SAAK,mBAAmB,KAAK,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO;AAAA,EACvD;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,CAAC;AACnB,QAAI,QAAQ,KAAK,cAAc,iBAAiB,GAAG;AACjD,WAAK,cAAc,iBAAiB,EAAE,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,WAAW;AAET,SAAK,iBAAiB,KAAK;AAE3B,SAAK,KAAK,kBAAkB,MAAM;AAEhC,WAAK,cAAc,KAAK,CAAC,GAAG,KAAK,oBAAoB,EAAE,MAAM,KAAK,iBAAiB,OAAO,CAAC;AAAA,IAC7F,CAAC;AACD,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,YAAY;AACV,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,mBAAmB;AACjB,UAAMA,OAAM,KAAK,iBAAiB,OAAO;AACzC,UAAM,gBAAgB,KAAK,cAAc,iBAAiB;AAC1D,QAAI,QAAQA,QAAO,QAAQ,iBAAiB,QAAQ,KAAK,kBAAkB;AACzE,YAAM,UAAU,KAAK,iBAAiB,KAAK,KAAK,eAAe;AAC/D,YAAM,UAAU,KAAK,cAAc,sBAAsB,OAAO;AAChE,UAAI,QAAQ,QAAQ,GAAG;AACrB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,UAAMA,OAAM,KAAK,iBAAiB,OAAO;AACzC,UAAM,SAAS,YAAY,WAAW,KAAK,UAAU;AACrD,QAAI;AAEJ,IAAAA,KAAI,UAAU,OAAK;AACjB,mBAAa,OAAO,KAAK,QAAM,MAAM,EAAE;AAAA,IACzC,CAAC;AAED,QAAI,QAAQ,YAAY;AAEtB,WAAK,YAAY;AAAA,IACnB,OAAO;AAEL,UAAI,OAAO,SAAS,GAAG;AACrB,aAAK,YAAY,OAAO,CAAC;AAEzB,aAAK,KAAK,kBAAkB,MAAM;AAChC,eAAK,UAAU,MAAMA,IAAG;AAAA,QAC1B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAkB,gBAAgB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,MAAM,CAAC;AAAA,IAChL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,QAAQ;AAAA,QACN,YAAY,CAAC,GAAG,qBAAqB,YAAY;AAAA,QACjD,sBAAsB,CAAC,GAAG,+BAA+B,sBAAsB;AAAA,MACjF;AAAA,MACA,SAAS;AAAA,QACP,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,kBAAkB,uBAAuB,wBAAwB,+BAA+B,0BAA0B;AAAA,MACzI,SAAS,CAAC,kBAAkB,uBAAuB,wBAAwB,+BAA+B,0BAA0B;AAAA,IACtI,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,uBAAuB,wBAAwB,+BAA+B,0BAA0B;AAAA,MACpI,cAAc,CAAC,kBAAkB,uBAAuB,wBAAwB,+BAA+B,0BAA0B;AAAA,IAC3I,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,MAAM,KAAK,SAAS;AAC9B,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,gBAAgB,UAAU;AAC/B,QAAI;AACJ,YAAQ,SAAS,MAAM;AAAA,MACrB,KAAK;AACH,oBAAQ,0BAAU,SAAS,KAAK,SAAS,OAAO;AAChD;AAAA,MACF,KAAK;AAAA,MACL;AACE,gBAAQ,yBAAU,IAAI,SAAS,KAAK,SAAS,OAAO;AACpD;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,iBAAiB,WAAW;AACjC,UAAM,SAAS,CAAC;AAChB,eAAW,KAAK,WAAW;AACzB,UAAI,UAAU,eAAe,CAAC,GAAG;AAC/B,eAAO,CAAC,IAAI,4BAA2B,gBAAgB,UAAU,CAAC,CAAC;AAAA,MACrE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,WAAO,4BAA2B,gBAAgB,IAAI;AAAA,EACxD;AACF;", "names": ["map"]}