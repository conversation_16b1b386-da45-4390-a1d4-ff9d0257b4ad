import { LocalizationModule, LocalizationService, PermissionDirective } from '@abp/ng.core';
import { SettingTabsService } from '@abp/ng.setting-management/config';
import { EmailSettingsService } from '@abp/ng.setting-management/proxy';
import { NgComponentOutlet } from '@angular/common';
import { Component, DestroyRef, inject, OnDestroy, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButton } from '@angular/material/button';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatListItem, MatNavList } from '@angular/material/list';
import {
  AlertService,
  fields,
  LOADING,
  model,
  requiredValidator,
  TtwrFormComponent,
} from '@ttwr-framework/ngx-main-visuals';
import { FeatureManagementDialogComponent } from '../feature-management-dialog/feature-management-dialog.component';
import { SettingManagementSendTestEmailDialogComponent } from './setting-management-send-test-email-dialog/setting-management-send-test-email-dialog.component';

@Component({
  selector: 'app-setting-management',
  standalone: true,
  imports: [
    LocalizationModule,
    MatNavList,
    MatCard,
    MatCardContent,
    MatListItem,
    TtwrFormComponent,
    NgComponentOutlet,
    PermissionDirective,
    MatButton,
    MatIcon,
  ],
  templateUrl: './setting-management.component.html',
  styleUrl: './setting-management.component.scss',
})
export class SettingManagementComponent implements OnDestroy {
  private settingsTabs = inject(SettingTabsService);
  private emailSettings = inject(EmailSettingsService);
  private dialog = inject(MatDialog);
  private localization = inject(LocalizationService);
  private destroyRef = inject(DestroyRef);
  private alert = inject(AlertService);
  private loading = inject(LOADING);

  private lastFieldsHiddenSignal = signal(true);

  protected activeSectionIndex = signal(0);
  protected tabs = toSignal(this.settingsTabs.visible$, { initialValue: [] });

  protected emailFormConfig = model({
    defaultFromDisplayName: fields.text(),
    defaultFromAddress: fields.text(),
    smtpHost: fields.text(),
    smtpPort: fields.number(),
    smtpEnableSsl: fields.boolean(),
    smtpUseDefaultCredentials: fields.boolean(),
    smtpDomain: fields.text(),
    smtpUserName: fields.text(),
    smtpPassword: fields.text(),
  }).form({
    submitAction: {
      onSubmit: value => {
        this.loading.set(true);

        this.emailSettings
          .update(value)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: () => {
              this.alert.success(
                this.localization.instant('AbpSettingManagement::SuccessfullySaved')
              );
            },
            error: err => {
              this.loading.set(false);
              const message = this.localization.instant(
                err.error?.error?.message || 'AbpAccount::DefaultErrorMessage'
              );
              this.alert.error(message);
            },
          });
      },
      submitLabel: 'Save',
      submitIcon: 'check',
    },
    viewFunc: () => this.emailSettings.get() as any,
    fields: {
      defaultFromDisplayName: {
        validators: [requiredValidator],
        label: this.localization.instant('AbpSettingManagement::DefaultFromDisplayName'),
      },
      defaultFromAddress: {
        validators: [requiredValidator],
        label: this.localization.instant('AbpSettingManagement::DefaultFromAddress'),
      },
      smtpHost: {
        label: this.localization.instant('AbpSettingManagement::SmtpHost'),
      },
      smtpPort: {
        validators: [requiredValidator],
        label: this.localization.instant('AbpSettingManagement::SmtpPort'),
      },
      smtpEnableSsl: {
        label: this.localization.instant('AbpSettingManagement::SmtpEnableSsl'),
      },
      smtpUseDefaultCredentials: {
        label: this.localization.instant('AbpSettingManagement::SmtpUseDefaultCredentials'),
        onChange: value => this.lastFieldsHiddenSignal.set(value ?? true),
      },
      smtpDomain: {
        label: this.localization.instant('AbpSettingManagement::SmtpDomain'),
        hiddenSignal: this.lastFieldsHiddenSignal,
      },
      smtpUserName: {
        label: this.localization.instant('AbpSettingManagement::SmtpUserName'),
        hiddenSignal: this.lastFieldsHiddenSignal,
      },
      smtpPassword: {
        label: this.localization.instant('AbpSettingManagement::SmtpPassword'),
        hiddenSignal: this.lastFieldsHiddenSignal,
      },
    },
    actions: [
      {
        label: this.localization.instant('AbpSettingManagement::SendTestEmail'),
        delegateFunc: ({ defaultFromAddress }) => {
          this.dialog.open(SettingManagementSendTestEmailDialogComponent, {
            width: '100%',
            maxWidth: '800px',
            data: defaultFromAddress,
          });
        },
      },
    ],
  });

  protected openFeaturesModel() {
    this.dialog.open(FeatureManagementDialogComponent, {
      maxWidth: '800px',
      width: '100%',
    });
  }

  ngOnDestroy() {
    this.loading.set(false);
  }
}
